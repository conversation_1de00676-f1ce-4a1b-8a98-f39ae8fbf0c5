# RGB 颜色格式完整指南

## 概述

RGB（Red, Green, Blue）是基于光的加色混合原理的颜色模型。它通过红、绿、蓝三个颜色通道的不同强度组合来表示颜色，是显示器和数字图像处理中最常用的颜色空间。

## 格式规范

### 基本格式
```css
rgb(red, green, blue)
```

### 带透明度格式
```css
rgba(red, green, blue, alpha)
```

### 参数说明
- **red**: 红色通道，范围 0-255
- **green**: 绿色通道，范围 0-255
- **blue**: 蓝色通道，范围 0-255
- **alpha**: 透明度，范围 0-1 (可选)

## 加色混合原理

### 光的三原色
RGB 基于光的加色混合原理：
- **红光 + 绿光 = 黄光**
- **红光 + 蓝光 = 品红光**
- **绿光 + 蓝光 = 青光**
- **红光 + 绿光 + 蓝光 = 白光**

### 颜色立方体
RGB 颜色空间可以表示为一个立方体：
- 原点 (0,0,0) = 黑色
- 对角点 (255,255,255) = 白色
- 三个轴分别代表红、绿、蓝通道

## 常见颜色示例

### 基础颜色
```css
rgb(255, 0, 0)    /* 纯红色 */
rgb(0, 255, 0)    /* 纯绿色 */
rgb(0, 0, 255)    /* 纯蓝色 */
rgb(255, 255, 0)  /* 黄色 */
rgb(255, 0, 255)  /* 品红色 */
rgb(0, 255, 255)  /* 青色 */
rgb(255, 255, 255) /* 白色 */
rgb(0, 0, 0)      /* 黑色 */
```

### 灰度颜色
```css
rgb(0, 0, 0)       /* 黑色 */
rgb(64, 64, 64)    /* 深灰 */
rgb(128, 128, 128) /* 中灰 */
rgb(192, 192, 192) /* 浅灰 */
rgb(255, 255, 255) /* 白色 */
```

### 透明度示例
```css
rgba(255, 0, 0, 1.0)   /* 完全不透明的红色 */
rgba(255, 0, 0, 0.8)   /* 80% 不透明度 */
rgba(255, 0, 0, 0.5)   /* 50% 透明度 */
rgba(255, 0, 0, 0.2)   /* 20% 不透明度 */
rgba(255, 0, 0, 0.0)   /* 完全透明 */
```

## 颜色通道分析

### 红色通道 (R)
控制红色光的强度：
- 0: 无红色
- 128: 中等红色
- 255: 最大红色

### 绿色通道 (G)
控制绿色光的强度：
- 0: 无绿色
- 128: 中等绿色
- 255: 最大绿色

### 蓝色通道 (B)
控制蓝色光的强度：
- 0: 无蓝色
- 128: 中等蓝色
- 255: 最大蓝色

## 转换方法

### RGB 转 HEX
```javascript
function rgbToHex(r, g, b) {
  return "#" + [r, g, b].map(x => {
    const hex = x.toString(16);
    return hex.length === 1 ? "0" + hex : hex;
  }).join("");
}

// 使用示例
rgbToHex(255, 107, 53); // "#FF6B35"
```

### RGB 转 HSL
```javascript
function rgbToHsl(r, g, b) {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h, s, l = (max + min) / 2;
  
  if (max === min) {
    h = s = 0; // 无色相
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }
  
  return [Math.round(h * 360), Math.round(s * 100), Math.round(l * 100)];
}
```

### 亮度计算
```javascript
function getLuminance(r, g, b) {
  // 使用 ITU-R BT.709 标准
  return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}
```

## 使用场景

### Web 开发
```css
/* CSS 中使用 RGB */
.header {
  background-color: rgb(59, 130, 246);
  color: rgb(255, 255, 255);
}

/* 使用透明度 */
.overlay {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 渐变效果 */
.gradient {
  background: linear-gradient(
    to right,
    rgb(255, 0, 0),
    rgb(0, 255, 0),
    rgb(0, 0, 255)
  );
}
```

### JavaScript 动态颜色
```javascript
// 动态生成颜色
function generateRandomColor() {
  const r = Math.floor(Math.random() * 256);
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  return `rgb(${r}, ${g}, ${b})`;
}

// 颜色插值
function interpolateColor(color1, color2, factor) {
  const [r1, g1, b1] = color1;
  const [r2, g2, b2] = color2;
  
  const r = Math.round(r1 + (r2 - r1) * factor);
  const g = Math.round(g1 + (g2 - g1) * factor);
  const b = Math.round(b1 + (b2 - b1) * factor);
  
  return [r, g, b];
}
```

## 设备相关性

### 显示器差异
RGB 是设备相关的颜色空间，同一个 RGB 值在不同设备上可能显示不同：
- **色域差异**: sRGB, Adobe RGB, DCI-P3
- **白点差异**: D50, D65
- **伽马值差异**: 1.8, 2.2, 2.4

### 色彩管理
```css
/* 指定色彩空间 */
.wide-gamut {
  color: color(display-p3 1 0 0); /* P3 红色 */
}

.standard {
  color: rgb(255, 0, 0); /* sRGB 红色 */
}
```

## 最佳实践

### ✅ 推荐做法
1. **使用 rgba() 添加透明度** - 而非叠加半透明元素
2. **考虑设备色域** - 为宽色域设备提供更丰富的颜色
3. **使用相对单位** - 便于响应式设计
4. **色彩管理** - 确保跨设备一致性

### ❌ 避免做法
1. **超出范围的值** - 如 rgb(300, -50, 256)
2. **忽略透明度** - 不考虑背景色的影响
3. **直接用于印刷** - RGB 不适合 CMYK 印刷
4. **忽略无障碍** - 不检查对比度

## 无障碍设计

### 对比度计算
```javascript
function getContrastRatio(rgb1, rgb2) {
  const l1 = getLuminance(...rgb1) / 255;
  const l2 = getLuminance(...rgb2) / 255;
  
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

// WCAG 标准检查
function checkWCAG(foreground, background) {
  const ratio = getContrastRatio(foreground, background);
  
  return {
    AA: ratio >= 4.5,
    AAA: ratio >= 7,
    ratio: ratio.toFixed(2)
  };
}
```

### 色盲友好设计
```css
/* 避免仅依赖红绿色区分 */
.error {
  color: rgb(220, 38, 38);
  border-left: 4px solid rgb(220, 38, 38);
}

.success {
  color: rgb(5, 150, 105);
  border-left: 4px solid rgb(5, 150, 105);
}
```

## 性能优化

### CSS 变量
```css
:root {
  --primary-rgb: 59, 130, 246;
  --success-rgb: 5, 150, 105;
  --error-rgb: 220, 38, 38;
}

.button-primary {
  background-color: rgb(var(--primary-rgb));
  box-shadow: 0 4px 6px rgba(var(--primary-rgb), 0.1);
}
```

### 预计算颜色
```javascript
// 预计算常用颜色变体
const colorPalette = {
  primary: {
    50: [239, 246, 255],
    100: [219, 234, 254],
    500: [59, 130, 246],
    900: [30, 58, 138]
  }
};
```

## 工具和资源

### 开发工具
- Chrome DevTools 颜色选择器
- VS Code 颜色预览插件
- Figma 颜色系统
- Adobe Color CC

### 在线工具
- [ColorCode.cc](/) - 专业颜色转换
- RGB Color Picker
- Contrast Checker
- Color Palette Generator

## 浏览器兼容性

| 功能 | Chrome | Firefox | Safari | Edge |
|------|--------|---------|--------|------|
| rgb() | 1+ | 1+ | 1+ | 12+ |
| rgba() | 1+ | 3+ | 3.1+ | 12+ |
| 百分比值 | 78+ | 52+ | 12.1+ | 79+ |

## 总结

RGB 颜色格式是数字显示的基础，理解其加色混合原理和设备相关性对于创建高质量的视觉体验至关重要。通过合理使用 RGB 和 RGBA，结合无障碍设计原则，可以创建既美观又实用的用户界面。

记住 RGB 最适合屏幕显示，如需印刷请考虑转换为 CMYK 色彩空间。
