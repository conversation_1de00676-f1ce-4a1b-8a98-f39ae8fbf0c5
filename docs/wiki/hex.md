# HEX 颜色格式完整指南

## 概述

HEX（十六进制）颜色代码是 Web 开发中最广泛使用的颜色表示方法。它使用 6 位十六进制数字来表示红、绿、蓝三个颜色通道的强度，提供了一种简洁而精确的颜色描述方式。

## 格式规范

### 基本格式
```
#RRGGBB
```

- **#** - 井号前缀，标识这是一个 HEX 颜色值
- **RR** - 红色通道，范围 00-FF (0-255)
- **GG** - 绿色通道，范围 00-FF (0-255)  
- **BB** - 蓝色通道，范围 00-FF (0-255)

### 简写格式
```
#RGB
```
当每个通道的两位数字相同时，可以使用 3 位简写：
- `#FF0000` 可以简写为 `#F00`
- `#00FF00` 可以简写为 `#0F0`
- `#0000FF` 可以简写为 `#00F`

## 十六进制数字系统

### 数字对应关系
| 十六进制 | 十进制 | 百分比 |
|----------|--------|--------|
| 00       | 0      | 0%     |
| 33       | 51     | 20%    |
| 66       | 102    | 40%    |
| 99       | 153    | 60%    |
| CC       | 204    | 80%    |
| FF       | 255    | 100%   |

### 常用值
- **00** - 最小值（无颜色）
- **80** - 中等值（50% 强度）
- **FF** - 最大值（全强度）

## 常见颜色示例

### 基础颜色
```css
#FF0000  /* 纯红色 */
#00FF00  /* 纯绿色 */
#0000FF  /* 纯蓝色 */
#FFFF00  /* 黄色 (红+绿) */
#FF00FF  /* 品红色 (红+蓝) */
#00FFFF  /* 青色 (绿+蓝) */
#FFFFFF  /* 白色 */
#000000  /* 黑色 */
```

### 灰度颜色
```css
#000000  /* 黑色 */
#333333  /* 深灰 */
#666666  /* 中灰 */
#999999  /* 浅灰 */
#CCCCCC  /* 很浅灰 */
#FFFFFF  /* 白色 */
```

### 流行的品牌色
```css
#1DA1F2  /* Twitter 蓝 */
#4267B2  /* Facebook 蓝 */
#FF0000  /* YouTube 红 */
#25D366  /* WhatsApp 绿 */
#E4405F  /* Instagram 粉 */
```

## 转换方法

### HEX 转 RGB
```javascript
function hexToRgb(hex) {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgb(${r}, ${g}, ${b})`;
}
```

### RGB 转 HEX
```javascript
function rgbToHex(r, g, b) {
  return "#" + [r, g, b].map(x => {
    const hex = x.toString(16);
    return hex.length === 1 ? "0" + hex : hex;
  }).join("");
}
```

## 使用场景

### Web 开发
- CSS 样式表中的颜色定义
- HTML 内联样式
- JavaScript 动态颜色操作
- SVG 图形颜色

### 设计工具
- Photoshop、Sketch、Figma 等设计软件
- 颜色选择器和调色板
- 设计系统和品牌指南

### 代码示例
```css
/* CSS 中使用 HEX 颜色 */
.primary-button {
  background-color: #3B82F6;
  color: #FFFFFF;
  border: 1px solid #2563EB;
}

.text-muted {
  color: #6B7280;
}
```

## 最佳实践

### ✅ 推荐做法
1. **使用完整的 6 位格式** - 避免歧义，提高可读性
2. **保持大写字母** - 统一代码风格
3. **定义 CSS 变量** - 便于维护和主题切换
4. **考虑对比度** - 确保文本可读性
5. **使用语义化命名** - 如 `--color-primary` 而非 `--blue`

### ❌ 避免做法
1. **混用大小写** - 如 `#Ff0000`
2. **忘记井号前缀** - 如 `FF0000`
3. **使用无效字符** - 如 `#GG0000`
4. **硬编码颜色值** - 直接在多处使用相同的 HEX 值

## 无障碍设计

### 对比度要求
- **AA 级别**: 对比度至少 4.5:1
- **AAA 级别**: 对比度至少 7:1
- **大文本**: 对比度至少 3:1

### 色盲友好
考虑红绿色盲用户，避免仅依赖颜色传达信息：
```css
/* 不好的做法 */
.error { color: #FF0000; }
.success { color: #00FF00; }

/* 更好的做法 */
.error { 
  color: #DC2626; 
  border-left: 4px solid #DC2626;
}
.success { 
  color: #059669; 
  border-left: 4px solid #059669;
}
```

## 工具和资源

### 在线工具
- [ColorCode.cc](/) - 专业颜色转换工具
- Adobe Color - 配色方案生成
- Coolors.co - 调色板生成器
- WebAIM Contrast Checker - 对比度检查

### 浏览器开发者工具
- Chrome DevTools 颜色选择器
- Firefox 颜色检查器
- Safari Web Inspector

## 浏览器兼容性

HEX 颜色格式在所有现代浏览器中都有完整支持：

| 浏览器 | 支持版本 | 备注 |
|--------|----------|------|
| Chrome | 所有版本 | 完全支持 |
| Firefox | 所有版本 | 完全支持 |
| Safari | 所有版本 | 完全支持 |
| Edge | 所有版本 | 完全支持 |
| IE | 3.0+ | 完全支持 |

## 高级技巧

### CSS 变量中使用 HEX
```css
:root {
  --primary-color: #3B82F6;
  --primary-hover: #2563EB;
  --primary-light: #DBEAFE;
}

.button {
  background-color: var(--primary-color);
}

.button:hover {
  background-color: var(--primary-hover);
}
```

### JavaScript 中的颜色操作
```javascript
// 颜色亮度调整
function adjustBrightness(hex, percent) {
  const num = parseInt(hex.replace("#", ""), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255))
    .toString(16).slice(1);
}
```

## 总结

HEX 颜色格式是 Web 开发的基础，掌握其使用方法对于前端开发者至关重要。通过理解十六进制数字系统、转换方法和最佳实践，可以更有效地使用 HEX 颜色创建美观且无障碍的用户界面。

记住始终考虑颜色的语义含义、对比度要求和用户体验，让颜色成为提升产品质量的有力工具。
