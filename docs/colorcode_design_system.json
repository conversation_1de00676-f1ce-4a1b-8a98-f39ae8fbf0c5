{"designSystem": {"name": "Developer Tools Platform", "version": "1.0", "theme": "modern_developer_focused", "colorPalette": {"primary": {"purple": "#6366f1", "purple_light": "#8b5cf6", "purple_dark": "#4f46e5"}, "secondary": {"green": "#10b981", "blue": "#3b82f6", "pink": "#ec4899", "orange": "#f59e0b"}, "neutral": {"white": "#ffffff", "gray_50": "#f9fafb", "gray_100": "#f3f4f6", "gray_200": "#e5e7eb", "gray_300": "#d1d5db", "gray_400": "#9ca3af", "gray_500": "#6b7280", "gray_600": "#4b5563", "gray_700": "#374151", "gray_800": "#1f2937", "gray_900": "#111827"}, "background": {"page": "#ffffff", "section_light": "#f9fafb", "section_dark": "#1f2937", "card": "#ffffff", "footer": "#111827"}, "semantic": {"success": "#10b981", "warning": "#f59e0b", "error": "#ef4444", "info": "#3b82f6"}}, "typography": {"fontFamily": {"primary": "Inter, system-ui, -apple-system, sans-serif", "secondary": "SF Pro Display, system-ui, sans-serif", "mono": "SF Mono, Monaco, 'Cascadia Code', monospace"}, "fontSizes": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem", "5xl": "3rem"}, "fontWeights": {"light": 300, "regular": 400, "medium": 500, "semibold": 600, "bold": 700, "extrabold": 800}, "lineHeight": {"tight": 1.25, "snug": 1.375, "normal": 1.5, "relaxed": 1.625, "loose": 2}}, "spacing": {"unit": "rem", "scale": {"px": "1px", "0": "0", "1": "0.25rem", "2": "0.5rem", "3": "0.75rem", "4": "1rem", "5": "1.25rem", "6": "1.5rem", "8": "2rem", "10": "2.5rem", "12": "3rem", "16": "4rem", "20": "5rem", "24": "6rem", "32": "8rem"}, "component": {"card_padding": "1.5rem", "section_padding": "4rem", "container_padding": "1rem", "button_padding": "0.75rem 1.5rem", "badge_padding": "0.25rem 0.75rem"}}, "layout": {"maxWidth": "1200px", "containerAlignment": "center", "gridSystem": {"columns": 12, "gutter": "1.5rem"}, "breakpoints": {"sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}}, "components": {"card": {"baseStyles": {"backgroundColor": "#ffffff", "borderRadius": "0.75rem", "boxShadow": "0 1px 3px rgba(0, 0, 0, 0.1)", "padding": "1.5rem", "border": "1px solid #e5e7eb", "transition": "all 0.2s ease-in-out"}, "variants": {"elevated": {"boxShadow": "0 4px 12px rgba(0, 0, 0, 0.1)"}, "outlined": {"boxShadow": "none", "border": "1px solid #d1d5db"}, "ghost": {"boxShadow": "none", "border": "none", "backgroundColor": "transparent"}}, "states": {"hover": {"transform": "translateY(-2px)", "boxShadow": "0 8px 24px rgba(0, 0, 0, 0.15)"}}}, "button": {"baseStyles": {"borderRadius": "0.5rem", "fontSize": "0.875rem", "fontWeight": 500, "padding": "0.75rem 1.5rem", "transition": "all 0.2s ease-in-out", "cursor": "pointer", "border": "none", "textDecoration": "none", "display": "inline-flex", "alignItems": "center", "justifyContent": "center", "gap": "0.5rem"}, "variants": {"primary": {"backgroundColor": "#6366f1", "color": "#ffffff"}, "secondary": {"backgroundColor": "#f3f4f6", "color": "#374151"}, "outline": {"backgroundColor": "transparent", "color": "#6366f1", "border": "1px solid #6366f1"}}, "sizes": {"sm": {"padding": "0.5rem 1rem", "fontSize": "0.75rem"}, "md": {"padding": "0.75rem 1.5rem", "fontSize": "0.875rem"}, "lg": {"padding": "1rem 2rem", "fontSize": "1rem"}}}, "badge": {"baseStyles": {"borderRadius": "0.375rem", "fontSize": "0.75rem", "fontWeight": 500, "padding": "0.25rem 0.75rem", "display": "inline-flex", "alignItems": "center", "gap": "0.25rem"}, "variants": {"success": {"backgroundColor": "#dcfce7", "color": "#166534"}, "info": {"backgroundColor": "#dbeafe", "color": "#1e40af"}, "warning": {"backgroundColor": "#fef3c7", "color": "#92400e"}, "neutral": {"backgroundColor": "#f3f4f6", "color": "#374151"}}}, "iconContainer": {"baseStyles": {"width": "3rem", "height": "3rem", "borderRadius": "0.75rem", "display": "flex", "alignItems": "center", "justifyContent": "center", "marginBottom": "1rem"}, "colorVariants": {"purple": {"backgroundColor": "#f3f4f6", "color": "#6366f1"}, "green": {"backgroundColor": "#dcfce7", "color": "#10b981"}, "blue": {"backgroundColor": "#dbeafe", "color": "#3b82f6"}, "pink": {"backgroundColor": "#fce7f3", "color": "#ec4899"}, "orange": {"backgroundColor": "#fef3c7", "color": "#f59e0b"}}}, "statsCard": {"baseStyles": {"textAlign": "center", "padding": "2rem", "backgroundColor": "#ffffff", "borderRadius": "0.75rem", "boxShadow": "0 1px 3px rgba(0, 0, 0, 0.1)"}, "numberStyles": {"fontSize": "2.25rem", "fontWeight": 700, "color": "#111827", "marginBottom": "0.5rem"}, "labelStyles": {"fontSize": "0.875rem", "fontWeight": 500, "color": "#6b7280", "marginBottom": "0.25rem"}, "descriptionStyles": {"fontSize": "0.75rem", "color": "#9ca3af"}}}, "effects": {"shadows": {"sm": "0 1px 2px rgba(0, 0, 0, 0.05)", "md": "0 1px 3px rgba(0, 0, 0, 0.1)", "lg": "0 4px 12px rgba(0, 0, 0, 0.1)", "xl": "0 8px 24px rgba(0, 0, 0, 0.15)", "2xl": "0 16px 48px rgba(0, 0, 0, 0.2)"}, "borderRadius": {"sm": "0.25rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1rem", "2xl": "1.5rem", "full": "9999px"}, "transitions": {"fast": "0.15s ease-in-out", "medium": "0.2s ease-in-out", "slow": "0.3s ease-in-out"}}, "visualHierarchy": {"principles": ["Clear sectional divisions with consistent spacing", "Card-based layout for tool organization", "Prominent call-to-action buttons", "Icon-driven visual communication", "Statistical emphasis through large numbers", "Subtle color coding for different tool categories"], "contentStructure": {"hero": {"alignment": "center", "spacing": "large", "typography": "heading_large", "badges": "horizontal_row"}, "toolGrid": {"layout": "responsive_grid", "columns": "3_column_desktop", "spacing": "medium", "cardStyle": "elevated"}, "statsSection": {"layout": "4_column_grid", "alignment": "center", "emphasis": "large_numbers", "background": "light"}, "footer": {"background": "dark", "layout": "multi_column", "color": "inverse"}}}, "accessibility": {"colorContrast": {"minimum": "4.5:1", "enhanced": "7:1"}, "focusIndicators": {"visible": true, "style": "outline", "color": "primary", "width": "2px"}, "semanticMarkup": true, "keyboardNavigation": true}, "responsiveDesign": {"approach": "mobile_first", "breakpointStrategy": "progressive_enhancement", "gridBehavior": {"mobile": "single_column", "tablet": "two_column", "desktop": "three_column"}, "navigationPattern": "responsive_header"}, "brandPersonality": {"tone": "professional_approachable", "characteristics": ["Developer-focused", "Clean and minimal", "Trustworthy", "Efficient", "Modern"], "visualStyle": "clean_functional_design"}}}