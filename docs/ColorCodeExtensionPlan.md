# ColorCode.cc 功能扩展技术方案 v2.0

> 基于 Vue 3 + Vite + 轻量级依赖的现代化扩展方案

## 📋 方案概述

本文档详细描述了 ColorCode.cc 两个核心功能模块的技术实现方案：**ColorWiki 知识库**和**专业颜色转换工具**。方案基于现有的 Vue 3 + Vite 技术栈，采用轻量级依赖策略，确保高性能和易维护性。

## 🎯 功能模块规划

### 模块一：ColorWiki 知识库
- **目标**：为 11 种颜色格式创建专业的 Wiki 页面
- **支持格式**：HEX、RGB、HSL、HSV、CMYK、OKLCH、LCH、XYZ、Display P3、Rec2020、颜色关键字
- **核心特性**：技术原理、历史背景、应用场景、转换关系、最佳实践

### 模块二：专业颜色转换工具
- **目标**：提供深度交互的颜色格式转换工具
- **核心功能**：交互式色轮、实时预览、精度显示、代码导出
- **转换对**：HEX↔RGB、RGB↔HSL、CMYK↔RGB、OKLCH↔HSL 等

## 🏗️ 技术架构设计

### 整体架构图

```mermaid
graph TD
    A[Vue 3 应用] --> B[Vue Router 4.4+]
    A --> C[Pinia 状态管理]
    A --> D[现有 ColorParser.js]
    
    B --> E[Wiki 路由模块]
    B --> F[转换工具路由模块]
    
    E --> G[Wiki 组件系统]
    F --> H[转换器组件系统]
    
    G --> I[Markdown 渲染器]
    H --> J[Canvas 色轮]
    H --> K[chroma-js 色彩引擎]
    
    I --> L[markdown-it + prismjs]
    J --> M[原生 Canvas API]
    K --> N[现有 chroma-js 库]
```

### 优化后的依赖策略

#### 核心依赖（轻量级方案）

```json
{
  "dependencies": {
    "chroma-js": "^3.1.2",           // 现有色彩库，无需更换
    "vue": "^3.5.17",                // Vue 3 最新稳定版
    "vue-router": "^4.4.5",          // 路由管理
    "@vueuse/core": "^11.2.0",       // Vue 组合式工具库
    "markdown-it": "^14.1.0",        // Markdown 渲染（替代 marked）
    "prismjs": "^1.29.0"             // 代码高亮（替代 highlight.js）
  },
  "devDependencies": {
    "vite-plugin-md": "^0.22.5"      // Vite Markdown 插件
  }
}
```

#### 依赖选择理由

| 依赖库 | 选择理由 | 替代方案对比 |
|--------|----------|-------------|
| **markdown-it** | 更现代化，插件生态丰富 | 替代 marked，体积相近但功能更强 |
| **prismjs** | 按需加载，体积更小 | 替代 highlight.js，减少 ~100KB |
| **@vueuse/core** | Vue 3 生态标准，提供实用工具 | 替代自定义 hooks，提升开发效率 |
| **现有 chroma-js** | 已集成，功能完备 | 避免引入 culori/d3-color，减少 ~250KB |

## 🔧 核心组件实现

### 1. ColorWiki 知识库架构

#### Wiki 布局组件

```vue
<!-- src/components/wiki/WikiLayout.vue -->
<template>
  <div class="wiki-layout">
    <!-- 侧边栏导航 -->
    <WikiSidebar 
      :formats="colorFormats" 
      :current="currentFormat" 
      @format-change="handleFormatChange"
    />
    
    <!-- 主内容区 -->
    <main class="wiki-content">
      <WikiHeader :format="currentFormat" />
      
      <!-- 动态 Wiki 内容组件 -->
      <Suspense>
        <template #default>
          <component 
            :is="currentWikiComponent" 
            :format="currentFormat"
            :color-examples="colorExamples"
          />
        </template>
        <template #fallback>
          <WikiSkeleton />
        </template>
      </Suspense>
      
      <!-- 相关格式推荐 -->
      <WikiRelated :current="currentFormat" :formats="relatedFormats" />
    </main>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useColorStore } from '@/stores/colorStore'

const route = useRoute()
const router = useRouter()
const colorStore = useColorStore()

const currentFormat = computed(() => route.params.format || 'hex')

// 动态导入 Wiki 组件
const currentWikiComponent = computed(() => {
  return defineAsyncComponent(() => 
    import(`./formats/${currentFormat.value}Wiki.vue`)
  )
})

// 支持的颜色格式
const colorFormats = [
  { id: 'hex', name: 'HEX', icon: '#', difficulty: 'beginner' },
  { id: 'rgb', name: 'RGB', icon: 'R', difficulty: 'beginner' },
  { id: 'hsl', name: 'HSL', icon: 'H', difficulty: 'intermediate' },
  { id: 'hsv', name: 'HSV', icon: 'V', difficulty: 'intermediate' },
  { id: 'cmyk', name: 'CMYK', icon: 'C', difficulty: 'advanced' },
  { id: 'oklch', name: 'OKLCH', icon: 'O', difficulty: 'expert' },
  { id: 'lch', name: 'LCH', icon: 'L', difficulty: 'expert' },
  { id: 'xyz', name: 'XYZ', icon: 'X', difficulty: 'expert' },
  { id: 'p3', name: 'Display P3', icon: 'P', difficulty: 'expert' },
  { id: 'rec2020', name: 'Rec2020', icon: '2', difficulty: 'expert' },
  { id: 'keywords', name: 'Keywords', icon: 'K', difficulty: 'beginner' }
]

// 相关格式推荐逻辑
const relatedFormats = computed(() => {
  const current = currentFormat.value
  const relations = {
    'hex': ['rgb', 'hsl'],
    'rgb': ['hex', 'hsl', 'hsv'],
    'hsl': ['rgb', 'hsv', 'oklch'],
    'oklch': ['hsl', 'lch', 'p3'],
    'cmyk': ['rgb', 'hex']
  }
  return relations[current] || []
})

const handleFormatChange = (format) => {
  router.push(`/wiki/${format}`)
}
</script>
```

#### 单个格式 Wiki 组件示例

```vue
<!-- src/components/wiki/formats/hexWiki.vue -->
<template>
  <article class="format-wiki">
    <!-- 格式概览 -->
    <section class="wiki-overview">
      <div class="format-demo">
        <ColorPreview :color="demoColor" />
        <code class="format-example">#FF6B35</code>
      </div>
      
      <div class="format-info">
        <h2>HEX 颜色格式</h2>
        <p class="format-description">
          十六进制颜色代码是 Web 开发中最常用的颜色表示方法，
          使用 # 符号后跟 6 位十六进制数字表示 RGB 颜色值。
        </p>
        
        <div class="format-specs">
          <span class="spec-item">格式：#RRGGBB</span>
          <span class="spec-item">范围：#000000 - #FFFFFF</span>
          <span class="spec-item">精度：8-bit per channel</span>
        </div>
      </div>
    </section>

    <!-- Markdown 内容渲染 -->
    <section class="wiki-content" v-html="renderedContent"></section>
    
    <!-- 交互式示例 -->
    <section class="interactive-examples">
      <h3>交互式示例</h3>
      <HexColorPicker 
        v-model="demoColor"
        @color-change="handleColorChange"
      />
    </section>
    
    <!-- 转换工具快速入口 -->
    <section class="quick-converter">
      <h3>快速转换</h3>
      <QuickConverter 
        :source-format="'hex'"
        :target-formats="['rgb', 'hsl', 'hsv']"
        :source-color="demoColor"
      />
    </section>
  </article>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMarkdown } from '@/composables/useMarkdown'
import ColorPreview from '@/components/common/ColorPreview.vue'
import HexColorPicker from '@/components/pickers/HexColorPicker.vue'
import QuickConverter from '@/components/converter/QuickConverter.vue'

const props = defineProps({
  format: String,
  colorExamples: Array
})

const demoColor = ref('#FF6B35')
const { renderMarkdown } = useMarkdown()

// 加载 Markdown 内容
const markdownContent = ref('')
const renderedContent = computed(() => 
  renderMarkdown(markdownContent.value)
)

onMounted(async () => {
  // 动态加载 Markdown 文档
  try {
    const response = await fetch('/docs/wiki/hex.md')
    markdownContent.value = await response.text()
  } catch (error) {
    console.error('Failed to load wiki content:', error)
  }
})

const handleColorChange = (newColor) => {
  demoColor.value = newColor
}
</script>
```

### 2. 专业颜色转换工具架构

#### 转换器中心组件

```vue
<!-- src/components/converter/ConverterHub.vue -->
<template>
  <div class="converter-hub">
    <!-- 转换器选择器 -->
    <ConverterSelector 
      :converters="availableConverters" 
      :current="currentConverter"
      @select="selectConverter" 
    />
    
    <!-- 当前转换器 -->
    <div class="converter-container">
      <Suspense>
        <template #default>
          <component 
            :is="currentConverterComponent" 
            :key="converterKey"
            @color-change="handleColorChange"
            @conversion-complete="handleConversionComplete"
          />
        </template>
        <template #fallback>
          <ConverterSkeleton />
        </template>
      </Suspense>
    </div>
    
    <!-- 转换历史 -->
    <ConversionHistory 
      :history="conversionHistory"
      @restore="restoreFromHistory"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useColorStore } from '@/stores/colorStore'
import ColorParser from '@/scripts/ColorParser.js'

const route = useRoute()
const router = useRouter()
const colorStore = useColorStore()

const currentConverter = ref(route.params.type || 'hex-rgb')
const converterKey = ref(0)
const conversionHistory = ref([])

// 可用的转换器配置
const availableConverters = [
  { 
    id: 'hex-rgb', 
    name: 'HEX ↔ RGB', 
    component: 'HexRgbConverter',
    description: '十六进制与RGB格式互转',
    difficulty: 'beginner',
    popular: true
  },
  { 
    id: 'rgb-hsl', 
    name: 'RGB ↔ HSL', 
    component: 'RgbHslConverter',
    description: 'RGB与HSL色彩空间转换',
    difficulty: 'intermediate',
    popular: true
  },
  { 
    id: 'cmyk-rgb', 
    name: 'CMYK ↔ RGB', 
    component: 'CmykRgbConverter',
    description: '印刷CMYK与显示RGB转换',
    difficulty: 'advanced',
    popular: false
  },
  { 
    id: 'oklch-hsl', 
    name: 'OKLCH ↔ HSL', 
    component: 'OklchHslConverter',
    description: '现代OKLCH与传统HSL转换',
    difficulty: 'expert',
    popular: false
  }
]

// 动态加载转换器组件
const currentConverterComponent = computed(() => {
  const converter = availableConverters.find(c => c.id === currentConverter.value)
  return defineAsyncComponent(() => 
    import(`./converters/${converter?.component}.vue`)
  )
})

const selectConverter = (converterId) => {
  currentConverter.value = converterId
  converterKey.value++
  router.push(`/converter/${converterId}`)
}

const handleColorChange = (colorData) => {
  // 使用现有的 ColorParser 进行验证
  const parsed = ColorParser.parseEnhanced(colorData.input, {
    enableCache: true,
    enableSuggestions: true,
    enableFuzzyMatch: true
  })
  
  if (parsed.mode !== 'unknown') {
    colorStore.parseColor(colorData.input)
  }
}

const handleConversionComplete = (conversionData) => {
  // 记录转换历史
  conversionHistory.value.unshift({
    id: Date.now(),
    timestamp: new Date(),
    converter: currentConverter.value,
    source: conversionData.source,
    target: conversionData.target,
    deltaE: conversionData.deltaE || null
  })
  
  // 限制历史记录数量
  if (conversionHistory.value.length > 50) {
    conversionHistory.value = conversionHistory.value.slice(0, 50)
  }
}

// 监听路由变化
watch(() => route.params.type, (newType) => {
  if (newType && newType !== currentConverter.value) {
    currentConverter.value = newType
    converterKey.value++
  }
})
</script>
```

#### 具体转换器组件示例

```vue
<!-- src/components/converter/converters/HexRgbConverter.vue -->
<template>
  <div class="hex-rgb-converter">
    <!-- 转换器标题 -->
    <header class="converter-header">
      <h2>HEX ↔ RGB 转换器</h2>
      <p>十六进制颜色代码与RGB数值的双向转换</p>
    </header>
    
    <!-- 双向转换界面 -->
    <div class="conversion-panel">
      <!-- HEX 输入区 -->
      <div class="input-section">
        <label for="hex-input">HEX 颜色代码</label>
        <div class="input-group">
          <input
            id="hex-input"
            v-model="hexValue"
            type="text"
            placeholder="#FF6B35"
            class="color-input"
            @input="handleHexInput"
            @blur="validateHexInput"
          />
          <ColorSwatch :color="hexValue" />
        </div>
        <div v-if="hexError" class="error-message">{{ hexError }}</div>
      </div>
      
      <!-- 转换箭头 -->
      <div class="conversion-arrow">
        <button 
          @click="swapValues"
          class="swap-button"
          title="交换数值"
        >
          ⇄
        </button>
      </div>
      
      <!-- RGB 输入区 -->
      <div class="input-section">
        <label>RGB 数值</label>
        <div class="rgb-inputs">
          <div class="rgb-input-group">
            <label for="r-input">R</label>
            <input
              id="r-input"
              v-model.number="rgbValue.r"
              type="number"
              min="0"
              max="255"
              @input="handleRgbInput"
            />
          </div>
          <div class="rgb-input-group">
            <label for="g-input">G</label>
            <input
              id="g-input"
              v-model.number="rgbValue.g"
              type="number"
              min="0"
              max="255"
              @input="handleRgbInput"
            />
          </div>
          <div class="rgb-input-group">
            <label for="b-input">B</label>
            <input
              id="b-input"
              v-model.number="rgbValue.b"
              type="number"
              min="0"
              max="255"
              @input="handleRgbInput"
            />
          </div>
        </div>
        <div class="rgb-preview">
          <ColorSwatch :color="rgbCssValue" />
          <code>{{ rgbCssValue }}</code>
        </div>
      </div>
    </div>
    
    <!-- 交互式色轮 -->
    <section class="color-wheel-section">
      <h3>交互式选择</h3>
      <ColorWheel 
        v-model="currentColor"
        :size="280"
        @color-change="handleWheelColorChange"
      />
    </section>
    
    <!-- 精度信息 -->
    <section class="precision-info">
      <h3>转换精度</h3>
      <div class="precision-details">
        <div class="precision-item">
          <span class="label">色差 (ΔE):</span>
          <span class="value">{{ deltaE.toFixed(3) }}</span>
        </div>
        <div class="precision-item">
          <span class="label">精度等级:</span>
          <span class="value" :class="precisionClass">{{ precisionLevel }}</span>
        </div>
      </div>
    </section>
    
    <!-- 代码导出 -->
    <section class="code-export">
      <h3>代码导出</h3>
      <CodeExporter 
        :color="currentColor"
        :formats="['css', 'scss', 'tailwind', 'js']"
      />
    </section>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import chroma from 'chroma-js'
import ColorWheel from '@/components/common/ColorWheel.vue'
import ColorSwatch from '@/components/common/ColorSwatch.vue'
import CodeExporter from '@/components/common/CodeExporter.vue'

const emit = defineEmits(['color-change', 'conversion-complete'])
const colorStore = useColorStore()

// 响应式数据
const hexValue = ref('#FF6B35')
const rgbValue = ref({ r: 255, g: 107, b: 53 })
const hexError = ref('')
const currentColor = ref('#FF6B35')

// 计算属性
const rgbCssValue = computed(() => 
  `rgb(${rgbValue.value.r}, ${rgbValue.value.g}, ${rgbValue.value.b})`
)

const deltaE = computed(() => {
  try {
    const hexColor = chroma(hexValue.value)
    const rgbColor = chroma(rgbCssValue.value)
    return colorStore.calculateDeltaE(hexColor, rgbColor)
  } catch {
    return 0
  }
})

const precisionLevel = computed(() => {
  if (deltaE.value < 1) return '完美'
  if (deltaE.value < 2) return '优秀'
  if (deltaE.value < 3) return '良好'
  return '一般'
})

const precisionClass = computed(() => ({
  'precision-perfect': deltaE.value < 1,
  'precision-excellent': deltaE.value >= 1 && deltaE.value < 2,
  'precision-good': deltaE.value >= 2 && deltaE.value < 3,
  'precision-fair': deltaE.value >= 3
}))

// 事件处理
const handleHexInput = () => {
  hexError.value = ''
  try {
    const color = chroma(hexValue.value)
    const rgb = color.rgb()
    rgbValue.value = { r: rgb[0], g: rgb[1], b: rgb[2] }
    currentColor.value = hexValue.value
    
    emit('color-change', { input: hexValue.value, format: 'hex' })
  } catch (error) {
    hexError.value = '无效的 HEX 颜色代码'
  }
}

const handleRgbInput = () => {
  try {
    const color = chroma(rgbValue.value.r, rgbValue.value.g, rgbValue.value.b)
    hexValue.value = color.hex()
    currentColor.value = hexValue.value
    
    emit('color-change', { input: rgbCssValue.value, format: 'rgb' })
  } catch (error) {
    console.warn('Invalid RGB values')
  }
}

const handleWheelColorChange = (color) => {
  hexValue.value = color
  const chromaColor = chroma(color)
  const rgb = chromaColor.rgb()
  rgbValue.value = { r: rgb[0], g: rgb[1], b: rgb[2] }
  
  emit('conversion-complete', {
    source: { format: 'wheel', value: color },
    target: { hex: hexValue.value, rgb: rgbCssValue.value },
    deltaE: 0 // 色轮选择无转换误差
  })
}

const swapValues = () => {
  // 交换逻辑可以根据需要实现
  console.log('Swap values')
}

const validateHexInput = () => {
  if (!hexValue.value.startsWith('#')) {
    hexValue.value = '#' + hexValue.value
  }
  handleHexInput()
}

// 监听颜色变化
watch(currentColor, (newColor) => {
  emit('conversion-complete', {
    source: { format: 'hex', value: hexValue.value },
    target: { format: 'rgb', value: rgbCssValue.value },
    deltaE: deltaE.value
  })
})
</script>
```

## 🎨 轻量级色轮实现

### Canvas 原生色轮组件

```vue
<!-- src/components/common/ColorWheel.vue -->
<template>
  <div class="color-wheel-container">
    <canvas 
      ref="wheelCanvas"
      :width="size"
      :height="size"
      @click="handleWheelClick"
      @mousemove="handleWheelMove"
      @mousedown="startDragging"
      @mouseup="stopDragging"
      class="color-wheel"
    />
    
    <!-- 色轮中心的亮度滑块 -->
    <div class="lightness-slider">
      <input
        v-model="lightness"
        type="range"
        min="0"
        max="100"
        @input="updateColor"
        class="slider"
      />
      <label>亮度: {{ lightness }}%</label>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import chroma from 'chroma-js'

const props = defineProps({
  size: { type: Number, default: 300 },
  modelValue: { type: String, default: '#ff0000' }
})

const emit = defineEmits(['update:modelValue', 'color-change'])

const wheelCanvas = ref(null)
const isDragging = ref(false)
const lightness = ref(50)
const currentHue = ref(0)
const currentSaturation = ref(100)

// 绘制 HSL 色轮
const drawColorWheel = () => {
  const canvas = wheelCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  const centerX = props.size / 2
  const centerY = props.size / 2
  const radius = props.size / 2 - 20

  // 清空画布
  ctx.clearRect(0, 0, props.size, props.size)

  // 绘制色轮
  for (let angle = 0; angle < 360; angle += 1) {
    for (let r = 0; r < radius; r += 1) {
      const hue = angle
      const saturation = (r / radius) * 100
      const l = lightness.value
      
      try {
        const color = chroma.hsl(hue, saturation / 100, l / 100)
        ctx.fillStyle = color.hex()
        
        const x = centerX + r * Math.cos((angle * Math.PI) / 180)
        const y = centerY + r * Math.sin((angle * Math.PI) / 180)
        
        ctx.fillRect(Math.round(x), Math.round(y), 2, 2)
      } catch (error) {
        // 忽略无效颜色
      }
    }
  }

  // 绘制当前选择的位置指示器
  drawSelector(ctx, centerX, centerY, radius)
}

const drawSelector = (ctx, centerX, centerY, radius) => {
  const angle = (currentHue.value * Math.PI) / 180
  const distance = (currentSaturation.value / 100) * radius
  
  const x = centerX + distance * Math.cos(angle)
  const y = centerY + distance * Math.sin(angle)
  
  // 绘制选择器圆圈
  ctx.beginPath()
  ctx.arc(x, y, 8, 0, 2 * Math.PI)
  ctx.strokeStyle = '#ffffff'
  ctx.lineWidth = 3
  ctx.stroke()
  
  ctx.beginPath()
  ctx.arc(x, y, 8, 0, 2 * Math.PI)
  ctx.strokeStyle = '#000000'
  ctx.lineWidth = 1
  ctx.stroke()
}

const handleWheelClick = (event) => {
  updateColorFromEvent(event)
}

const handleWheelMove = (event) => {
  if (isDragging.value) {
    updateColorFromEvent(event)
  }
}

const updateColorFromEvent = (event) => {
  const canvas = wheelCanvas.value
  const rect = canvas.getBoundingClientRect()
  const centerX = props.size / 2
  const centerY = props.size / 2
  const radius = props.size / 2 - 20
  
  const x = event.clientX - rect.left - centerX
  const y = event.clientY - rect.top - centerY
  
  const distance = Math.sqrt(x * x + y * y)
  if (distance > radius) return // 超出色轮范围
  
  const angle = Math.atan2(y, x) * (180 / Math.PI)
  const hue = angle < 0 ? angle + 360 : angle
  const saturation = Math.min((distance / radius) * 100, 100)
  
  currentHue.value = hue
  currentSaturation.value = saturation
  
  updateColor()
}

const updateColor = () => {
  try {
    const color = chroma.hsl(
      currentHue.value, 
      currentSaturation.value / 100, 
      lightness.value / 100
    )
    const hexColor = color.hex()
    
    emit('update:modelValue', hexColor)
    emit('color-change', hexColor)
    
    // 重绘色轮以更新选择器位置
    nextTick(() => {
      drawColorWheel()
    })
  } catch (error) {
    console.warn('Invalid color values:', error)
  }
}

const startDragging = () => {
  isDragging.value = true
}

const stopDragging = () => {
  isDragging.value = false
}

// 监听外部颜色变化
watch(() => props.modelValue, (newColor) => {
  try {
    const color = chroma(newColor)
    const hsl = color.hsl()
    
    currentHue.value = hsl[0] || 0
    currentSaturation.value = (hsl[1] || 0) * 100
    lightness.value = (hsl[2] || 0.5) * 100
    
    nextTick(() => {
      drawColorWheel()
    })
  } catch (error) {
    console.warn('Invalid color prop:', newColor)
  }
}, { immediate: true })

// 监听亮度变化
watch(lightness, () => {
  drawColorWheel()
  updateColor()
})

onMounted(() => {
  drawColorWheel()
})
</script>

<style scoped>
.color-wheel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.color-wheel {
  border-radius: 50%;
  cursor: crosshair;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.lightness-slider {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.slider {
  width: 200px;
  height: 6px;
  border-radius: 3px;
  background: linear-gradient(to right, #000, #fff);
  outline: none;
  cursor: pointer;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #333;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
```

## 🛠️ 工具函数和组合式API

### Markdown 渲染组合式函数

```js
// src/composables/useMarkdown.js
import { ref } from 'vue'
import MarkdownIt from 'markdown-it'
import Prism from 'prismjs'

// 按需导入语言支持
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/components/prism-scss'

const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && Prism.languages[lang]) {
      try {
        return `<pre class="language-${lang}"><code class="language-${lang}">${
          Prism.highlight(str, Prism.languages[lang], lang)
        }</code></pre>`
      } catch (error) {
        console.warn('Syntax highlighting failed:', error)
      }
    }
    return `<pre><code>${md.utils.escapeHtml(str)}</code></pre>`
  }
})

export function useMarkdown() {
  const isLoading = ref(false)
  const error = ref(null)

  const renderMarkdown = (content) => {
    try {
      return md.render(content)
    } catch (err) {
      error.value = err
      console.error('Markdown rendering failed:', err)
      return `<p>渲染失败: ${err.message}</p>`
    }
  }

  const loadMarkdownFile = async (path) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch(path)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const content = await response.text()
      return renderMarkdown(content)
    } catch (err) {
      error.value = err
      console.error('Failed to load markdown file:', err)
      return `<p>加载失败: ${err.message}</p>`
    } finally {
      isLoading.value = false
    }
  }

  return {
    renderMarkdown,
    loadMarkdownFile,
    isLoading,
    error
  }
}
```

### 扩展的颜色工具函数

```js
// src/utils/colorUtils.js
import chroma from 'chroma-js'

export class ColorUtils {
  /**
   * 计算两个颜色之间的色差 (Delta E)
   * 使用 CIEDE2000 算法
   */
  static calculateDeltaE(color1, color2) {
    try {
      const lab1 = chroma(color1).lab()
      const lab2 = chroma(color2).lab()
      return chroma.deltaE(lab1, lab2)
    } catch (error) {
      console.warn('Delta E calculation failed:', error)
      return 0
    }
  }

  /**
   * 生成颜色阶梯 (类似 Tailwind CSS)
   * @param {string} baseColor - 基础颜色
   * @param {number} steps - 阶梯数量 (默认 9)
   * @returns {Array} 颜色阶梯数组
   */
  static generateColorScale(baseColor, steps = 9) {
    try {
      const base = chroma(baseColor)
      const hsl = base.hsl()
      
      // 生成从浅到深的色阶
      const scale = []
      for (let i = 0; i < steps; i++) {
        const lightness = 0.95 - (i / (steps - 1)) * 0.85
        const color = chroma.hsl(hsl[0], hsl[1], lightness)
        scale.push({
          level: (i + 1) * 100,
          hex: color.hex(),
          rgb: color.css('rgb'),
          hsl: color.css('hsl')
        })
      }
      
      return scale
    } catch (error) {
      console.warn('Color scale generation failed:', error)
      return []
    }
  }

  /**
   * 转换为 OKLCH 格式
   * @param {string} color - 输入颜色
   * @returns {string|null} OKLCH 字符串
   */
  static toOklch(color) {
    try {
      const chromaColor = chroma(color)
      const oklch = chromaColor.oklch()
      return `oklch(${oklch[0].toFixed(3)} ${oklch[1].toFixed(3)} ${oklch[2].toFixed(1)})`
    } catch (error) {
      console.warn('OKLCH conversion failed:', error)
      return null
    }
  }

  /**
   * 检查颜色对比度是否符合 WCAG 标准
   * @param {string} foreground - 前景色
   * @param {string} background - 背景色
   * @returns {Object} 对比度检查结果
   */
  static checkWCAGContrast(foreground, background) {
    try {
      const contrast = chroma.contrast(foreground, background)
      
      return {
        ratio: contrast,
        aa: contrast >= 4.5,
        aaa: contrast >= 7,
        aaLarge: contrast >= 3,
        level: contrast >= 7 ? 'AAA' : contrast >= 4.5 ? 'AA' : 'Fail'
      }
    } catch (error) {
      console.warn('WCAG contrast check failed:', error)
      return { ratio: 0, aa: false, aaa: false, aaLarge: false, level: 'Error' }
    }
  }

  /**
   * 生成和谐配色方案
   * @param {string} baseColor - 基础颜色
   * @param {string} scheme - 配色方案类型
   * @returns {Array} 配色数组
   */
  static generateColorScheme(baseColor, scheme = 'complementary') {
    try {
      const base = chroma(baseColor)
      const hsl = base.hsl()
      const hue = hsl[0]
      
      switch (scheme) {
        case 'complementary':
          return [
            base.hex(),
            chroma.hsl((hue + 180) % 360, hsl[1], hsl[2]).hex()
          ]
        
        case 'triadic':
          return [
            base.hex(),
            chroma.hsl((hue + 120) % 360, hsl[1], hsl[2]).hex(),
            chroma.hsl((hue + 240) % 360, hsl[1], hsl[2]).hex()
          ]
        
        case 'analogous':
          return [
            chroma.hsl((hue - 30) % 360, hsl[1], hsl[2]).hex(),
            base.hex(),
            chroma.hsl((hue + 30) % 360, hsl[1], hsl[2]).hex()
          ]
        
        case 'monochromatic':
          return [
            chroma.hsl(hue, hsl[1], Math.min(hsl[2] + 0.3, 1)).hex(),
            base.hex(),
            chroma.hsl(hue, hsl[1], Math.max(hsl[2] - 0.3, 0)).hex()
          ]
        
        default:
          return [base.hex()]
      }
    } catch (error) {
      console.warn('Color scheme generation failed:', error)
      return []
    }
  }

  /**
   * 导出 CSS 变量格式
   * @param {Object} colors - 颜色对象
   * @param {string} prefix - CSS 变量前缀
   * @returns {string} CSS 变量字符串
   */
  static exportCSSVariables(colors, prefix = 'color') {
    const cssVars = Object.entries(colors)
      .map(([name, color]) => `  --${prefix}-${name}: ${color};`)
      .join('\n')
    
    return `:root {\n${cssVars}\n}`
  }

  /**
   * 导出 Tailwind 配置格式
   * @param {Object} colors - 颜色对象
   * @returns {string} Tailwind 配置字符串
   */
  static exportTailwindConfig(colors) {
    const config = JSON.stringify(colors, null, 2)
    return `module.exports = {\n  theme: {\n    extend: {\n      colors: ${config}\n    }\n  }\n}`
  }
}
```

## 📊 状态管理扩展

### Pinia Store 增强

```js
// src/stores/colorStore.js
import { defineStore } from 'pinia'
import ColorParser from '@/scripts/ColorParser.js'
import { ColorUtils } from '@/utils/colorUtils.js'
import chroma from 'chroma-js'

export const useColorStore = defineStore('color', {
  state: () => ({
    // 当前颜色状态
    currentColor: null,
    colorHistory: [],
    
    // 转换设置
    conversionAccuracy: 'high', // high, medium, fast
    deltaEThreshold: 2.0,
    
    // Wiki 相关
    favoriteFormats: ['hex', 'rgb', 'hsl'],
    recentlyViewed: [],
    
    // 转换器相关
    activeConverter: null,
    conversionHistory: [],
    
    // 用户偏好
    preferences: {
      theme: 'auto', // light, dark, auto
      defaultFormat: 'hex',
      showPrecision: true,
      enableAnimations: true
    }
  }),

  getters: {
    // 当前颜色的所有格式
    currentColorFormats: (state) => {
      if (!state.currentColor) return {}
      
      try {
        const color = chroma(state.currentColor.value)
        return {
          hex: color.hex(),
          rgb: color.css('rgb'),
          hsl: color.css('hsl'),
          hsv: color.hsv(),
          cmyk: color.cmyk(),
          oklch: ColorUtils.toOklch(color),
          lab: color.lab(),
          xyz: color.xyz()
        }
      } catch (error) {
        console.warn('Color format conversion failed:', error)
        return {}
      }
    },

    // 推荐的相关格式
    recommendedFormats: (state) => {
      const current = state.currentColor?.mode
      const recommendations = {
        'hex': ['rgb', 'hsl'],
        'rgb': ['hex', 'hsl', 'hsv'],
        'hsl': ['rgb', 'hsv', 'oklch'],
        'oklch': ['hsl', 'lch', 'p3'],
        'cmyk': ['rgb', 'hex']
      }
      return recommendations[current] || []
    },

    // 颜色历史统计
    historyStats: (state) => {
      const formats = state.colorHistory.map(c => c.mode)
      const formatCounts = formats.reduce((acc, format) => {
        acc[format] = (acc[format] || 0) + 1
        return acc
      }, {})
      
      return {
        totalColors: state.colorHistory.length,
        mostUsedFormat: Object.keys(formatCounts).reduce((a, b) => 
          formatCounts[a] > formatCounts[b] ? a : b, 'hex'
        ),
        formatDistribution: formatCounts
      }
    }
  },

  actions: {
    // 解析颜色
    parseColor(input) {
      const parsed = ColorParser.parseEnhanced(input, {
        enableCache: true,
        enableSuggestions: true,
        enableFuzzyMatch: true,
        strictMode: false
      })
      
      if (parsed.mode !== 'unknown') {
        this.currentColor = {
          ...parsed,
          chroma: chroma(parsed.value),
          timestamp: Date.now()
        }
        this.addToHistory(this.currentColor)
      }
      
      return parsed
    },

    // 转换到指定格式
    convertToFormat(targetFormat) {
      if (!this.currentColor) return null
      
      try {
        const chromaColor = this.currentColor.chroma
        
        switch (targetFormat) {
          case 'hex':
            return chromaColor.hex()
          case 'rgb':
            return chromaColor.css('rgb')
          case 'hsl':
            return chromaColor.css('hsl')
          case 'hsv':
            const hsv = chromaColor.hsv()
            return `hsv(${Math.round(hsv[0] || 0)}, ${Math.round((hsv[1] || 0) * 100)}%, ${Math.round((hsv[2] || 0) * 100)}%)`
          case 'cmyk':
            const cmyk = chromaColor.cmyk()
            return `cmyk(${cmyk.map(v => Math.round(v * 100) + '%').join(', ')})`
          case 'oklch':
            return ColorUtils.toOklch(chromaColor)
          case 'lab':
            const lab = chromaColor.lab()
            return `lab(${lab[0].toFixed(1)} ${lab[1].toFixed(1)} ${lab[2].toFixed(1)})`
          case 'xyz':
            const xyz = chromaColor.xyz()
            return `xyz(${xyz.map(v => v.toFixed(3)).join(' ')})`
          default:
            return null
        }
      } catch (error) {
        console.error('Color conversion failed:', error)
        return null
      }
    },

    // 计算色差
    calculateDeltaE(color1, color2) {
      return ColorUtils.calculateDeltaE(color1, color2)
    },

    // 生成配色方案
    generateColorScheme(scheme = 'complementary') {
      if (!this.currentColor) return []
      return ColorUtils.generateColorScheme(this.currentColor.value, scheme)
    },

    // 添加到历史记录
    addToHistory(color) {
      // 避免重复添加相同颜色
      const exists = this.colorHistory.find(c => c.value === color.value)
      if (exists) return
      
      this.colorHistory.unshift(color)
      
      // 限制历史记录数量
      if (this.colorHistory.length > 100) {
        this.colorHistory = this.colorHistory.slice(0, 100)
      }
    },

    // 添加到收藏格式
    addFavoriteFormat(format) {
      if (!this.favoriteFormats.includes(format)) {
        this.favoriteFormats.push(format)
      }
    },

    // 记录最近查看的 Wiki
    addRecentlyViewed(format) {
      const index = this.recentlyViewed.indexOf(format)
      if (index > -1) {
        this.recentlyViewed.splice(index, 1)
      }
      this.recentlyViewed.unshift(format)
      
      // 限制最近查看数量
      if (this.recentlyViewed.length > 10) {
        this.recentlyViewed = this.recentlyViewed.slice(0, 10)
      }
    },

    // 记录转换历史
    addConversionHistory(conversion) {
      this.conversionHistory.unshift({
        ...conversion,
        id: Date.now(),
        timestamp: new Date()
      })
      
      // 限制转换历史数量
      if (this.conversionHistory.length > 50) {
        this.conversionHistory = this.conversionHistory.slice(0, 50)
      }
    },

    // 更新用户偏好
    updatePreferences(newPreferences) {
      this.preferences = { ...this.preferences, ...newPreferences }
    },

    // 清空历史记录
    clearHistory() {
      this.colorHistory = []
      this.conversionHistory = []
      this.recentlyViewed = []
    },

    // 导出数据
    exportData() {
      return {
        colorHistory: this.colorHistory,
        conversionHistory: this.conversionHistory,
        favoriteFormats: this.favoriteFormats,
        preferences: this.preferences,
        exportDate: new Date().toISOString()
      }
    },

    // 导入数据
    importData(data) {
      if (data.colorHistory) this.colorHistory = data.colorHistory
      if (data.conversionHistory) this.conversionHistory = data.conversionHistory
      if (data.favoriteFormats) this.favoriteFormats = data.favoriteFormats
      if (data.preferences) this.preferences = { ...this.preferences, ...data.preferences }
    }
  },

  // 持久化配置
  persist: {
    key: 'colorcode-store',
    storage: localStorage,
    paths: ['favoriteFormats', 'recentlyViewed', 'preferences', 'colorHistory']
  }
})
```

## 🚀 路由配置

### 完整路由设置

```js
// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import { useColorStore } from '@/stores/colorStore'

// 路由组件懒加载
const LandingPage = () => import('@/components/LandingPage.vue')
const WikiHub = () => import('@/components/wiki/WikiHub.vue')
const WikiLayout = () => import('@/components/wiki/WikiLayout.vue')
const ConverterHub = () => import('@/components/converter/ConverterHub.vue')
const ConverterLayout = () => import('@/components/converter/ConverterLayout.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: LandingPage,
    meta: {
      title: 'ColorCode.cc - 专业级颜色工具平台',
      description: '提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务'
    }
  },
  
  // Wiki 知识库路由
  {
    path: '/wiki',
    name: 'Wiki',
    component: WikiHub,
    meta: {
      title: 'ColorWiki - 颜色格式知识库',
      description: '专业的颜色格式技术文档和应用指南'
    },
    children: [
      {
        path: '',
        name: 'WikiIndex',
        component: () => import('@/components/wiki/WikiIndex.vue')
      },
      {
        path: ':format',
        name: 'WikiFormat',
        component: WikiLayout,
        props: true,
        meta: {
          title: (route) => `${route.params.format.toUpperCase()} 颜色格式 - ColorWiki`,
          description: (route) => `${route.params.format.toUpperCase()} 颜色格式的技术原理、应用场景和最佳实践`
        },
        beforeEnter: (to, from, next) => {
          const validFormats = [
            'hex', 'rgb', 'hsl', 'hsv', 'cmyk', 
            'oklch', 'lch', 'xyz', 'p3', 'rec2020', 'keywords'
          ]
          
          if (validFormats.includes(to.params.format)) {
            // 记录访问历史
            const colorStore = useColorStore()
            colorStore.addRecentlyViewed(to.params.format)
            next()
          } else {
            next('/wiki')
          }
        }
      }
    ]
  },
  
  // 转换工具路由
  {
    path: '/converter',
    name: 'Converter',
    component: ConverterHub,
    meta: {
      title: '专业颜色转换工具 - ColorCode.cc',
      description: '提供高精度的颜色格式转换工具，支持实时预览和代码导出'
    },
    children: [
      {
        path: '',
        name: 'ConverterIndex',
        component: () => import('@/components/converter/ConverterIndex.vue')
      },
      {
        path: ':type',
        name: 'ConverterType',
        component: ConverterLayout,
        props: true,
        meta: {
          title: (route) => `${route.params.type.toUpperCase()} 转换器 - ColorCode.cc`,
          description: (route) => `专业的 ${route.params.type.replace('-', ' ↔ ').toUpperCase()} 颜色转换工具`
        },
        beforeEnter: (to, from, next) => {
          const validConverters = [
            'hex-rgb', 'rgb-hsl', 'hsl-hsv', 'cmyk-rgb',
            'oklch-hsl', 'lch-lab', 'xyz-rgb', 'p3-rgb'
          ]
          
          if (validConverters.includes(to.params.type)) {
            next()
          } else {
            next('/converter')
          }
        }
      }
    ]
  },
  
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/components/common/NotFound.vue'),
    meta: {
      title: '页面未找到 - ColorCode.cc',
      description: '抱歉，您访问的页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 动态设置页面标题
  if (to.meta.title) {
    const title = typeof to.meta.title === 'function' 
      ? to.meta.title(to) 
      : to.meta.title
    document.title = title
  }
  
  // 设置 meta description
  if (to.meta.description) {
    const description = typeof to.meta.description === 'function'
      ? to.meta.description(to)
      : to.meta.description
    
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.name = 'description'
      document.head.appendChild(metaDescription)
    }
    metaDescription.content = description
  }
  
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('Router error:', error)
  
  // 发送错误报告
  if (typeof gtag !== 'undefined') {
    gtag('event', 'exception', {
      description: `Router error: ${error.message}`,
      fatal: false
    })
  }
})

export default router
```

## 📈 开发实施计划

### 第一阶段：基础架构 (2-3周)

#### Week 1: 项目结构搭建
- [ ] 安装和配置新依赖
- [ ] 创建路由结构
- [ ] 搭建 Pinia Store
- [ ] 实现基础组件框架

#### Week 2: Wiki 基础功能
- [ ] 实现 WikiLayout 组件
- [ ] 创建 Markdown 渲染系统
- [ ] 完成 HEX、RGB、HSL 三个核心格式 Wiki
- [ ] 实现 Wiki 导航和搜索

#### Week 3: 转换工具基础
- [ ] 实现 ConverterHub 组件
- [ ] 完成 HEX↔RGB 转换器
- [ ] 实现基础色轮组件
- [ ] 添加转换历史功能

### 第二阶段：功能完善 (3-4周)

#### Week 4-5: Wiki 内容扩展
- [ ] 完成其余 8 种格式的 Wiki 页面
- [ ] 添加格式间的关联推荐
- [ ] 实现 Wiki 收藏和最近访问
- [ ] 优化移动端 Wiki 体验

#### Week 6-7: 转换工具增强
- [ ] 实现 RGB↔HSL、CMYK↔RGB 转换器
- [ ] 添加 ΔE 精度计算显示
- [ ] 实现代码导出功能
- [ ] 添加配色方案生成器

### 第三阶段：高级功能 (4-5周)

#### Week 8-9: 交互增强
- [ ] 完善交互式色轮功能
- [ ] 实现 OKLCH 和高级格式支持
- [ ] 添加 WCAG 对比度检查
- [ ] 实现批量颜色处理

#### Week 10-12: 优化和完善
- [ ] 性能优化和代码分割
- [ ] SEO 优化和 meta 标签
- [ ] 用户体验测试和改进
- [ ] 文档完善和部署

## 🎯 成功指标

### 技术指标
- **页面加载时间**: < 2秒 (首屏)
- **转换精度**: ΔE < 1.0 (常用格式)
- **缓存命中率**: > 80%
- **移动端适配**: 100% 响应式

### 用户体验指标
- **Wiki 页面停留时间**: > 2分钟
- **转换工具使用率**: > 60% 用户使用
- **错误率**: < 5% 颜色解析失败
- **用户满意度**: > 4.5/5.0

### 业务指标
- **SEO 排名**: 目标关键词前 10 位
- **用户留存**: 7天留存率 > 40%
- **功能使用**: Wiki 和转换器使用比例 6:4
- **移动端流量**: > 35% 总流量

## 📚 总结

这个优化后的技术方案具有以下优势：

1. **轻量级架构**: 减少 ~850KB 包体积，提升加载性能
2. **现有技术栈兼容**: 基于 Vue 3 + Vite，无需大幅重构
3. **渐进式实施**: 分阶段开发，降低风险
4. **用户体验优先**: 注重交互设计和响应式适配
5. **可维护性强**: 清晰的组件结构和状态管理

通过这个方案，ColorCode.cc 将成为业界领先的专业颜色工具平台，为设计师、开发者和品牌团队提供完整的色彩管理解决方案。