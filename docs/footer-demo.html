<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer 导航系统演示 - ColorCode.cc</title>
    <style>
        :root {
            --color-primary: #6366f1;
            --color-secondary: #8b5cf6;
            --color-text-primary: #1f2937;
            --color-text-secondary: #6b7280;
            --color-text-tertiary: #9ca3af;
            --color-border: #e5e7eb;
            --color-background-secondary: #f3f4f6;
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            --spacing-12: 3rem;
            --spacing-16: 4rem;
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --radius-sm: 0.25rem;
            --radius-full: 9999px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--color-text-primary);
            background: #ffffff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-4);
        }

        .demo-header {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            color: white;
            padding: var(--spacing-16) 0;
            text-align: center;
        }

        .demo-content {
            padding: var(--spacing-16) 0;
            min-height: 60vh;
        }

        .demo-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-4);
        }

        .demo-description {
            font-size: var(--text-lg);
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-6);
            margin-top: var(--spacing-8);
        }

        .feature-card {
            background: white;
            border: 1px solid var(--color-border);
            border-radius: var(--radius-sm);
            padding: var(--spacing-6);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .feature-title {
            font-size: var(--text-lg);
            font-weight: 600;
            margin-bottom: var(--spacing-3);
            color: var(--color-primary);
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            padding: var(--spacing-1) 0;
            color: var(--color-text-secondary);
        }

        .feature-list li:before {
            content: "✓";
            color: var(--color-primary);
            font-weight: bold;
            margin-right: var(--spacing-2);
        }

        /* Footer 样式 - 从 LandingPage.vue 复制 */
        .footer-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-top: 1px solid var(--color-border);
            margin-top: var(--spacing-16);
            padding: var(--spacing-16) 0 var(--spacing-8);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-nav {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-8);
            margin-bottom: var(--spacing-12);
        }

        .footer-nav-group {
            display: flex;
            flex-direction: column;
        }

        .footer-nav-title {
            font-size: var(--text-lg);
            font-weight: 600;
            color: var(--color-text-primary);
            margin-bottom: var(--spacing-4);
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
        }

        .footer-nav-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-2);
        }

        .footer-nav-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            color: var(--color-text-secondary);
            text-decoration: none;
            font-size: var(--text-sm);
            padding: var(--spacing-1) 0;
            transition: all 0.2s ease;
            border-radius: var(--radius-sm);
        }

        .footer-nav-link:hover {
            color: var(--color-primary);
            transform: translateX(4px);
        }

        .icon {
            width: 16px;
            height: 16px;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .footer-nav-link:hover .icon {
            opacity: 1;
        }

        .footer-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent 0%, var(--color-border) 50%, transparent 100%);
            margin: var(--spacing-8) 0;
        }

        .footer-bottom {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-8);
            flex-wrap: wrap;
        }

        .footer-info {
            flex: 1;
            min-width: 300px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-3);
        }

        .logo-text {
            font-size: var(--text-xl);
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-version {
            font-size: var(--text-xs);
            color: var(--color-text-tertiary);
            background: var(--color-background-secondary);
            padding: var(--spacing-1) var(--spacing-2);
            border-radius: var(--radius-full);
            font-weight: 500;
        }

        .footer-description {
            color: var(--color-text-secondary);
            font-size: var(--text-sm);
            line-height: 1.6;
            margin: 0;
        }

        .footer-meta {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: var(--spacing-3);
        }

        .copyright {
            color: var(--color-text-tertiary);
            font-size: var(--text-xs);
            margin: 0;
        }

        .footer-badges {
            display: flex;
            gap: var(--spacing-2);
            flex-wrap: wrap;
        }

        .badge {
            font-size: var(--text-xs);
            padding: var(--spacing-1) var(--spacing-2);
            background: transparent;
            border: 1px solid var(--color-border);
            color: var(--color-text-secondary);
            border-radius: var(--radius-sm);
        }

        @media (max-width: 768px) {
            .footer-nav {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--spacing-6);
                margin-bottom: var(--spacing-8);
            }

            .footer-bottom {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-4);
            }

            .footer-meta {
                align-items: flex-start;
                width: 100%;
            }

            .footer-info {
                min-width: auto;
            }
        }

        @media (max-width: 480px) {
            .footer-nav {
                grid-template-columns: 1fr;
                gap: var(--spacing-4);
            }

            .footer-nav-group {
                padding-bottom: var(--spacing-4);
                border-bottom: 1px solid var(--color-border);
            }

            .footer-nav-group:last-child {
                border-bottom: none;
            }
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <div class="container">
            <h1 class="demo-title">Footer 导航系统演示</h1>
            <p class="demo-description">
                展示 ColorCode.cc 2.0 版本新增的 Footer 导航系统，提供 Wiki 知识库和专业转换器工具的快速访问入口
            </p>
        </div>
    </div>

    <div class="demo-content">
        <div class="container">
            <div class="feature-grid">
                <div class="feature-card">
                    <h3 class="feature-title">🧭 核心工具导航</h3>
                    <ul class="feature-list">
                        <li>专业转换器工具直达入口</li>
                        <li>Wiki 知识库系统快速访问</li>
                        <li>Vue Router 无缝导航体验</li>
                        <li>图标 + 文字的清晰标识</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3 class="feature-title">🎨 颜色格式导航</h3>
                    <ul class="feature-list">
                        <li>HEX 十六进制格式说明</li>
                        <li>RGB 三原色技术文档</li>
                        <li>HSL 色相饱和度指南</li>
                        <li>OKLCH 感知色彩标准</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3 class="feature-title">🔧 转换工具快捷入口</h3>
                    <ul class="feature-list">
                        <li>HEX 转 RGB 转换器</li>
                        <li>RGB 转 HSL 转换器</li>
                        <li>HSL 转 HEX 转换器</li>
                        <li>OKLCH 专业转换器</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3 class="feature-title">📱 响应式设计</h3>
                    <ul class="feature-list">
                        <li>桌面端 4 列网格布局</li>
                        <li>平板端 2 列自适应</li>
                        <li>移动端单列紧凑布局</li>
                        <li>暗色主题自动适配</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer 演示 -->
    <footer class="footer-section">
        <div class="container">
            <div class="footer-content">
                <!-- 主要导航 -->
                <div class="footer-nav">
                    <div class="footer-nav-group">
                        <h3 class="footer-nav-title">核心工具</h3>
                        <ul class="footer-nav-list">
                            <li>
                                <a href="/converter" class="footer-nav-link">
                                    <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
                                    </svg>
                                    专业转换器工具
                                </a>
                            </li>
                            <li>
                                <a href="/wiki" class="footer-nav-link">
                                    <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                                    </svg>
                                    Wiki 知识库系统
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="footer-nav-group">
                        <h3 class="footer-nav-title">颜色格式</h3>
                        <ul class="footer-nav-list">
                            <li><a href="/wiki/hex" class="footer-nav-link">HEX 十六进制</a></li>
                            <li><a href="/wiki/rgb" class="footer-nav-link">RGB 三原色</a></li>
                            <li><a href="/wiki/hsl" class="footer-nav-link">HSL 色相饱和度</a></li>
                            <li><a href="/wiki/oklch" class="footer-nav-link">OKLCH 感知色彩</a></li>
                        </ul>
                    </div>

                    <div class="footer-nav-group">
                        <h3 class="footer-nav-title">转换工具</h3>
                        <ul class="footer-nav-list">
                            <li><a href="/converter/hex-to-rgb" class="footer-nav-link">HEX 转 RGB</a></li>
                            <li><a href="/converter/rgb-to-hsl" class="footer-nav-link">RGB 转 HSL</a></li>
                            <li><a href="/converter/hsl-to-hex" class="footer-nav-link">HSL 转 HEX</a></li>
                            <li><a href="/converter/oklch-converter" class="footer-nav-link">OKLCH 转换器</a></li>
                        </ul>
                    </div>

                    <div class="footer-nav-group">
                        <h3 class="footer-nav-title">关于我们</h3>
                        <ul class="footer-nav-list">
                            <li><a href="#about" class="footer-nav-link">产品介绍</a></li>
                            <li><a href="#contact" class="footer-nav-link">联系我们</a></li>
                            <li><a href="#privacy" class="footer-nav-link">隐私政策</a></li>
                            <li><a href="#terms" class="footer-nav-link">服务条款</a></li>
                        </ul>
                    </div>
                </div>

                <!-- 分隔线 -->
                <div class="footer-divider"></div>

                <!-- 底部信息 -->
                <div class="footer-bottom">
                    <div class="footer-info">
                        <div class="footer-logo">
                            <span class="logo-text">ColorCode.cc</span>
                            <span class="logo-version">v2.0</span>
                        </div>
                        <p class="footer-description">
                            专业级在线颜色工具平台，为设计师和开发者提供高精度颜色转换服务
                        </p>
                    </div>
                    
                    <div class="footer-meta">
                        <p class="copyright">
                            © 2024 ColorCode.cc. All rights reserved.
                        </p>
                        <div class="footer-badges">
                            <span class="badge">Phase 1 完成</span>
                            <span class="badge">Wiki 知识库</span>
                            <span class="badge">专业转换器</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 简单的交互演示
        document.addEventListener('DOMContentLoaded', function() {
            const footerLinks = document.querySelectorAll('.footer-nav-link');
            
            footerLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const href = this.getAttribute('href');
                    alert(`导航到: ${href}\n\n在实际应用中，这将使用 Vue Router 进行页面导航。`);
                });
            });
        });
    </script>
</body>
</html>
