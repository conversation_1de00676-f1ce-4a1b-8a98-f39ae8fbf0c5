<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- 基础 SEO 信息 -->
  <title>ColorCode.cc - 专业级在线颜色工具平台</title>
  <meta name="description" content="专业级在线颜色工具平台，提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务。支持 OKLCH、LAB、CMYK 等专业格式，ΔE≤0.5 精度保障。" />
  <meta name="keywords" content="ColorCode.cc,颜色工具,色彩转换,OKLCH,LAB,CMYK,无障碍设计,CSS变量生成,Vue3,Vite" />
  <meta name="author" content="ColorCode.cc Team" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://colorcode.cc/" />
  <meta property="og:title" content="ColorCode.cc - 专业级在线颜色工具平台" />
  <meta property="og:description" content="专业级在线颜色工具平台，提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务。" />
  <meta property="og:image" content="https://colorcode.cc/og-image.png" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:locale" content="zh_CN" />
  <meta property="og:site_name" content="ColorCode.cc" />
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://colorcode.cc/" />
  <meta property="twitter:title" content="ColorCode.cc - 专业级在线颜色工具平台" />
  <meta property="twitter:description" content="专业级在线颜色工具平台，提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务。" />
  <meta property="twitter:image" content="https://colorcode.cc/twitter-image.png" />
  <meta property="twitter:creator" content="@colorcode_cc" />
  
  <!-- 网站图标 -->
  <link rel="icon" type="image/png" href="/favicon.png" />
  <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
  
  <!-- 结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "ColorCode.cc",
    "description": "专业级在线颜色工具平台",
    "url": "https://colorcode.cc",
    "applicationCategory": "DesignApplication",
    "operatingSystem": "Web Browser",
    "browserRequirements": "Requires JavaScript. Requires HTML5.",
    "softwareVersion": "1.0.0",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "CNY",
      "availability": "https://schema.org/InStock"
    },
    "creator": {
      "@type": "Organization",
      "name": "ColorCode.cc Team",
      "url": "https://colorcode.cc"
    },
    "featureList": [
      "高精度颜色转换（ΔE≤0.5）",
      "OKLCH 标准支持",
      "智能无障碍检测",
      "CSS 变量生成器"
    ]
  }
  </script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@200..900&family=Parkinsans:wght@300..800&display=swap" rel="stylesheet">
  <!-- 关键 CSS 内联（提升首屏性能） -->
  <style>
    /* 关键路径 CSS - 首屏渲染必需的样式 */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    html {
      scroll-behavior: smooth;
    }
    
    body {
      font-family: "Parkinsans", sans-serif;
      font-optical-sizing: auto;
      font-weight: 500;
      font-style: normal;
      font-size: 16px;
      line-height: 1.6;
      color: #1f2937;
      background-color: #f9fafb;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    #app {
      min-height: 100vh;
    }
    

  </style>
</head>

<body>
  <!-- 应用容器 -->
  <div id="app"></div>
  

  
  <!-- 主应用脚本 -->
  <script type="module" src="/src/main.js"></script>
  

</body>
</html>
