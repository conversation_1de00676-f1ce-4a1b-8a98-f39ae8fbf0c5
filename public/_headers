# Cloudflare Pages Headers Configuration
# 为 ColorCode.cc 优化的 HTTP 头配置

/*
  # 安全头
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  
  # 性能优化
  Cache-Control: public, max-age=31536000, immutable
  
  # CORS 配置
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, OPTIONS
  Access-Control-Allow-Headers: Content-Type

# JavaScript 模块文件
/*.js
  Content-Type: application/javascript; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

# CSS 文件
/*.css
  Content-Type: text/css; charset=utf-8
  Cache-Control: public, max-age=31536000, immutable

# 字体文件
/*.woff2
  Content-Type: font/woff2
  Cache-Control: public, max-age=31536000, immutable

/*.woff
  Content-Type: font/woff
  Cache-Control: public, max-age=31536000, immutable

# 图片文件
/*.png
  Content-Type: image/png
  Cache-Control: public, max-age=31536000, immutable

/*.jpg
  Content-Type: image/jpeg
  Cache-Control: public, max-age=31536000, immutable

/*.svg
  Content-Type: image/svg+xml
  Cache-Control: public, max-age=31536000, immutable

# HTML 文件
/*.html
  Content-Type: text/html; charset=utf-8
  Cache-Control: public, max-age=3600

# 根目录 HTML
/
  Content-Type: text/html; charset=utf-8
  Cache-Control: public, max-age=3600
