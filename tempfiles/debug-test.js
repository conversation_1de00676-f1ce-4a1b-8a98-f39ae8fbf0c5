/**
 * debug-test.js - Simple test to debug MarkdownWiki component
 */

import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import MarkdownWiki from '../src/components/wiki/formats/MarkdownWiki.vue'

// Mock dependencies
vi.mock('@/composables/useMarkdown', () => ({
  useMarkdown: () => ({
    loadMarkdownFile: vi.fn().mockResolvedValue('<h1>Test</h1>'),
    isLoading: { value: false },
    error: { value: null }
  })
}))

vi.mock('@/stores/colorStore', () => ({
  useColorStore: () => ({
    parseColor: vi.fn()
  })
}))

vi.mock('@/components/common/ColorPreview.vue', () => ({
  default: {
    name: 'ColorPreview',
    template: '<div>ColorPreview</div>'
  }
}))

vi.mock('../src/components/wiki/QuickConverter.vue', () => ({
  default: {
    name: 'QuickConverter', 
    template: '<div>QuickConverter</div>'
  }
}))

describe('MarkdownWiki Debug', () => {
  it('should render and show HTML structure', () => {
    const wrapper = mount(MarkdownWiki, {
      props: {
        format: 'hex'
      }
    })
    
    console.log('HTML:', wrapper.html())
    console.log('Classes found:', wrapper.find('*').classes())
    
    expect(wrapper.exists()).toBe(true)
  })
})
