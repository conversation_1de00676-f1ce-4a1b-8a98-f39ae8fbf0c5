/**
 * ColorCode.cc Vite Configuration
 * 基于 Vite 的现代化构建配置
 */

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue()
  ],

  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    },
    // 确保模块正确解析
    dedupe: ['vue']
  },

  server: {
    host: true,
    open: true
  },

  build: {
    outDir: 'dist',
    // 确保生成正确的模块格式
    target: 'es2015',
    // Cloudflare Pages 优化配置
    rollupOptions: {
      output: {
        // 确保模块正确打包
        manualChunks: {
          vue: ['vue'],
          vendor: ['chroma-js']
        },
        // 确保正确的模块格式
        format: 'es'
      }
    },
    // 确保所有依赖都被正确打包
    commonjsOptions: {
      include: [/node_modules/]
    },
    // 确保模块正确解析
    minify: 'esbuild',
    sourcemap: false
  },

  define: {
    __APP_VERSION__: JSON.stringify('1.0.0'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  }
})
