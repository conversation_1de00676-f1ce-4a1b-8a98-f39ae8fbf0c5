/**
 * ColorCode.cc Vitest Configuration
 * 基于 Vitest 3.x 的测试配置
 * 
 * 测试特性：
 * - Vue 3 组件测试
 * - 颜色转换算法测试
 * - 用户交互测试
 * - 覆盖率报告
 * - 性能测试
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue({
      // 测试环境下的 Vue 配置
      template: {
        compilerOptions: {
          // 在测试中将自定义元素视为原生元素
          isCustomElement: (tag) => tag.startsWith('test-')
        }
      }
    })
  ],
  
  // 路径解析配置（与主应用保持一致）
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  
  // 测试配置
  test: {
    // 测试环境
    environment: 'jsdom',
    
    // 全局测试设置
    globals: true,
    
    // 设置文件
    setupFiles: ['./src/test/setup.js'],
    
    // 包含的测试文件模式
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'src/**/__tests__/**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    
    // 排除的文件
    exclude: [
      'node_modules',
      'dist',
      '.git',
      '.vscode',
      'coverage'
    ],
    
    // 测试超时设置
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html', 'json', 'lcov'],
      reportsDirectory: './coverage',
      
      // 覆盖率阈值
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      },
      
      // 包含的文件
      include: [
        'src/**/*.{js,vue}'
      ],
      
      // 排除的文件
      exclude: [
        'src/main.js',
        'src/**/*.test.js',
        'src/**/*.spec.js',
        'src/**/__tests__/**',
        'src/test/**'
      ]
    },
    
    // 并发设置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        minThreads: 1,
        maxThreads: 4
      }
    },
    
    // 监听模式配置
    watch: {
      // 忽略的文件
      ignored: ['**/node_modules/**', '**/dist/**']
    },
    
    // 报告器配置
    reporters: ['verbose', 'html'],
    outputFile: {
      html: './test-results/index.html',
      json: './test-results/results.json'
    },
    
    // 模拟配置
    mockReset: true,
    clearMocks: true,
    restoreMocks: true,
    
    // 环境变量
    env: {
      NODE_ENV: 'test',
      VITE_APP_TITLE: 'ColorCode.cc Test'
    }
  },
  
  // 定义全局变量（与主应用保持一致）
  define: {
    __APP_VERSION__: JSON.stringify('1.0.0-test'),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString())
  },
  
  // 依赖优化
  optimizeDeps: {
    include: [
      'vue',
      '@vue/test-utils',
      'vitest',
      'jsdom'
    ]
  }
})
