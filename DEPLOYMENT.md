# ColorCode.cc Cloudflare Pages 部署指南

## 🚀 Cloudflare Pages 部署修复

### 问题描述
在 Cloudflare Pages 部署时遇到两个主要问题：

1. **模块解析错误**：
```
Uncaught TypeError: Failed to resolve module specifier "vue".
Relative references must start with either "/", "./", or "../".
```

2. **Yarn 锁定文件冲突**：
```
The lockfile would have been modified by this install, which is explicitly forbidden.
```

3. **Node.js 兼容性问题**：
```
[vite:build-html] crypto.hash is not a function
```

### 解决方案

#### 1. 兼容性优化的 Vite 配置
创建了专门的 Cloudflare Pages 构建配置 `vite.config.cloudflare.js`：

```javascript
export default defineConfig({
  build: {
    target: 'es2015',
    rollupOptions: {
      output: {
        manualChunks: {
          vue: ['vue'],
          vendor: ['chroma-js']
        },
        format: 'es'
      }
    },
    minify: 'esbuild',
    sourcemap: false,
    // Cloudflare Pages 兼容性设置
    reportCompressedSize: false
  }
})
```

**关键版本要求**：
- Vite: `^5.4.10`（避免 7.x 的 Node.js 兼容性问题）
- Node.js: `18.20.4` LTS（Cloudflare Pages 支持的稳定版本）

#### 2. 包管理器配置

**`.npmrc`** - 强制使用 npm：
```
registry=https://registry.npmjs.org/
package-lock=true
loglevel=warn
```

**`wrangler.toml`** - Cloudflare Pages 配置：
```toml
[build]
command = "npm install && npm run build:cloudflare"

[build.environment_variables]
NODE_VERSION = "18"
NPM_VERSION = "9"
YARN_ENABLE = "false"
```

#### 3. Cloudflare Pages 配置文件

**`_headers`** - HTTP 头配置：
```
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Cache-Control: public, max-age=31536000, immutable
```

**`_redirects`** - SPA 路由支持：
```
/* /index.html 200
```

#### 3. 构建脚本
```bash
npm run build:cloudflare
```

### 部署步骤

#### 方法一：GitHub 集成（推荐）

1. **推送代码到 GitHub**
   ```bash
   git add .
   git commit -m "fix: Cloudflare Pages 部署优化"
   git push origin main
   ```

2. **连接 Cloudflare Pages**
   - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - 进入 Pages 页面
   - 点击 "Create a project"
   - 选择 "Connect to Git"
   - 选择 GitHub 仓库

3. **配置构建设置**
   ```
   Framework preset: None
   Build command: npm install && npm run build:cloudflare
   Build output directory: dist
   Root directory: /
   ```

4. **环境变量**（重要）
   ```
   NODE_VERSION=18.20.4
   NPM_VERSION=9
   YARN_ENABLE=false
   NPM_CONFIG_PACKAGE_MANAGER=npm
   NODE_OPTIONS=--max-old-space-size=4096
   ```

#### 方法二：直接上传

1. **本地构建**
   ```bash
   npm run build:cloudflare
   ```

2. **上传 dist 目录**
   - 在 Cloudflare Pages 中选择 "Upload assets"
   - 上传整个 `dist` 目录

### 验证部署

1. **检查构建日志**
   - 确保没有模块解析错误
   - 验证所有资源正确打包

2. **浏览器测试**
   - 打开部署的 URL
   - 检查控制台是否有错误
   - 测试所有功能正常工作

3. **性能检查**
   - 使用 Lighthouse 检查性能
   - 验证页面加载速度
   - 测试响应式设计

### 常见问题解决

#### 问题：模块解析错误
**解决方案**：使用专门的 Cloudflare 构建配置
```bash
npm run build:cloudflare
```

#### 问题：静态资源 404
**解决方案**：检查 `_headers` 和 `_redirects` 配置

#### 问题：SPA 路由不工作
**解决方案**：确保 `_redirects` 文件包含：
```
/* /index.html 200
```

### 性能优化

1. **资源压缩**
   - 启用 Cloudflare 的 Auto Minify
   - 使用 Brotli 压缩

2. **缓存策略**
   - 静态资源：1年缓存
   - HTML：1小时缓存

3. **CDN 优化**
   - 启用 Cloudflare 的 Polish
   - 使用 WebP 图片格式

### 监控和维护

1. **错误监控**
   - 配置 Cloudflare Analytics
   - 设置错误报警

2. **性能监控**
   - 使用 Web Vitals
   - 定期性能审计

3. **更新流程**
   - 自动化 CI/CD
   - 分支保护规则

## 🎯 部署清单

- [ ] 代码推送到 GitHub
- [ ] Cloudflare Pages 项目创建
- [ ] 构建配置正确设置
- [ ] 环境变量配置
- [ ] 域名配置（如需要）
- [ ] SSL 证书验证
- [ ] 性能测试通过
- [ ] 功能测试通过
- [ ] 监控配置完成

## 📞 支持

如果遇到部署问题，请：
1. 检查构建日志
2. 验证配置文件
3. 联系技术支持团队
