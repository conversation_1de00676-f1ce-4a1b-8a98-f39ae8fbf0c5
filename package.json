{"name": "colorcode-landing-page", "version": "1.0.0", "description": "ColorCode.cc 专业级颜色工具平台 Landing Page", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:cloudflare": "vite build --config vite.config.cloudflare.js --mode production", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "verify": "npm ci && npm run build"}, "dependencies": {"@vueuse/core": "^11.3.0", "chroma-js": "^3.1.2", "markdown-it": "^14.1.0", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^3.2.3", "prismjs": "^1.30.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vitest/ui": "^1.6.1", "@vue/test-utils": "^2.4.6", "jsdom": "^26.1.0", "vite": "^5.4.19", "vitest": "^1.6.1"}, "keywords": ["colorcode", "color-tool", "color-conversion", "oklch", "accessibility"], "author": "ColorCode.cc Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/colorcode-cc/landing-page.git"}, "homepage": "https://colorcode.cc", "bugs": {"url": "https://github.com/colorcode-cc/landing-page/issues"}, "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.45.1"}}