/**
 * ColorCode.cc Landing Page Styles
 * 基于 colorcode_design_system.json 设计系统规范
 * 
 * 设计原则：
 * - 专业级颜色工具的品牌定位
 * - 响应式设计，支持多设备
 * - 现代化的视觉效果
 * - 优秀的用户体验
 */

/* ============================================================================
   CSS 自定义属性 (基于设计系统)
   ============================================================================ */

:root {
  /* 主色调 - 基于设计系统的 primary 颜色 */
  --color-primary: #6366f1;
  --color-primary-light: #8b5cf6;
  --color-primary-dark: #4f46e5;
  --color-primary-50: #eef2ff;
  --color-primary-100: #e0e7ff;
  --color-primary-500: #6366f1;
  --color-primary-600: #5b21b6;
  --color-primary-700: #4c1d95;

  /* 辅助色 */
  --color-secondary-green: #10b981;
  --color-secondary-blue: #3b82f6;
  --color-secondary-pink: #ec4899;
  --color-secondary-orange: #f59e0b;
  
  /* 中性色 */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 背景色 - Light Mode 淡雅风格 */
  --bg-page: #ffffff;
  --bg-section-light: #f9fafb;
  --bg-section-alt: #f3f4f6;
  --bg-card: #ffffff;
  --bg-footer: #111827;
  --bg-hero-gradient: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
  
  /* 功能色 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* 字体 */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  
  /* 间距 - 严格按照设计系统规范 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;

  /* 组件间距 - 设计系统规范 */
  --card-padding: 1.5rem;          /* 设计系统规范：card_padding */
  --section-padding: 4rem;         /* 设计系统规范：section_padding */
  --container-padding: 1rem;       /* 设计系统规范：container_padding */
  
  /* 圆角 - 严格按照设计系统规范 */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;    /* 按钮圆角 */
  --radius-lg: 0.75rem;   /* 卡片和图标容器圆角 */
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* 阴影 - 严格按照设计系统规范 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 1px 3px rgba(0, 0, 0, 0.1);     /* 卡片基础阴影 */
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.1);    /* 卡片悬停阴影 */
  --shadow-xl: 0 8px 24px rgba(0, 0, 0, 0.15);   /* 卡片强调阴影 */
  --shadow-2xl: 0 16px 48px rgba(0, 0, 0, 0.2);
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ============================================================================
   基础样式重置
   ============================================================================ */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

/* 基础字体样式 - 严格按照设计系统规范 */
body {
  font-family: "Parkinsans","Noto Serif SC", sans-serif;
  font-optical-sizing: auto;
  font-weight: 500;
  font-style: normal;
  font-size: 1rem;                 /* 设计系统规范：typography fontSize base */
  line-height: 1.5;                /* 设计系统规范：typography lineHeight normal */
  color: #374151;                  /* 设计系统规范：neutral gray_700 */
  background-color: #ffffff;       /* 设计系统规范：background page */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ============================================================================
   布局组件
   ============================================================================ */

/* 容器布局 - 严格按照设计系统规范 */
.container {
  max-width: 1200px;               /* 设计系统规范：layout maxWidth */
  margin: 0 auto;                  /* 设计系统规范：layout containerAlignment */
  padding: 0 1rem;                 /* 设计系统规范：container_padding */
}

@media (min-width: 768px) {        /* 设计系统规范：breakpoints md */
  .container {
    padding: 0 1.5rem;             /* 设计系统规范：gridSystem gutter */
  }
}

@media (min-width: 1024px) {       /* 设计系统规范：breakpoints lg */
  .container {
    padding: 0 2rem;
  }
}

/* ============================================================================
   按钮组件
   ============================================================================ */

/* 按钮组件 - 严格按照设计系统规范 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;                     /* 设计系统规范 */
  padding: 0.75rem 1.5rem;         /* 设计系统规范：button_padding */
  font-size: 0.875rem;             /* 设计系统规范：sm */
  font-weight: 500;                /* 设计系统规范：medium */
  line-height: 1;
  border: none;
  border-radius: 0.5rem;           /* 设计系统规范：button borderRadius */
  cursor: pointer;
  transition: all 0.2s ease-in-out; /* 设计系统规范：medium transition */
  text-decoration: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.btn-lg {
  padding: 1rem 2rem;              /* 设计系统规范：lg size */
  font-size: 1rem;                 /* 设计系统规范：base */
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-600);
  border-color: var(--color-primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--color-gray-200);
  color: var(--color-gray-700);
  border-color: var(--color-gray-200);
}

.btn-secondary:hover {
  background-color: var(--color-gray-300);
  border-color: var(--color-gray-300);
}

/* ============================================================================
   图标样式
   ============================================================================ */

.icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* ============================================================================
   徽章组件
   ============================================================================ */

/* 徽章组件 - 严格按照设计系统规范 */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;                    /* 设计系统规范 */
  padding: 0.25rem 0.75rem;        /* 设计系统规范：badge_padding */
  font-size: 0.75rem;              /* 设计系统规范：xs */
  font-weight: 500;                /* 设计系统规范：medium */
  border-radius: 0.375rem;         /* 设计系统规范：badge borderRadius */
  white-space: nowrap;
}

.badge-success {
  background-color: #dcfce7;       /* 设计系统规范：success variant */
  color: #166534;
}

.badge-info {
  background-color: #dbeafe;       /* 设计系统规范：info variant */
  color: #1e40af;
}

.badge-warning {
  background-color: #fef3c7;       /* 设计系统规范：warning variant */
  color: #92400e;
}

/* ============================================================================
   区域标题样式
   ============================================================================ */

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.section-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: #1f2937;                  /* 设计系统规范：neutral gray_800 */
  margin-bottom: var(--spacing-4);
}

@media (min-width: 768px) {
  .section-title {
    font-size: var(--text-4xl);
  }
}

.section-description {
  font-size: var(--text-lg);
  color: var(--color-gray-600);
  /* max-width: 600px; */
  margin: 0 auto;
}

/* ============================================================================
   Hero 区域样式
   ============================================================================ */

.hero-section {
  padding: var(--spacing-20) 0;
  background: var(--bg-hero-gradient);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.015'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  /* max-width: 800px; */
  margin: 0 auto;
}

.hero-badges {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-8);
}

/* Hero标题 - 严格按照设计系统规范 */
.hero-title {
  font-size: 2.25rem;              /* 设计系统规范：typography fontSize 4xl */
  font-weight: 800;                /* 设计系统规范：typography fontWeight extrabold */
  color: #1f2937;                  /* 设计系统规范：neutral gray_800 */
  margin-bottom: 1rem;             /* 设计系统规范：spacing 4 */
  line-height: 1.25;               /* 设计系统规范：typography lineHeight tight */
}

@media (min-width: 768px) {        /* 设计系统规范：breakpoints md */
  .hero-title {
    font-size: 3rem;               /* 设计系统规范：typography fontSize 5xl */
  }
}

.hero-subtitle {
  display: block;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-top: var(--spacing-2);
}

.hero-description {
  font-size: var(--text-lg);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-8);
  line-height: 1.7;
}

@media (min-width: 768px) {
  .hero-description {
    font-size: var(--text-xl);
  }
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  align-items: center;
}

@media (min-width: 640px) {
  .hero-actions {
    flex-direction: row;
    justify-content: center;
  }
}

/* ============================================================================
   Hero 可视化演示区域
   ============================================================================ */

.hero-visual {
  margin-top: var(--spacing-16);
  display: flex;
  justify-content: center;
}

.color-demo-container {
  background: white;
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-xl);
  /* max-width: 500px; */
  width: 100%;
}

.color-picker-demo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.demo-input {
  text-align: center;
}

.demo-color-input {
  width: 300px;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--text-lg);
  font-family: var(--font-family-mono);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  text-align: center;
  transition: border-color var(--transition-fast);
}

.demo-color-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.demo-color-input.error {
  border-color: var(--color-error);
  background-color: rgba(239, 68, 68, 0.05);
}

.demo-color-input.error:focus {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.demo-color-input.success {
  border-color: var(--color-success);
  background-color: rgba(16, 185, 129, 0.05);
}

.demo-color-input.success:focus {
  border-color: var(--color-success);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.error-message {
  margin-top: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-md);
  color: var(--color-error);
  font-size: var(--text-sm);
  text-align: center;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.format-detected {
  margin-top: var(--spacing-2);
  padding: var(--spacing-1) var(--spacing-3);
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: var(--radius-md);
  color: var(--color-success);
  font-size: var(--text-sm);
  text-align: center;
  font-weight: 500;
}

.demo-outputs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-4);
}

.demo-output {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  background: var(--color-gray-50);
  border: 2px solid transparent;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
  user-select: none;
}

.demo-output:hover {
  transform: translateY(-2px);
  background: var(--color-gray-100);
  border-color: var(--color-primary-light);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.demo-output.copied {
  /* background: rgba(16, 185, 129, 0.1); */
  border-color: var(--color-success);
  transform: translateY(-2px);
}

.demo-output.copied:hover {
  /* background: rgba(16, 185, 129, 0.15); */
}

.format-label {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.format-value {
  font-size: var(--text-sm);
  font-family: var(--font-family-mono);
  color: var(--color-gray-800);
  font-weight: 500;
}

.copy-icon {
  position: absolute;
  top: var(--spacing-2);
  right: var(--spacing-2);
  font-size: var(--text-xs);
  opacity: 0;
  transition: opacity var(--transition-fast);
  pointer-events: none;
}

.demo-output:hover .copy-icon {
  opacity: 0.6;
}

.demo-output.copied .copy-icon {
  opacity: 1;
  color: var(--color-success);
}

/* ============================================================================
   功能特性区域样式
   ============================================================================ */

.features-section {
  padding: var(--section-padding) 0;
  background: var(--bg-page);
}

/* 功能网格 - 严格按照设计系统规范 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;      /* 设计系统规范：mobile single_column */
  gap: 1.5rem;                     /* 设计系统规范：gridSystem gutter */
}

@media (min-width: 768px) {        /* 设计系统规范：breakpoints md */
  .features-grid {
    grid-template-columns: repeat(2, 1fr); /* 设计系统规范：tablet two_column */
  }
}

@media (min-width: 1024px) {       /* 设计系统规范：breakpoints lg */
  .features-grid {
    grid-template-columns: repeat(4, 1fr); /* 设计系统规范：desktop three_column (调整为4列以适应4个功能) */
  }
}

/* 功能卡片 - 严格按照设计系统规范 */
.feature-card {
  padding: 1.5rem;                 /* 设计系统规范：card_padding */
  background: #ffffff;             /* 设计系统规范：card backgroundColor */
  border: 1px solid #e5e7eb;       /* 设计系统规范：card border */
  border-radius: 0.75rem;          /* 设计系统规范：card borderRadius */
  text-align: center;
  transition: all 0.2s ease-in-out; /* 设计系统规范：medium transition */
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 设计系统规范：card boxShadow */
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-600) 100%);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-2px);     /* 设计系统规范：card hover transform */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15); /* 设计系统规范：card hover boxShadow */
  border-color: #d1d5db;           /* 设计系统规范：gray_300 */
}

.feature-card:hover::before {
  transform: scaleX(1);
}

/* 图标容器 - 严格按照设计系统规范 */
.feature-icon {
  width: 3rem;                     /* 设计系统规范：iconContainer width */
  height: 3rem;                    /* 设计系统规范：iconContainer height */
  margin: 0 auto 1rem;             /* 设计系统规范：iconContainer marginBottom */
  border-radius: 0.75rem;          /* 设计系统规范：iconContainer borderRadius */
  display: flex;                   /* 设计系统规范：iconContainer display */
  align-items: center;             /* 设计系统规范：iconContainer alignItems */
  justify-content: center;         /* 设计系统规范：iconContainer justifyContent */
  position: relative;
}

.feature-icon .icon {
  width: 1.5rem;
  height: 1.5rem;
  z-index: 1;
}

/* 图标容器颜色变体 - 严格按照设计系统规范 */
.icon-purple {
  background-color: #f3f4f6;       /* 设计系统规范：purple variant backgroundColor */
  color: #6366f1;                  /* 设计系统规范：purple variant color */
}

.icon-green {
  background-color: #dcfce7;       /* 设计系统规范：green variant backgroundColor */
  color: #10b981;                  /* 设计系统规范：green variant color */
}

.icon-blue {
  background-color: #dbeafe;       /* 设计系统规范：blue variant backgroundColor */
  color: #3b82f6;                  /* 设计系统规范：blue variant color */
}

.icon-orange {
  background-color: #fef3c7;       /* 设计系统规范：orange variant backgroundColor */
  color: #f59e0b;                  /* 设计系统规范：orange variant color */
}

.feature-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: #111827;                  /* 设计系统规范：neutral gray_900 */
  margin-bottom: var(--spacing-4);
}

.feature-description {
  font-size: var(--text-base);
  color: var(--color-gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-6);
}

.feature-specs {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-2);
}

.spec-badge {
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--color-primary-50);
  color: var(--color-primary-700);
  font-size: var(--text-xs);
  font-weight: 500;
  border-radius: var(--radius-md);
}

/* ============================================================================
   用户群体区域样式
   ============================================================================ */

.user-groups-section {
  padding: var(--section-padding) 0;
  background: var(--bg-section-light);
}

.user-groups-tabs {
  max-width: 1000px;
  margin: 0 auto;
}

.tab-nav {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-12);
  padding: var(--spacing-2);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--color-gray-600);
  background: transparent;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.tab-btn:hover {
  color: var(--color-primary);
  background: var(--color-primary-50);
}

.tab-btn.active {
  color: white;
  background: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.tab-btn .icon {
  width: 1rem;
  height: 1rem;
}

.tab-content {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.user-group-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
  padding: var(--spacing-12);
}

@media (min-width: 1024px) {
  .user-group-content {
    grid-template-columns: 1fr 1fr;
    align-items: center;
  }
}

.group-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: #111827;                  /* 设计系统规范：neutral gray_900 */
  margin-bottom: var(--spacing-4);
}

.group-description {
  font-size: var(--text-lg);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-6);
  line-height: 1.6;
}

.group-features {
  list-style: none;
}

.group-features li {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  font-size: var(--text-base);
  color: var(--color-gray-700);
}

.group-features .icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--color-success);
  flex-shrink: 0;
  margin-top: 2px;
}

.demo-showcase {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
}

.showcase-item {
  text-align: center;
}

.showcase-item h4 {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

.demo-preview {
  width: 100%;
  height: 80px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-sm);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-fast);
}

.demo-preview:hover {
  transform: scale(1.05);
}

/* ============================================================================
   技术优势区域样式
   ============================================================================ */

.tech-advantages-section {
  padding: var(--section-padding) 0;
  background: var(--bg-page);
}

.tech-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .tech-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .tech-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.tech-card {
  padding: var(--spacing-8);
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  transition: all var(--transition-normal);
}

.tech-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  background: white;
}

.tech-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.tech-icon {
  width: 48px;
  height: 48px;
  background: var(--color-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.tech-icon .icon {
  width: 24px;
  height: 24px;
  color: white;
}

.tech-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-1);
}

.tech-version {
  font-size: var(--text-sm);
  color: var(--color-primary);
  font-weight: 500;
}

.tech-description {
  font-size: var(--text-base);
  color: var(--color-gray-600);
  line-height: 1.6;
  margin-bottom: var(--spacing-6);
}

.tech-metrics {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-4);
}

.metric {
  text-align: center;
  flex: 1;
}

.metric-value {
  display: block;
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

.metric-label {
  font-size: var(--text-xs);
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ============================================================================
   统计数据区域样式
   ============================================================================ */

.stats-section {
  padding: var(--section-padding) 0;
  background: var(--bg-section-light);
  color: var(--color-gray-700);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 640px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 统计卡片 - 严格按照设计系统规范 */
.stat-card {
  text-align: center;              /* 设计系统规范：statsCard textAlign */
  padding: 2rem;                   /* 设计系统规范：statsCard padding */
  background: #ffffff;             /* 设计系统规范：statsCard backgroundColor */
  border-radius: 0.75rem;          /* 设计系统规范：statsCard borderRadius */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* 设计系统规范：statsCard boxShadow */
}

.stat-number {
  font-size: 2.25rem;              /* 设计系统规范：statsCard numberStyles fontSize */
  font-weight: 700;                /* 设计系统规范：statsCard numberStyles fontWeight */
  margin-bottom: 0.5rem;           /* 设计系统规范：statsCard numberStyles marginBottom */
  color: #111827;                  /* 设计系统规范：statsCard numberStyles color */
}

.stat-label {
  font-size: 0.875rem;             /* 设计系统规范：statsCard labelStyles fontSize */
  font-weight: 500;                /* 设计系统规范：statsCard labelStyles fontWeight */
  margin-bottom: 0.25rem;          /* 设计系统规范：statsCard labelStyles marginBottom */
  color: #6b7280;                  /* 设计系统规范：statsCard labelStyles color */
}

.stat-description {
  font-size: 0.75rem;              /* 设计系统规范：statsCard descriptionStyles fontSize */
  color: #9ca3af;                  /* 设计系统规范：statsCard descriptionStyles color */
  line-height: 1.5;
}

/* ============================================================================
   定价区域样式
   ============================================================================ */

.pricing-section {
  padding: var(--section-padding) 0;
  background: var(--bg-section-alt);
}

.pricing-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
  max-width: 1000px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .pricing-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.pricing-card {
  background: white;
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  text-align: center;
  position: relative;
  transition: all var(--transition-normal);
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.pricing-card.featured {
  border-color: var(--color-primary);
  transform: scale(1.05);
  box-shadow: var(--shadow-xl);
}

.pricing-card.featured::before {
  content: '推荐';
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-primary);
  color: white;
  padding: var(--spacing-1) var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 600;
}

.plan-header {
  margin-bottom: var(--spacing-8);
}

.plan-name {
  font-size: var(--text-xl);
  font-weight: 700;
  color: #111827;                  /* 设计系统规范：neutral gray_900 */
  margin-bottom: var(--spacing-4);
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-4);
}

.price-currency {
  font-size: var(--text-lg);
  color: #4b5563;                  /* 设计系统规范：neutral gray_600 */
}

.price-amount {
  font-size: var(--text-4xl);
  font-weight: 800;
  color: #111827;                  /* 设计系统规范：neutral gray_900 */
}

.price-period {
  font-size: var(--text-base);
  color: var(--color-gray-600);
}

.plan-description {
  font-size: var(--text-base);
  color: var(--color-gray-600);
  line-height: 1.6;
}

.plan-features {
  margin-bottom: var(--spacing-8);
}

.plan-features ul {
  list-style: none;
  text-align: left;
}

.plan-features li {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
  font-size: var(--text-base);
  color: var(--color-gray-700);
}

.plan-features .icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--color-success);
  flex-shrink: 0;
  margin-top: 2px;
}

.plan-action .btn {
  width: 100%;
}

/* ============================================================================
   CTA 区域样式
   ============================================================================ */

.cta-section {
  padding: var(--section-padding) 0;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-6);
  line-height: 1.2;
}

@media (min-width: 768px) {
  .cta-title {
    font-size: var(--text-4xl);
  }
}

.cta-description {
  font-size: var(--text-lg);
  opacity: 0.9;
  margin-bottom: var(--spacing-8);
  line-height: 1.6;
}

.cta-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  align-items: center;
}

@media (min-width: 640px) {
  .cta-actions {
    flex-direction: row;
    justify-content: center;
  }
}

/* ============================================================================
   动画效果
   ============================================================================ */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* ============================================================================
   响应式优化
   ============================================================================ */

@media (max-width: 640px) {
  .hero-badges {
    flex-direction: column;
    align-items: center;
  }

  .badge {
    justify-content: center;
  }

  .demo-outputs {
    grid-template-columns: 1fr;
  }

  .tech-metrics {
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .tab-nav {
    flex-direction: column;
  }

  .demo-showcase {
    grid-template-columns: 1fr;
  }
}

/* ============================================================================
   可访问性优化
   ============================================================================ */

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .btn-outline {
    border-width: 2px;
  }

  .feature-card,
  .tech-card,
  .pricing-card {
    border-width: 2px;
  }
}

/* 深色模式支持（可选） */
@media (prefers-color-scheme: dark) {
  :root {
    --color-gray-50: #1f2937;
    --color-gray-100: #374151;
    --color-gray-800: #f9fafb;
    --color-gray-900: #ffffff;
  }
}
