/**
 * ColorCode.cc Design System
 * 基于 colorcode_design_system.json 的完整设计系统实现
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

:root {
  /* ============================================================================
     颜色系统 - Color Palette
     ============================================================================ */
  
  /* 主色调 - Primary Colors */
  --color-primary: #6366f1;
  --color-primary-light: #8b5cf6;
  --color-primary-dark: #4f46e5;
  
  /* 辅助色 - Secondary Colors */
  --color-secondary-green: #10b981;
  --color-secondary-blue: #3b82f6;
  --color-secondary-pink: #ec4899;
  --color-secondary-orange: #f59e0b;
  
  /* 中性色 - Neutral Colors */
  --color-white: #ffffff;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* 背景色 - Background Colors */
  --color-bg-page: #ffffff;
  --color-bg-section-light: #f9fafb;
  --color-bg-section-dark: #1f2937;
  --color-bg-card: #ffffff;
  --color-bg-footer: #111827;
  
  /* 语义色 - Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* 文本颜色 - Text Colors */
  --color-text-primary: #111827;
  --color-text-secondary: #6b7280;
  --color-text-tertiary: #9ca3af;
  --color-text-inverse: #ffffff;
  
  /* 边框颜色 - Border Colors */
  --color-border: #e5e7eb;
  --color-border-light: #f3f4f6;
  --color-border-dark: #d1d5db;
  
  /* ============================================================================
     字体系统 - Typography
     ============================================================================ */
  
  /* 字体族 - Font Families */
  --font-family-primary: 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-secondary: 'SF Pro Display', system-ui, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  
  /* 字体大小 - Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  
  /* 字体粗细 - Font Weights */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* 行高 - Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* ============================================================================
     间距系统 - Spacing
     ============================================================================ */
  
  /* 基础间距 - Base Spacing */
  --spacing-px: 1px;
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */
  --spacing-32: 8rem;     /* 128px */
  
  /* 组件间距 - Component Spacing */
  --spacing-card-padding: 1.5rem;
  --spacing-section-padding: 4rem;
  --spacing-container-padding: 1rem;
  --spacing-button-padding: 0.75rem 1.5rem;
  --spacing-badge-padding: 0.25rem 0.75rem;
  
  /* ============================================================================
     布局系统 - Layout
     ============================================================================ */
  
  /* 容器 - Container */
  --layout-max-width: 1200px;
  --layout-grid-columns: 12;
  --layout-grid-gutter: 1.5rem;
  
  /* 断点 - Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  
  /* ============================================================================
     效果系统 - Effects
     ============================================================================ */
  
  /* 阴影 - Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 8px 24px rgba(0, 0, 0, 0.15);
  --shadow-2xl: 0 16px 48px rgba(0, 0, 0, 0.2);
  
  /* 圆角 - Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* 过渡 - Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-medium: 0.2s ease-in-out;
  --transition-slow: 0.3s ease-in-out;
}

/* ============================================================================
   基础样式重置 - Base Styles
   ============================================================================ */

* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-page);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ============================================================================
   组件基础样式 - Component Base Styles
   ============================================================================ */

/* 卡片组件 - Card Component */
.ds-card {
  background-color: var(--color-bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-card-padding);
  border: 1px solid var(--color-border);
  transition: var(--transition-medium);
}

.ds-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.ds-card--elevated {
  box-shadow: var(--shadow-lg);
}

.ds-card--outlined {
  box-shadow: none;
  border: 1px solid var(--color-border-dark);
}

.ds-card--ghost {
  box-shadow: none;
  border: none;
  background-color: transparent;
}

/* 按钮组件 - Button Component */
.ds-button {
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-button-padding);
  transition: var(--transition-medium);
  cursor: pointer;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  font-family: inherit;
}

.ds-button--primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.ds-button--primary:hover {
  background-color: var(--color-primary-dark);
}

.ds-button--secondary {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

.ds-button--secondary:hover {
  background-color: var(--color-gray-200);
}

.ds-button--outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.ds-button--outline:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
}

/* 按钮尺寸 - Button Sizes */
.ds-button--sm {
  padding: 0.5rem 1rem;
  font-size: var(--font-size-xs);
}

.ds-button--lg {
  padding: 1rem 2rem;
  font-size: var(--font-size-base);
}

/* 徽章组件 - Badge Component */
.ds-badge {
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-badge-padding);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
}

.ds-badge--success {
  background-color: #dcfce7;
  color: #166534;
}

.ds-badge--info {
  background-color: #dbeafe;
  color: #1e40af;
}

.ds-badge--warning {
  background-color: #fef3c7;
  color: #92400e;
}

.ds-badge--neutral {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

/* 图标容器 - Icon Container */
.ds-icon-container {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-4);
}

.ds-icon-container--purple {
  background-color: var(--color-gray-100);
  color: var(--color-primary);
}

.ds-icon-container--green {
  background-color: #dcfce7;
  color: var(--color-secondary-green);
}

.ds-icon-container--blue {
  background-color: #dbeafe;
  color: var(--color-secondary-blue);
}

.ds-icon-container--pink {
  background-color: #fce7f3;
  color: var(--color-secondary-pink);
}

.ds-icon-container--orange {
  background-color: #fef3c7;
  color: var(--color-secondary-orange);
}

/* 统计卡片 - Stats Card */
.ds-stats-card {
  text-align: center;
  padding: 2rem;
  background-color: var(--color-bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.ds-stats-number {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-2);
}

.ds-stats-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-1);
}

.ds-stats-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/* ============================================================================
   工具类 - Utility Classes
   ============================================================================ */

/* 容器 - Container */
.ds-container {
  max-width: var(--layout-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-container-padding);
}

/* 文本对齐 - Text Alignment */
.ds-text-center { text-align: center; }
.ds-text-left { text-align: left; }
.ds-text-right { text-align: right; }

/* 字体粗细 - Font Weights */
.ds-font-light { font-weight: var(--font-weight-light); }
.ds-font-regular { font-weight: var(--font-weight-regular); }
.ds-font-medium { font-weight: var(--font-weight-medium); }
.ds-font-semibold { font-weight: var(--font-weight-semibold); }
.ds-font-bold { font-weight: var(--font-weight-bold); }

/* 文本颜色 - Text Colors */
.ds-text-primary { color: var(--color-text-primary); }
.ds-text-secondary { color: var(--color-text-secondary); }
.ds-text-tertiary { color: var(--color-text-tertiary); }

/* 间距 - Spacing */
.ds-mb-1 { margin-bottom: var(--spacing-1); }
.ds-mb-2 { margin-bottom: var(--spacing-2); }
.ds-mb-3 { margin-bottom: var(--spacing-3); }
.ds-mb-4 { margin-bottom: var(--spacing-4); }
.ds-mb-6 { margin-bottom: var(--spacing-6); }
.ds-mb-8 { margin-bottom: var(--spacing-8); }
