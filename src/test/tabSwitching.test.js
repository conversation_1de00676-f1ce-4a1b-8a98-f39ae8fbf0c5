/**
 * 标签页切换功能专项测试
 * 验证用户群体标签页的切换逻辑
 * 
 * <AUTHOR> Team
 * @version 1.1.1
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import LandingPage from '../components/LandingPage.vue'

describe('标签页切换功能测试', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(LandingPage)
  })

  describe('标签页基础功能', () => {
    it('应该有四个用户群体标签', () => {
      const tabs = wrapper.findAll('.tab-btn')
      expect(tabs.length).toBe(4)
      
      // 验证标签文本
      const expectedTabs = ['UI/UX 设计师', '前端开发者', '品牌营销团队', '教育用户']
      tabs.forEach((tab, index) => {
        expect(tab.text()).toContain(expectedTabs[index])
      })
    })

    it('应该默认选中设计师标签', () => {
      const activeTab = wrapper.find('.tab-btn.active')
      expect(activeTab.exists()).toBe(true)
      expect(activeTab.text()).toContain('UI/UX 设计师')

      // 验证默认显示的内容
      const activeContent = wrapper.find('.user-group-content')
      expect(activeContent.exists()).toBe(true)

      const groupTitle = wrapper.find('.group-title')
      expect(groupTitle.text()).toContain('为设计师量身定制的专业工具')
    })

    it('应该有对应的内容区域', () => {
      const contentAreas = wrapper.findAll('.user-group-content')
      expect(contentAreas.length).toBe(1) // 现在只显示当前激活的内容区域

      // 验证内容区域是可见的
      const activeContent = wrapper.find('.user-group-content')
      expect(activeContent.exists()).toBe(true)
    })
  })

  describe('标签页切换逻辑', () => {
    it('点击开发者标签应该切换到开发者内容', async () => {
      // 找到开发者标签
      const developerTab = wrapper.findAll('.tab-btn').find(tab =>
        tab.text().includes('前端开发者')
      )
      expect(developerTab).toBeTruthy()

      // 点击开发者标签
      await developerTab.trigger('click')
      await wrapper.vm.$nextTick()

      // 验证标签状态
      expect(developerTab.classes()).toContain('active')
      expect(wrapper.vm.activeUserGroup).toBe('developers')

      // 验证内容更新
      const groupTitle = wrapper.find('.group-title')
      expect(groupTitle.text()).toContain('开发者友好的代码生成工具')
    })

    it('点击营销团队标签应该切换到营销团队内容', async () => {
      // 找到营销团队标签
      const marketingTab = wrapper.findAll('.tab-btn').find(tab =>
        tab.text().includes('品牌营销团队')
      )
      expect(marketingTab).toBeTruthy()

      // 点击营销团队标签
      await marketingTab.trigger('click')
      await wrapper.vm.$nextTick()

      // 验证标签状态
      expect(marketingTab.classes()).toContain('active')
      expect(wrapper.vm.activeUserGroup).toBe('marketing')

      // 验证内容更新
      const groupTitle = wrapper.find('.group-title')
      expect(groupTitle.text()).toContain('品牌色彩管理专家')
    })

    it('点击教育用户标签应该切换到教育用户内容', async () => {
      // 找到教育用户标签
      const educationTab = wrapper.findAll('.tab-btn').find(tab =>
        tab.text().includes('教育用户')
      )
      expect(educationTab).toBeTruthy()

      // 点击教育用户标签
      await educationTab.trigger('click')
      await wrapper.vm.$nextTick()

      // 验证标签状态
      expect(educationTab.classes()).toContain('active')
      expect(wrapper.vm.activeUserGroup).toBe('education')

      // 验证内容更新
      const groupTitle = wrapper.find('.group-title')
      expect(groupTitle.text()).toContain('交互式色彩学习平台')
    })

    it('切换标签时应该只显示对应的内容', async () => {
      // 切换到开发者标签
      const developerTab = wrapper.findAll('.tab-btn').find(tab =>
        tab.text().includes('前端开发者')
      )
      await developerTab.trigger('click')
      await wrapper.vm.$nextTick()

      // 验证只有一个内容区域存在且是开发者内容
      const contentAreas = wrapper.findAll('.user-group-content')
      expect(contentAreas.length).toBe(1)

      const visibleContent = wrapper.find('.user-group-content')
      expect(visibleContent.exists()).toBe(true)
      expect(visibleContent.find('.group-title').text()).toContain('开发者友好的代码生成工具')
    })
  })

  describe('标签页样式验证', () => {
    it('激活的标签应该有正确的样式类', async () => {
      const tabs = wrapper.findAll('.tab-btn')
      
      // 默认第一个标签是激活的
      expect(tabs[0].classes()).toContain('active')
      
      // 点击第二个标签
      await tabs[1].trigger('click')
      await wrapper.vm.$nextTick()
      
      // 验证样式类的变化
      expect(tabs[0].classes()).not.toContain('active')
      expect(tabs[1].classes()).toContain('active')
    })

    it('所有标签都应该有基础样式类', () => {
      const tabs = wrapper.findAll('.tab-btn')
      tabs.forEach(tab => {
        expect(tab.classes()).toContain('tab-btn')
      })
    })
  })

  describe('内容区域验证', () => {
    it('每个用户群体都应该有完整的内容结构', () => {
      const userGroups = ['designers', 'developers', 'marketing', 'education']
      
      userGroups.forEach(groupId => {
        // 切换到对应标签
        wrapper.vm.activeUserGroup = groupId
        wrapper.vm.$nextTick()
        
        // 验证内容结构
        const groupContent = wrapper.find(`[data-group="${groupId}"]`) || 
                           wrapper.findAll('.user-group-content').find(content => 
                             content.element.style.display !== 'none'
                           )
        
        if (groupContent && groupContent.exists()) {
          expect(groupContent.find('.group-title').exists()).toBe(true)
          expect(groupContent.find('.group-description').exists()).toBe(true)
          expect(groupContent.find('.group-features').exists()).toBe(true)
        }
      })
    })
  })
})
