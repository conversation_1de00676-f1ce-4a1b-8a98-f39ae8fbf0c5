/**
 * Footer 导航系统测试
 * 测试 LandingPage.vue 中的 footer 功能
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import LandingPage from '@/components/LandingPage.vue'

// 创建测试路由
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/converter', component: { template: '<div>Converter</div>' } },
    { path: '/wiki', component: { template: '<div>Wiki</div>' } },
    { path: '/wiki/hex', component: { template: '<div>HEX Wiki</div>' } },
    { path: '/wiki/rgb', component: { template: '<div>RGB Wiki</div>' } },
    { path: '/wiki/hsl', component: { template: '<div>HSL Wiki</div>' } },
    { path: '/wiki/oklch', component: { template: '<div>OKLCH Wiki</div>' } },
    { path: '/converter/hex-to-rgb', component: { template: '<div>HEX to RGB</div>' } },
    { path: '/converter/rgb-to-hsl', component: { template: '<div>RGB to HSL</div>' } },
    { path: '/converter/hsl-to-hex', component: { template: '<div>HSL to HEX</div>' } },
    { path: '/converter/oklch-converter', component: { template: '<div>OKLCH Converter</div>' } }
  ]
})

describe('Footer 导航系统', () => {
  let wrapper

  beforeEach(async () => {
    // 设置路由到首页
    await router.push('/')
    
    // 挂载组件
    wrapper = mount(LandingPage, {
      global: {
        plugins: [router]
      }
    })
    
    await wrapper.vm.$nextTick()
  })

  describe('Footer 结构测试', () => {
    it('应该渲染 footer 区域', () => {
      const footer = wrapper.find('.footer-section')
      expect(footer.exists()).toBe(true)
    })

    it('应该包含核心工具导航组', () => {
      const coreToolsGroup = wrapper.find('.footer-nav-group').at(0)
      expect(coreToolsGroup.exists()).toBe(true)
      expect(coreToolsGroup.text()).toContain('核心工具')
    })

    it('应该包含颜色格式导航组', () => {
      const formatsGroup = wrapper.find('.footer-nav-group').at(1)
      expect(formatsGroup.exists()).toBe(true)
      expect(formatsGroup.text()).toContain('颜色格式')
    })

    it('应该包含转换工具导航组', () => {
      const convertersGroup = wrapper.find('.footer-nav-group').at(2)
      expect(convertersGroup.exists()).toBe(true)
      expect(convertersGroup.text()).toContain('转换工具')
    })

    it('应该包含关于我们导航组', () => {
      const aboutGroup = wrapper.find('.footer-nav-group').at(3)
      expect(aboutGroup.exists()).toBe(true)
      expect(aboutGroup.text()).toContain('关于我们')
    })
  })

  describe('核心工具链接测试', () => {
    it('应该包含专业转换器工具链接', () => {
      const converterLink = wrapper.find('a[href="/converter"]')
      expect(converterLink.exists()).toBe(true)
      expect(converterLink.text()).toContain('专业转换器工具')
    })

    it('应该包含Wiki知识库系统链接', () => {
      const wikiLink = wrapper.find('a[href="/wiki"]')
      expect(wikiLink.exists()).toBe(true)
      expect(wikiLink.text()).toContain('Wiki 知识库系统')
    })
  })

  describe('颜色格式链接测试', () => {
    it('应该包含HEX格式链接', () => {
      const hexLink = wrapper.find('a[href="/wiki/hex"]')
      expect(hexLink.exists()).toBe(true)
      expect(hexLink.text()).toContain('HEX')
    })

    it('应该包含RGB格式链接', () => {
      const rgbLink = wrapper.find('a[href="/wiki/rgb"]')
      expect(rgbLink.exists()).toBe(true)
      expect(rgbLink.text()).toContain('RGB')
    })

    it('应该包含HSL格式链接', () => {
      const hslLink = wrapper.find('a[href="/wiki/hsl"]')
      expect(hslLink.exists()).toBe(true)
      expect(hslLink.text()).toContain('HSL')
    })

    it('应该包含OKLCH格式链接', () => {
      const oklchLink = wrapper.find('a[href="/wiki/oklch"]')
      expect(oklchLink.exists()).toBe(true)
      expect(oklchLink.text()).toContain('OKLCH')
    })
  })

  describe('转换工具链接测试', () => {
    it('应该包含HEX转RGB工具链接', () => {
      const hexToRgbLink = wrapper.find('a[href="/converter/hex-to-rgb"]')
      expect(hexToRgbLink.exists()).toBe(true)
      expect(hexToRgbLink.text()).toContain('HEX 转 RGB')
    })

    it('应该包含RGB转HSL工具链接', () => {
      const rgbToHslLink = wrapper.find('a[href="/converter/rgb-to-hsl"]')
      expect(rgbToHslLink.exists()).toBe(true)
      expect(rgbToHslLink.text()).toContain('RGB 转 HSL')
    })

    it('应该包含HSL转HEX工具链接', () => {
      const hslToHexLink = wrapper.find('a[href="/converter/hsl-to-hex"]')
      expect(hslToHexLink.exists()).toBe(true)
      expect(hslToHexLink.text()).toContain('HSL 转 HEX')
    })

    it('应该包含OKLCH转换器链接', () => {
      const oklchConverterLink = wrapper.find('a[href="/converter/oklch-converter"]')
      expect(oklchConverterLink.exists()).toBe(true)
      expect(oklchConverterLink.text()).toContain('OKLCH 转换器')
    })
  })

  describe('Footer 底部信息测试', () => {
    it('应该显示品牌信息', () => {
      const logoText = wrapper.find('.logo-text')
      expect(logoText.exists()).toBe(true)
      expect(logoText.text()).toBe('ColorCode.cc')
    })

    it('应该显示版本信息', () => {
      const logoVersion = wrapper.find('.logo-version')
      expect(logoVersion.exists()).toBe(true)
      expect(logoVersion.text()).toBe('v2.0')
    })

    it('应该显示版权信息', () => {
      const copyright = wrapper.find('.copyright')
      expect(copyright.exists()).toBe(true)
      expect(copyright.text()).toContain('© 2024 ColorCode.cc')
    })

    it('应该显示功能徽章', () => {
      const badges = wrapper.findAll('.footer-badges .badge')
      expect(badges.length).toBeGreaterThan(0)
      
      const badgeTexts = badges.map(badge => badge.text())
      expect(badgeTexts).toContain('Phase 1 完成')
      expect(badgeTexts).toContain('Wiki 知识库')
      expect(badgeTexts).toContain('专业转换器')
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动端正确显示', async () => {
      // 模拟移动端视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 480
      })
      
      // 触发resize事件
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()
      
      const footer = wrapper.find('.footer-section')
      expect(footer.exists()).toBe(true)
      
      // 验证导航组在移动端的布局
      const navGroups = wrapper.findAll('.footer-nav-group')
      expect(navGroups.length).toBe(4)
    })
  })

  describe('交互功能测试', () => {
    it('链接应该具有正确的悬停效果类', () => {
      const footerLinks = wrapper.findAll('.footer-nav-link')
      footerLinks.forEach(link => {
        expect(link.classes()).toContain('footer-nav-link')
      })
    })

    it('图标应该正确显示', () => {
      const iconsInLinks = wrapper.findAll('.footer-nav-link .icon')
      expect(iconsInLinks.length).toBeGreaterThan(0)
    })
  })
})
