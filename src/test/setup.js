/**
 * ColorCode.cc Test Setup
 * Vitest 测试环境设置文件
 * 
 * 功能：
 * - 全局测试配置
 * - 模拟浏览器 API
 * - 测试工具函数
 * - 性能测试辅助
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// ============================================================================
// 全局测试配置
// ============================================================================

/**
 * 配置 Vue Test Utils 全局设置
 */
config.global.mocks = {
  // 模拟全局属性
  $version: '1.0.0-test',
  $buildTime: '2024-01-01T00:00:00.000Z'
}

/**
 * 全局组件存根
 */
config.global.stubs = {
  // 路由相关组件存根
  'router-link': true,
  'router-view': true,
  
  // 第三方组件存根
  transition: false,
  'transition-group': false
}

// ============================================================================
// 浏览器 API 模拟
// ============================================================================

/**
 * 模拟 window.matchMedia
 */
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // 已弃用
    removeListener: vi.fn(), // 已弃用
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }))
})

/**
 * 模拟 ResizeObserver
 */
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

/**
 * 模拟 IntersectionObserver
 */
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

/**
 * 模拟 Canvas API
 */
const mockCanvasContext = {
  clearRect: vi.fn(),
  fillRect: vi.fn(),
  strokeRect: vi.fn(),
  beginPath: vi.fn(),
  closePath: vi.fn(),
  moveTo: vi.fn(),
  lineTo: vi.fn(),
  arc: vi.fn(),
  fill: vi.fn(),
  stroke: vi.fn(),
  createLinearGradient: vi.fn(() => ({
    addColorStop: vi.fn()
  })),
  createRadialGradient: vi.fn(() => ({
    addColorStop: vi.fn()
  })),
  getImageData: vi.fn((x = 0, y = 0, width = 1, height = 1) => ({
    data: new Uint8ClampedArray(width * height * 4),
    width,
    height
  })),
  createImageData: vi.fn((width, height) => {
    // 如果第一个参数是 ImageData 对象
    if (typeof width === 'object' && width.width && width.height) {
      return {
        data: new Uint8ClampedArray(width.width * width.height * 4),
        width: width.width,
        height: width.height
      }
    }
    // 如果是宽度和高度参数
    const w = typeof width === 'number' ? width : 1
    const h = typeof height === 'number' ? height : w
    return {
      data: new Uint8ClampedArray(w * h * 4),
      width: w,
      height: h
    }
  }),
  putImageData: vi.fn(),
  save: vi.fn(),
  restore: vi.fn(),
  translate: vi.fn(),
  rotate: vi.fn(),
  scale: vi.fn(),
  fillStyle: '#000000',
  strokeStyle: '#000000',
  lineWidth: 1,
  globalAlpha: 1,
  globalCompositeOperation: 'source-over'
}

// 模拟 HTMLCanvasElement
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: vi.fn(() => mockCanvasContext),
  writable: true
})

Object.defineProperty(HTMLCanvasElement.prototype, 'toDataURL', {
  value: vi.fn(() => 'data:image/png;base64,'),
  writable: true
})

Object.defineProperty(HTMLCanvasElement.prototype, 'toBlob', {
  value: vi.fn((callback) => {
    callback(new Blob([''], { type: 'image/png' }))
  }),
  writable: true
})

/**
 * 模拟 requestAnimationFrame
 */
global.requestAnimationFrame = vi.fn().mockImplementation(cb => {
  return setTimeout(cb, 16) // 模拟 60fps
})

global.cancelAnimationFrame = vi.fn().mockImplementation(id => {
  clearTimeout(id)
})

/**
 * 模拟 performance API
 */
Object.defineProperty(window, 'performance', {
  writable: true,
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => [])
  }
})

/**
 * 模拟 localStorage
 */
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  writable: true,
  value: localStorageMock
})

/**
 * 模拟 sessionStorage
 */
Object.defineProperty(window, 'sessionStorage', {
  writable: true,
  value: localStorageMock
})

/**
 * 模拟 navigator
 */
Object.defineProperty(window, 'navigator', {
  writable: true,
  value: {
    userAgent: 'Mozilla/5.0 (Test Environment)',
    language: 'zh-CN',
    languages: ['zh-CN', 'en'],
    onLine: true,
    serviceWorker: {
      register: vi.fn(() => Promise.resolve()),
      ready: Promise.resolve()
    }
  }
})

/**
 * 模拟 fetch API
 */
global.fetch = vi.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob())
  })
)

/**
 * 模拟 URL.createObjectURL
 */
global.URL.createObjectURL = vi.fn(() => 'blob:mock-url')
global.URL.revokeObjectURL = vi.fn()

// ============================================================================
// 测试工具函数
// ============================================================================

/**
 * 等待下一个 tick
 * @param {number} ms - 等待时间（毫秒）
 * @returns {Promise}
 */
global.nextTick = (ms = 0) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 模拟用户点击事件
 * @param {Element} element - 目标元素
 * @param {Object} options - 事件选项
 */
global.mockClick = (element, options = {}) => {
  const event = new MouseEvent('click', {
    bubbles: true,
    cancelable: true,
    ...options
  })
  element.dispatchEvent(event)
}

/**
 * 模拟键盘事件
 * @param {Element} element - 目标元素
 * @param {string} key - 按键
 * @param {Object} options - 事件选项
 */
global.mockKeyPress = (element, key, options = {}) => {
  const event = new KeyboardEvent('keydown', {
    key,
    bubbles: true,
    cancelable: true,
    ...options
  })
  element.dispatchEvent(event)
}

/**
 * 模拟输入事件
 * @param {Element} element - 目标元素
 * @param {string} value - 输入值
 */
global.mockInput = (element, value) => {
  element.value = value
  const event = new Event('input', {
    bubbles: true,
    cancelable: true
  })
  element.dispatchEvent(event)
}

/**
 * 创建模拟的颜色数据
 * @param {string} hex - 十六进制颜色值
 * @returns {Object} 颜色数据对象
 */
global.createMockColor = (hex = '#6366f1') => {
  return {
    hex,
    rgb: { r: 99, g: 102, b: 241 },
    hsl: { h: 238, s: 84, l: 67 },
    lab: { l: 47.3, a: 25.2, b: -67.9 }
  }
}

// ============================================================================
// 颜色转换测试辅助函数
// ============================================================================

/**
 * 验证颜色转换精度
 * @param {Object} result - 转换结果
 * @param {Object} expected - 期望结果
 * @param {number} tolerance - 容差
 * @returns {boolean} 是否在容差范围内
 */
global.validateColorAccuracy = (result, expected, tolerance = 0.5) => {
  if (typeof result !== 'object' || typeof expected !== 'object') {
    return false
  }
  
  for (const key in expected) {
    if (Math.abs(result[key] - expected[key]) > tolerance) {
      return false
    }
  }
  
  return true
}

/**
 * 计算 Delta E 色差
 * @param {Object} color1 - 颜色1 (LAB)
 * @param {Object} color2 - 颜色2 (LAB)
 * @returns {number} Delta E 值
 */
global.calculateDeltaE = (color1, color2) => {
  const deltaL = color1.l - color2.l
  const deltaA = color1.a - color2.a
  const deltaB = color1.b - color2.b
  
  return Math.sqrt(deltaL * deltaL + deltaA * deltaA + deltaB * deltaB)
}

// ============================================================================
// 性能测试辅助
// ============================================================================

/**
 * 性能测试包装器
 * @param {Function} fn - 要测试的函数
 * @param {number} iterations - 迭代次数
 * @returns {Object} 性能统计
 */
global.performanceTest = async (fn, iterations = 1000) => {
  const times = []
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now()
    await fn()
    const end = performance.now()
    times.push(end - start)
  }
  
  const total = times.reduce((sum, time) => sum + time, 0)
  const average = total / iterations
  const min = Math.min(...times)
  const max = Math.max(...times)
  
  return {
    total,
    average,
    min,
    max,
    iterations
  }
}

// ============================================================================
// 控制台输出控制
// ============================================================================

/**
 * 在测试环境中静默某些控制台输出
 */
const originalConsoleError = console.error
console.error = (...args) => {
  // 过滤掉一些已知的无害警告
  const message = args[0]
  if (
    typeof message === 'string' &&
    (
      message.includes('[Vue warn]') ||
      message.includes('ResizeObserver loop limit exceeded')
    )
  ) {
    return
  }
  originalConsoleError.apply(console, args)
}

// ============================================================================
// 测试环境清理
// ============================================================================

/**
 * 每个测试后的清理工作
 */
afterEach(() => {
  // 清理所有模拟
  vi.clearAllMocks()
  
  // 重置 localStorage
  localStorageMock.clear()
  
  // 清理 DOM
  document.body.innerHTML = ''
  
  // 重置全局状态
  delete window.__VUE_APP__
})

/**
 * 所有测试完成后的清理工作
 */
afterAll(() => {
  // 恢复原始的 console 方法
  console.error = originalConsoleError
})
