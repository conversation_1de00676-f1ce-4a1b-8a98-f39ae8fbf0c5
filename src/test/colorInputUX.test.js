/**
 * 颜色输入用户体验增强功能测试
 * 测试点击全选、粘贴即时识别等新功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import LandingPage from '../components/LandingPage.vue'

// Mock chroma-js
vi.mock('chroma-js', () => {
  const mockChroma = vi.fn((color) => {
    if (color === 'invalid-color' || color === 'xyz(1,2,3)') {
      throw new Error('Invalid color')
    }
    return {
      hex: () => color.startsWith('#') ? color.toUpperCase() : '#FF0000',
      rgb: () => [255, 0, 0],
      hsl: () => [0, 1, 0.5],
      cmyk: () => [0, 1, 1, 0],
      oklch: () => [0.525, 0.15, 239]
    }
  })
  
  mockChroma.valid = vi.fn((color) => {
    return !['invalid-color', 'xyz(1,2,3)'].includes(color)
  })
  
  return { default: mockChroma }
})

// Mock analytics
const mockTrackEvent = vi.fn()
vi.mock('../utils/analytics.js', () => ({
  trackEvent: mockTrackEvent
}))

describe('颜色输入用户体验增强功能测试', () => {
  let wrapper

  beforeEach(() => {
    // 清除所有 mock 调用记录
    vi.clearAllMocks()
    
    // 挂载组件
    wrapper = mount(LandingPage)
  })

  describe('点击全选功能', () => {
    it('应该在点击输入框时全选文本', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // Mock select 方法
      const selectSpy = vi.fn()
      input.element.select = selectSpy
      
      // 触发点击事件
      await input.trigger('click')
      
      // 等待 nextTick
      await wrapper.vm.$nextTick()
      
      // 验证 select 方法被调用
      expect(selectSpy).toHaveBeenCalled()
    })

    it('应该在获得焦点时全选文本', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // Mock select 方法
      const selectSpy = vi.fn()
      input.element.select = selectSpy
      
      // 触发焦点事件
      await input.trigger('focus')
      
      // 等待 nextTick
      await wrapper.vm.$nextTick()
      
      // 验证 select 方法被调用
      expect(selectSpy).toHaveBeenCalled()
    })
  })

  describe('粘贴即时识别功能', () => {
    it('应该在粘贴时立即处理颜色', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      pasteEvent.clipboardData = {
        getData: vi.fn().mockReturnValue('#ff0000')
      }
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 等待处理完成
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 10))
      
      // 验证颜色值被设置
      expect(wrapper.vm.demoColor).toBe('#FF0000')
      expect(wrapper.vm.detectedFormat).toBe('HEX')
      expect(wrapper.vm.isValidColor).toBe(true)
    })

    it('应该在粘贴时显示粘贴状态样式', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      pasteEvent.clipboardData = {
        getData: vi.fn().mockReturnValue('rgb(255, 0, 0)')
      }
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 验证粘贴状态
      expect(wrapper.vm.isPasting).toBe(true)
      
      // 等待粘贴状态重置
      await new Promise(resolve => setTimeout(resolve, 150))
      
      expect(wrapper.vm.isPasting).toBe(false)
    })

    it('应该处理粘贴空内容的情况', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建空粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      pasteEvent.clipboardData = {
        getData: vi.fn().mockReturnValue('')
      }
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 等待处理完成
      await wrapper.vm.$nextTick()
      
      // 验证粘贴状态被重置
      expect(wrapper.vm.isPasting).toBe(false)
    })

    it('应该处理粘贴数据获取失败的情况', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建无 clipboardData 的粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      // 不设置 clipboardData
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 等待处理完成
      await wrapper.vm.$nextTick()
      
      // 验证粘贴状态被重置
      expect(wrapper.vm.isPasting).toBe(false)
    })
  })

  describe('防抖输入处理', () => {
    it('应该在输入变化时使用防抖处理', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 设置初始值
      await input.setValue('#ff0000')
      
      // 触发输入事件
      await input.trigger('input')
      
      // 立即检查，应该还没有处理
      expect(wrapper.vm.detectedFormat).toBe('HEX') // 初始默认值
      
      // 等待防抖延迟
      await new Promise(resolve => setTimeout(resolve, 600))
      
      // 现在应该已经处理了
      expect(wrapper.vm.isValidColor).toBe(true)
    })

    it('应该在粘贴时跳过防抖处理', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 设置粘贴状态
      wrapper.vm.isPasting = true
      
      // 触发输入事件
      await input.trigger('input')
      
      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // 验证没有设置防抖定时器（因为在粘贴状态）
      expect(wrapper.vm.inputDebounceTimer).toBeNull()
    })
  })

  describe('样式状态管理', () => {
    it('应该正确应用粘贴状态样式类', async () => {
      // 设置粘贴状态
      wrapper.vm.isPasting = true
      await wrapper.vm.$nextTick()
      
      const input = wrapper.find('.demo-color-input')
      expect(input.classes()).toContain('pasting')
    })

    it('应该正确应用成功状态样式类', async () => {
      // 设置成功状态
      wrapper.vm.isValidColor = true
      wrapper.vm.colorError = ''
      await wrapper.vm.$nextTick()
      
      const input = wrapper.find('.demo-color-input')
      expect(input.classes()).toContain('success')
    })

    it('应该正确应用错误状态样式类', async () => {
      // 设置错误状态
      wrapper.vm.colorError = '错误信息'
      await wrapper.vm.$nextTick()
      
      const input = wrapper.find('.demo-color-input')
      expect(input.classes()).toContain('error')
    })

    it('应该正确应用修正状态样式类', async () => {
      // 设置修正状态
      wrapper.vm.correctionInfo = {
        original: 'ff0000',
        corrected: '#ff0000'
      }
      await wrapper.vm.$nextTick()
      
      const input = wrapper.find('.demo-color-input')
      expect(input.classes()).toContain('corrected')
    })

    it('应该正确应用低置信度状态样式类', async () => {
      // 设置低置信度状态
      wrapper.vm.confidence = 0.7
      await wrapper.vm.$nextTick()
      
      const input = wrapper.find('.demo-color-input')
      expect(input.classes()).toContain('low-confidence')
    })
  })

  describe('动态占位符功能', () => {
    it('应该生成动态占位符文本', () => {
      const placeholder = wrapper.vm.inputPlaceholder
      
      // 验证占位符包含必要信息
      expect(placeholder).toContain('输入颜色值')
      expect(placeholder).toContain('支持')
      expect(placeholder).toContain('种格式')
    })

    it('应该在 ColorParser 方法不可用时使用降级占位符', () => {
      // 临时移除 getSupportedFormats 方法
      const originalMethod = wrapper.vm.ColorParser?.getSupportedFormats
      if (wrapper.vm.ColorParser) {
        delete wrapper.vm.ColorParser.getSupportedFormats
      }
      
      const placeholder = wrapper.vm.inputPlaceholder
      
      // 验证降级占位符正常工作
      expect(placeholder).toContain('输入颜色值')
      expect(placeholder).toContain('支持')
      
      // 恢复方法
      if (wrapper.vm.ColorParser && originalMethod) {
        wrapper.vm.ColorParser.getSupportedFormats = originalMethod
      }
    })
  })
})
