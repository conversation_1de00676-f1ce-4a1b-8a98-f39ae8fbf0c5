/**
 * 颜色解析器测试套件
 * 验证智能颜色识别引擎的完整功能
 *
 * @version 2.0.0
 * <AUTHOR> Team
 */

import { describe, it, expect, beforeEach } from 'vitest'
import ColorParser from '../scripts/ColorParser.js'

describe('颜色解析器测试', () => {
    beforeEach(() => {
        // 清除缓存确保测试独立性
        ColorParser.clearCache()
    })

    describe('向后兼容性测试', () => {
        it('应该保持与原 ColorParser 完全兼容', () => {
            const testCases = [
                { input: '#ff0000', expected: 'hex' },
                { input: 'rgb(255, 0, 0)', expected: 'rgb' },
                { input: 'hsl(0, 100%, 50%)', expected: 'hsl' },
                { input: 'red', expected: 'keyword' },
                { input: 'invalid-color', expected: 'unknown' }
            ]

            testCases.forEach(({ input, expected }) => {
                const result = ColorParser.parse(input)
                if (expected === 'unknown') {
                    expect(['unknown', 'error']).toContain(result.mode)
                } else {
                    expect(result.mode).toBe(expected)
                }
                expect(result.value).toBeDefined()
            })
        })

        it('应该返回与原解析器相同的数据结构', () => {
            const result = ColorParser.parse('#ff0000')
            
            // 检查必需的字段
            expect(result).toHaveProperty('mode')
            expect(result).toHaveProperty('value')
            
            // 检查可选的增强字段
            if (result.confidence !== undefined) {
                expect(typeof result.confidence).toBe('number')
            }
        })
    })

    describe('增强功能测试', () => {
        it('应该支持增强解析方法', () => {
            const result = ColorParser.parseEnhanced('#ff0000')
            expect(result.mode).toBe('hex')
            expect(result.confidence).toBe(1.0)
        })

        it('应该支持新的颜色格式', () => {
            const testCases = [
                { input: 'lch(50% 20 180)', expected: 'lch' },
                { input: 'xyz(0.5 0.3 0.2)', expected: 'xyz' },
                { input: 'color(display-p3 1 0 0)', expected: 'p3' },
                { input: 'color(rec2020 1 0 0)', expected: 'rec2020' }
            ]

            testCases.forEach(({ input, expected }) => {
                const result = ColorParser.parseEnhanced(input)
                expect(result.mode).toBe(expected)
            })
        })

        it('应该提供智能容错处理', () => {
            const testCases = [
                { input: 'ff0000', expected: '#ff0000' }, // 缺少 #
                { input: 'rgb( 255 , 0 , 0 )', expected: 'rgb(255, 0, 0)' }, // 空格问题
                { input: 'RGB(255,0,0)', expected: 'rgb(255, 0, 0)' } // 大小写
            ]

            testCases.forEach(({ input, expected }) => {
                const result = ColorParser.parseEnhanced(input)
                expect(result.mode).not.toBe('error')
                // RGB 格式不需要转换为大写，保持原格式
                if (expected.startsWith('rgb')) {
                    expect(result.value).toBe(expected)
                } else {
                    expect(result.value).toBe(expected.toUpperCase())
                }
            })
        })

        it('应该提供智能建议', () => {
            const result = ColorParser.parseEnhanced('gg0000') // 无效 HEX
            expect(result.mode).toBe('error')
            expect(result.suggestions).toBeDefined()
            expect(result.suggestions.length).toBeGreaterThan(0)
        })
    })

    describe('工具方法测试', () => {
        it('应该支持批量解析', () => {
            const inputs = ['#ff0000', 'rgb(0, 255, 0)', 'blue', 'invalid']
            const results = ColorParser.parseMultiple(inputs)
            
            expect(results).toHaveLength(4)
            expect(results[0].result.mode).toBe('hex')
            expect(results[1].result.mode).toBe('rgb')
            expect(results[2].result.mode).toBe('keyword')
            expect(['error', 'unknown']).toContain(results[3].result.mode)
        })

        it('应该支持颜色格式验证', () => {
            const validResult = ColorParser.validate('#ff0000', 'hex')
            expect(validResult.valid).toBe(true)
            expect(validResult.format).toBe('hex')

            const invalidResult = ColorParser.validate('#ff0000', 'rgb')
            expect(invalidResult.valid).toBe(false)
            expect(invalidResult.error).toBe('FORMAT_MISMATCH')
        })

        it('应该支持模糊匹配解析', () => {
            const result = ColorParser.parseWithCorrection('ff0000')
            expect(result.success).toBe(true)
            expect(result.corrected).toBe('#FF0000')
            expect(result.wasCorrected).toBe(true)
        })

        it('应该提供建议功能', () => {
            const suggestions = ColorParser.getSuggestions('invalid-color')
            expect(suggestions.hasError).toBe(true)
            expect(suggestions.suggestions).toBeDefined()
        })
    })

    describe('性能和缓存测试', () => {
        it('应该正确使用缓存', () => {
            const input = '#ff0000'
            
            // 第一次解析
            const result1 = ColorParser.parseEnhanced(input)
            expect(result1.mode).toBe('hex')
            
            // 第二次解析应该使用缓存
            const result2 = ColorParser.parseEnhanced(input)
            expect(result2).toEqual(result1)
            
            // 验证缓存中有数据
            const stats = ColorParser.getStats()
            expect(stats.cacheSize).toBeGreaterThan(0)
        })

        it('应该能够清除缓存', () => {
            ColorParser.parseEnhanced('#ff0000')
            expect(ColorParser.getStats().cacheSize).toBeGreaterThan(0)
            
            ColorParser.clearCache()
            expect(ColorParser.getStats().cacheSize).toBe(0)
        })
    })

    describe('扩展性测试', () => {
        it('应该支持注册自定义检测器', () => {
            // 注册一个简单的自定义格式检测器
            ColorParser.registerDetector('custom', (input) => {
                if (input.startsWith('custom:')) {
                    return {
                        mode: 'custom',
                        value: input
                    }
                }
                return null
            })

            const result = ColorParser.parseEnhanced('custom:test')
            expect(result.mode).toBe('custom')
            expect(result.value).toBe('custom:test')
        })

        it('应该返回支持的格式列表', () => {
            const formats = ColorParser.getSupportedFormats()
            expect(formats).toContain('hex')
            expect(formats).toContain('rgb')
            expect(formats).toContain('lch')
            expect(formats).toContain('p3')
            expect(formats.length).toBeGreaterThan(10)
        })
    })

    describe('健康检查测试', () => {
        it('应该通过健康检查', () => {
            const health = ColorParser.healthCheck()
            expect(health.healthy).toBe(true)
            expect(health.score).toBe(100)
            expect(health.results.every(r => r.passed)).toBe(true)
        })
    })

    describe('完整格式支持测试', () => {
        it('应该支持所有基础格式', () => {
            const testCases = [
                // HEX 格式
                { input: '#f00', expected: 'hex' },
                { input: '#ff0000', expected: 'hex' },
                { input: '#ff0000ff', expected: 'hexa' },
                
                // RGB 格式
                { input: 'rgb(255, 0, 0)', expected: 'rgb' },
                { input: 'rgba(255, 0, 0, 0.5)', expected: 'rgba' },
                
                // HSL 格式
                { input: 'hsl(0, 100%, 50%)', expected: 'hsl' },
                { input: 'hsla(0, 100%, 50%, 0.5)', expected: 'hsla' },
                
                // HSV 格式
                { input: 'hsv(0, 100%, 100%)', expected: 'hsv' },
                { input: 'hsva(0, 100%, 100%, 0.5)', expected: 'hsva' },
                
                // CMYK 格式
                { input: 'cmyk(0%, 100%, 100%, 0%)', expected: 'cmyk' },
                
                // OKLCH 格式
                { input: 'oklch(0.5 0.2 0)', expected: 'oklch' },
                
                // 关键字
                { input: 'red', expected: 'keyword' },
                { input: 'blue', expected: 'keyword' },
                { input: 'gray50', expected: 'gray' }
            ]

            testCases.forEach(({ input, expected }) => {
                const result = ColorParser.parseEnhanced(input)
                expect(result.mode).toBe(expected)
                expect(result.value).toBeDefined()
            })
        })

        it('应该支持所有新增格式', () => {
            const testCases = [
                // LCH 格式
                { input: 'lch(50% 20 180)', expected: 'lch' },
                { input: 'lch(75% 30 45)', expected: 'lch' },
                
                // XYZ 格式
                { input: 'xyz(0.5 0.3 0.2)', expected: 'xyz' },
                { input: 'xyz(0.1 0.9 0.4)', expected: 'xyz' },
                
                // Display P3 格式
                { input: 'color(display-p3 1 0 0)', expected: 'p3' },
                { input: 'color(display-p3 0.5 0.5 0.5)', expected: 'p3' },
                { input: 'color(display-p3 0 1 0 0.8)', expected: 'p3' },
                
                // Rec2020 格式
                { input: 'color(rec2020 1 0 0)', expected: 'rec2020' },
                { input: 'color(rec2020 0.2 0.8 0.3)', expected: 'rec2020' },
                { input: 'color(rec2020 0 0 1 0.9)', expected: 'rec2020' }
            ]

            testCases.forEach(({ input, expected }) => {
                const result = ColorParser.parseEnhanced(input)
                expect(result.mode).toBe(expected)
                expect(result.value).toBeDefined()
            })
        })
    })

    describe('错误处理测试', () => {
        it('应该正确处理各种错误情况', () => {
            const testCases = [
                { input: '', expected: 'error' },
                { input: null, expected: 'error' },
                { input: 'not-a-color', expected: 'error' },
                { input: 'invalid(999, 0, 0)', expected: 'error' }, // 无效函数名
                { input: '#gggggg', expected: 'error' }
            ]

            testCases.forEach(({ input, expected }) => {
                const result = ColorParser.parseEnhanced(input)
                expect(['error', 'unknown']).toContain(result.mode)
            })
        })

        it('应该为错误提供有用的建议', () => {
            const errorCases = [
                'ff0000', // 缺少 #
                'rgb(300, 0, 0)', // 超出范围
                'hsl(400, 100%, 50%)', // 超出范围
                'reed' // 拼写错误
            ]

            errorCases.forEach(input => {
                const result = ColorParser.parseEnhanced(input)
                if (result.mode === 'error') {
                    expect(result.suggestions).toBeDefined()
                    expect(result.message).toBeDefined()
                }
            })
        })
    })
})
