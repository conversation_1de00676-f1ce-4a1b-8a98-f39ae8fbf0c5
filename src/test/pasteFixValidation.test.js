/**
 * 粘贴功能修复验证测试
 * 验证粘贴时不会出现重复内容的问题
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import LandingPage from '../components/LandingPage.vue'

// Mock chroma-js
vi.mock('chroma-js', () => {
  const mockChroma = vi.fn((color) => {
    if (color === 'invalid-color') {
      throw new Error('Invalid color')
    }
    return {
      hex: () => color.startsWith('#') ? color.toUpperCase() : '#FF0000',
      rgb: () => [255, 0, 0],
      hsl: () => [0, 1, 0.5],
      cmyk: () => [0, 1, 1, 0],
      oklch: () => [0.525, 0.15, 239]
    }
  })
  
  mockChroma.valid = vi.fn((color) => color !== 'invalid-color')
  
  return { default: mockChroma }
})

// Mock analytics
const mockTrackEvent = vi.fn()
vi.mock('../utils/analytics.js', () => ({
  trackEvent: mockTrackEvent
}))

describe('粘贴功能修复验证测试', () => {
  let wrapper

  beforeEach(() => {
    vi.clearAllMocks()
    wrapper = mount(LandingPage)
  })

  describe('粘贴重复值修复验证', () => {
    it('应该阻止默认粘贴行为，避免重复内容', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      const preventDefaultSpy = vi.fn()
      pasteEvent.preventDefault = preventDefaultSpy
      pasteEvent.clipboardData = {
        getData: vi.fn().mockReturnValue('#ff0000')
      }
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 验证 preventDefault 被调用
      expect(preventDefaultSpy).toHaveBeenCalled()
      
      // 等待处理完成
      await wrapper.vm.$nextTick()
      
      // 验证颜色值被正确设置（只有一份）
      expect(wrapper.vm.demoColor).toBe('#FF0000')
      expect(wrapper.vm.detectedFormat).toBe('HEX')
      expect(wrapper.vm.isValidColor).toBe(true)
    })

    it('应该正确处理粘贴的 RGB 颜色值', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      const preventDefaultSpy = vi.fn()
      pasteEvent.preventDefault = preventDefaultSpy
      pasteEvent.clipboardData = {
        getData: vi.fn().mockReturnValue('rgb(255, 0, 0)')
      }
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 验证 preventDefault 被调用
      expect(preventDefaultSpy).toHaveBeenCalled()
      
      // 等待处理完成
      await wrapper.vm.$nextTick()
      
      // 验证颜色值被正确设置
      expect(wrapper.vm.demoColor).toBe('rgb(255, 0, 0)')
      expect(wrapper.vm.detectedFormat).toBe('RGB')
      expect(wrapper.vm.isValidColor).toBe(true)
    })

    it('应该正确处理粘贴的颜色关键字', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      const preventDefaultSpy = vi.fn()
      pasteEvent.preventDefault = preventDefaultSpy
      pasteEvent.clipboardData = {
        getData: vi.fn().mockReturnValue('red')
      }
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 验证 preventDefault 被调用
      expect(preventDefaultSpy).toHaveBeenCalled()
      
      // 等待处理完成
      await wrapper.vm.$nextTick()
      
      // 验证颜色值被正确设置
      expect(wrapper.vm.demoColor).toBe('red')
      expect(wrapper.vm.detectedFormat).toBe('KEYWORD')
      expect(wrapper.vm.isValidColor).toBe(true)
    })

    it('应该正确处理粘贴空内容的情况', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建空粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      const preventDefaultSpy = vi.fn()
      pasteEvent.preventDefault = preventDefaultSpy
      pasteEvent.clipboardData = {
        getData: vi.fn().mockReturnValue('')
      }
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 验证 preventDefault 被调用
      expect(preventDefaultSpy).toHaveBeenCalled()
      
      // 等待处理完成
      await wrapper.vm.$nextTick()
      
      // 验证粘贴状态被重置
      expect(wrapper.vm.isPasting).toBe(false)
    })

    it('应该正确处理粘贴数据获取失败的情况', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建无 clipboardData 的粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      const preventDefaultSpy = vi.fn()
      pasteEvent.preventDefault = preventDefaultSpy
      // 不设置 clipboardData
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 验证 preventDefault 被调用
      expect(preventDefaultSpy).toHaveBeenCalled()
      
      // 等待处理完成
      await wrapper.vm.$nextTick()
      
      // 验证粘贴状态被重置
      expect(wrapper.vm.isPasting).toBe(false)
    })
  })

  describe('输入框样式修复验证', () => {
    it('应该使用固定的高对比度颜色', () => {
      const input = wrapper.find('.demo-color-input')
      
      // 获取计算样式（在测试环境中可能无法获取实际样式，但可以验证类名）
      expect(input.exists()).toBe(true)
      expect(input.element.tagName).toBe('INPUT')
      expect(input.element.type).toBe('text')
    })

    it('应该正确应用输入框样式类', async () => {
      // 设置成功状态
      wrapper.vm.isValidColor = true
      wrapper.vm.colorError = ''
      await wrapper.vm.$nextTick()
      
      const input = wrapper.find('.demo-color-input')
      expect(input.classes()).toContain('success')
    })

    it('应该正确应用错误状态样式类', async () => {
      // 设置错误状态
      wrapper.vm.colorError = '错误信息'
      await wrapper.vm.$nextTick()
      
      const input = wrapper.find('.demo-color-input')
      expect(input.classes()).toContain('error')
    })
  })

  describe('粘贴状态管理', () => {
    it('应该正确管理粘贴状态', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 创建粘贴事件
      const pasteEvent = new Event('paste', { bubbles: true })
      pasteEvent.preventDefault = vi.fn()
      pasteEvent.clipboardData = {
        getData: vi.fn().mockReturnValue('#ff0000')
      }
      
      // 触发粘贴事件
      input.element.dispatchEvent(pasteEvent)
      
      // 验证粘贴状态被设置
      expect(wrapper.vm.isPasting).toBe(true)
      
      // 等待状态重置
      await new Promise(resolve => setTimeout(resolve, 150))
      
      // 验证粘贴状态被重置
      expect(wrapper.vm.isPasting).toBe(false)
    })

    it('应该在粘贴时显示粘贴样式类', async () => {
      // 设置粘贴状态
      wrapper.vm.isPasting = true
      await wrapper.vm.$nextTick()
      
      const input = wrapper.find('.demo-color-input')
      expect(input.classes()).toContain('pasting')
    })
  })
})
