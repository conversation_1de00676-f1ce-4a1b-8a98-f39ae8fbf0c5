/**
 * 控制台错误修复验证测试
 * 验证用户输入不完整颜色值时不会产生控制台错误
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import LandingPage from '../components/LandingPage.vue'

// Mock chroma-js
vi.mock('chroma-js', () => {
  const mockChroma = vi.fn((color) => {
    if (color === 'invalid-color' || color === '1' || color === '11') {
      throw new Error(`unknown format: ${color}`)
    }
    return {
      hex: () => '#FF0000',
      rgb: () => [255, 0, 0],
      hsl: () => [0, 1, 0.5],
      cmyk: () => [0, 1, 1, 0],
      oklch: () => [0.525, 0.15, 239]
    }
  })
  
  return { default: mockChroma }
})

// Mock analytics
const mockTrackEvent = vi.fn()
vi.mock('../utils/analytics.js', () => ({
  trackEvent: mockTrackEvent
}))

describe('控制台错误修复验证测试', () => {
  let wrapper
  let consoleSpy

  beforeEach(() => {
    vi.clearAllMocks()
    // 监听 console.error 调用
    consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    wrapper = mount(LandingPage)
  })

  afterEach(() => {
    consoleSpy.mockRestore()
  })

  describe('不完整输入处理', () => {
    it('应该在输入单个数字时不产生控制台错误', async () => {
      // 模拟用户输入单个数字
      wrapper.vm.demoColor = '1'
      await wrapper.vm.$nextTick()
      
      // 访问 demoFormats 计算属性来触发计算
      const formats = wrapper.vm.demoFormats
      
      // 验证返回默认格式而不是错误
      expect(formats).toEqual([
        { name: 'HEX', value: '#6366f1' },
        { name: 'RGB', value: 'rgb(99, 102, 241)' },
        { name: 'HSL', value: 'hsl(239, 84%, 67%)' },
        { name: 'CMYK', value: 'cmyk(59%, 58%, 0%, 5%)' },
        { name: 'OKLCH', value: 'oklch(0.525 0.15 239)' }
      ])
      
      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })

    it('应该在输入两个数字时不产生控制台错误', async () => {
      // 模拟用户输入两个数字
      wrapper.vm.demoColor = '11'
      await wrapper.vm.$nextTick()
      
      // 访问 demoFormats 计算属性来触发计算
      const formats = wrapper.vm.demoFormats
      
      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')
      
      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })

    it('应该在输入空字符串时不产生控制台错误', async () => {
      // 模拟用户清空输入
      wrapper.vm.demoColor = ''
      await wrapper.vm.$nextTick()
      
      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats
      
      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')
      
      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })

    it('应该在输入只有空格时不产生控制台错误', async () => {
      // 模拟用户输入空格
      wrapper.vm.demoColor = '   '
      await wrapper.vm.$nextTick()
      
      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats
      
      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')
      
      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })

    it('应该在输入过短的字符串时不产生控制台错误', async () => {
      // 模拟用户输入过短的字符串
      wrapper.vm.demoColor = 'a'
      await wrapper.vm.$nextTick()

      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats

      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')

      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })

    it('应该在输入无效的十六进制颜色时不产生控制台错误', async () => {
      // 模拟用户输入无效的十六进制颜色（7位字符）
      wrapper.vm.demoColor = '#1111221'
      await wrapper.vm.$nextTick()

      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats

      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')

      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })

    it('应该在输入包含无效字符的十六进制颜色时不产生控制台错误', async () => {
      // 模拟用户输入包含无效字符的十六进制颜色
      wrapper.vm.demoColor = '#gggggg'
      await wrapper.vm.$nextTick()

      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats

      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')

      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })

    it('应该在输入过长的十六进制颜色时不产生控制台错误', async () => {
      // 模拟用户输入过长的十六进制颜色
      wrapper.vm.demoColor = '#1234567890'
      await wrapper.vm.$nextTick()

      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats

      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')

      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })
  })

  describe('状态检查处理', () => {
    it('应该在颜色无效状态时返回默认格式', async () => {
      // 设置无效状态
      wrapper.vm.isValidColor = false
      wrapper.vm.demoColor = 'some-invalid-color'
      await wrapper.vm.$nextTick()
      
      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats
      
      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')
      
      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })

    it('应该在有错误信息时返回默认格式', async () => {
      // 设置错误状态
      wrapper.vm.colorError = '无效的颜色格式'
      wrapper.vm.demoColor = 'invalid-input'
      await wrapper.vm.$nextTick()
      
      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats
      
      // 验证返回默认格式
      expect(formats[0].value).toBe('#6366f1')
      
      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })
  })

  describe('异常处理', () => {
    it('应该正确处理无效颜色输入', async () => {
      // 现在使用 ColorParser 架构，无效输入会被 ColorParser 拒绝
      // 返回默认格式而不是错误格式，这是更好的用户体验
      wrapper.vm.isValidColor = true
      wrapper.vm.colorError = ''
      wrapper.vm.demoColor = 'invalid-color' // 这会被 ColorParser 拒绝
      await wrapper.vm.$nextTick()

      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats

      // 验证返回默认格式（ColorParser 架构的预期行为）
      expect(formats[0].value).toBe('#6366f1')

      // 验证没有控制台错误（因为 ColorParser 在 chroma-js 之前进行验证）
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })
  })

  describe('正常输入处理', () => {
    it('应该正确处理有效的颜色输入', async () => {
      // 设置有效状态和有效输入
      wrapper.vm.isValidColor = true
      wrapper.vm.colorError = ''
      wrapper.vm.demoColor = '#ff0000'
      await wrapper.vm.$nextTick()
      
      // 访问 demoFormats 计算属性
      const formats = wrapper.vm.demoFormats
      
      // 验证返回正确的转换结果
      expect(formats[0].value).toBe('#FF0000')
      
      // 验证没有控制台错误
      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('颜色转换错误')
      )
    })
  })
})
