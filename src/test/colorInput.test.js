/**
 * 颜色输入功能单元测试
 * 测试 LandingPage.vue 组件中的颜色输入和转换功能
 * 
 * 测试覆盖：
 * - ColorParser 集成
 * - chroma-js 颜色转换
 * - 错误处理
 * - 用户交互
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import LandingPage from '../components/LandingPage.vue'

// Mock chroma-js
vi.mock('chroma-js', () => {
  const mockChroma = vi.fn((color) => {
    // 模拟 chroma-js 的基本功能
    if (color === '#ff0000' || color === 'red' || color === 'rgb(255, 0, 0)') {
      return {
        hex: () => '#FF0000',
        rgb: () => [255, 0, 0],
        hsl: () => [0, 1, 0.5],
        lab: () => [53.2, 80.1, 67.2],
        cmyk: null, // 模拟不支持 CMYK，需要手动计算
        oklch: null // 模拟不支持 OKLCH
      }
    }
    if (color === '#6366f1') {
      return {
        hex: () => '#6366F1',
        rgb: () => [99, 102, 241],
        hsl: () => [239, 0.84, 0.67],
        lab: () => [52.5, 40.1, -61.3],
        cmyk: null,
        oklch: null
      }
    }
    if (color === 'invalid-color') {
      throw new Error('Invalid color')
    }
    // 默认返回蓝色
    return {
      hex: () => '#0000FF',
      rgb: () => [0, 0, 255],
      hsl: () => [240, 1, 0.5],
      lab: () => [32.3, 79.2, -107.9],
      cmyk: null,
      oklch: null
    }
  })
  
  return { default: mockChroma }
})

// Mock ColorParser
vi.mock('../scripts/ColorParser.js', () => {
  return {
    default: {
      parse: vi.fn((input) => {
        const cleanInput = input.toLowerCase().trim()
        
        if (cleanInput === '#ff0000' || cleanInput === 'ff0000') {
          return { mode: 'hex', value: '#ff0000' }
        }
        if (cleanInput === 'red') {
          return { mode: 'keyword', value: 'red', hex: '#ff0000' }
        }
        if (cleanInput === 'rgb(255, 0, 0)' || cleanInput === 'rgb(255,0,0)') {
          return { mode: 'rgb', value: 'rgb(255, 0, 0)' }
        }
        if (cleanInput === 'hsl(0, 100%, 50%)') {
          return { mode: 'hsl', value: 'hsl(0, 100%, 50%)' }
        }
        if (cleanInput === '#6366f1') {
          return { mode: 'hex', value: '#6366f1' }
        }
        if (cleanInput === 'invalid-color' || cleanInput === 'xyz(1,2,3)') {
          return { mode: 'unknown', value: input }
        }
        
        // 默认返回 unknown
        return { mode: 'unknown', value: input }
      })
    }
  }
})

describe('颜色输入功能测试', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(LandingPage)
  })

  describe('基本功能测试', () => {
    it('应该正确渲染颜色输入框', () => {
      const input = wrapper.find('.demo-color-input')
      expect(input.exists()).toBe(true)
      expect(input.attributes('placeholder')).toContain('输入颜色值')
    })

    it('应该显示默认颜色格式', () => {
      const outputs = wrapper.findAll('.demo-output')
      expect(outputs).toHaveLength(6)
      
      const formatLabels = outputs.map(output =>
        output.find('.format-label').text()
      )
      expect(formatLabels).toEqual(['HEX', 'RGB', 'HSL', 'LAB', 'CMYK', 'OKLCH'])
    })

    it('应该有默认的颜色值', () => {
      const input = wrapper.find('.demo-color-input')
      expect(input.element.value).toBe('#6366f1')
    })
  })

  describe('颜色格式检测测试', () => {
    it('应该正确检测 HEX 格式', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      const formatDetected = wrapper.find('.format-detected')
      expect(formatDetected.exists()).toBe(true)
      expect(formatDetected.text()).toContain('HEX')
    })

    it('应该正确检测颜色关键字', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('red')
      await input.trigger('blur')
      await nextTick()

      const formatDetected = wrapper.find('.format-detected')
      expect(formatDetected.exists()).toBe(true)
      expect(formatDetected.text()).toContain('KEYWORD')
    })

    it('应该正确检测 RGB 格式', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('rgb(255, 0, 0)')
      await input.trigger('blur')
      await nextTick()

      const formatDetected = wrapper.find('.format-detected')
      expect(formatDetected.exists()).toBe(true)
      expect(formatDetected.text()).toContain('RGB')
    })
  })

  describe('颜色转换测试', () => {
    it('应该正确转换 HEX 到其他格式', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      const outputs = wrapper.findAll('.demo-output')
      const hexOutput = outputs.find(output => 
        output.find('.format-label').text() === 'HEX'
      )
      const rgbOutput = outputs.find(output => 
        output.find('.format-label').text() === 'RGB'
      )

      expect(hexOutput.find('.format-value').text()).toBe('#FF0000')
      expect(rgbOutput.find('.format-value').text()).toBe('rgb(255, 0, 0)')
    })

    it('应该正确处理颜色关键字转换', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('red')
      await input.trigger('blur')
      await nextTick()

      const outputs = wrapper.findAll('.demo-output')
      const hexOutput = outputs.find(output => 
        output.find('.format-label').text() === 'HEX'
      )

      expect(hexOutput.find('.format-value').text()).toBe('#FF0000')
    })
  })

  describe('错误处理测试', () => {
    it('应该显示无效颜色的错误信息', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('invalid-color')
      await input.trigger('blur')
      await nextTick()

      const errorMessage = wrapper.find('.error-message')
      expect(errorMessage.exists()).toBe(true)
      expect(errorMessage.text()).toContain('无法识别的颜色格式')
      
      expect(input.classes()).toContain('error')
    })

    it('应该处理 chroma-js 解析错误', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 这个值会被 ColorParser 识别但 chroma-js 无法解析
      await input.setValue('xyz(1,2,3)')
      await input.trigger('blur')
      await nextTick()

      const errorMessage = wrapper.find('.error-message')
      expect(errorMessage.exists()).toBe(true)
    })

    it('应该在输入为空时恢复默认值', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('')
      await input.trigger('blur')
      await nextTick()

      expect(input.element.value).toBe('#6366f1')
      
      const formatDetected = wrapper.find('.format-detected')
      expect(formatDetected.exists()).toBe(true)
      expect(formatDetected.text()).toContain('HEX')
    })
  })

  describe('用户体验测试', () => {
    it('应该在成功时显示成功样式', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      expect(input.classes()).toContain('success')
      expect(input.classes()).not.toContain('error')
    })

    it('应该清除之前的错误状态', async () => {
      const input = wrapper.find('.demo-color-input')
      
      // 先输入无效颜色
      await input.setValue('invalid-color')
      await input.trigger('blur')
      await nextTick()

      expect(wrapper.find('.error-message').exists()).toBe(true)

      // 再输入有效颜色
      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      expect(wrapper.find('.error-message').exists()).toBe(false)
      expect(wrapper.find('.format-detected').exists()).toBe(true)
    })

    it('应该只在 blur 事件时处理颜色', async () => {
      const input = wrapper.find('.demo-color-input')
      
      await input.setValue('invalid-color')
      // 只触发 input 事件，不触发 blur
      await input.trigger('input')
      await nextTick()

      // 不应该显示错误信息
      expect(wrapper.find('.error-message').exists()).toBe(false)
    })
  })

  describe('边界情况测试', () => {
    it('应该处理带空格的输入', async () => {
      const input = wrapper.find('.demo-color-input')

      await input.setValue('  #ff0000  ')
      await input.trigger('blur')
      await nextTick()

      const formatDetected = wrapper.find('.format-detected')
      expect(formatDetected.exists()).toBe(true)
      expect(formatDetected.text()).toContain('HEX')
    })

    it('应该处理大小写混合的输入', async () => {
      const input = wrapper.find('.demo-color-input')

      await input.setValue('RED')
      await input.trigger('blur')
      await nextTick()

      const formatDetected = wrapper.find('.format-detected')
      expect(formatDetected.exists()).toBe(true)
      expect(formatDetected.text()).toContain('KEYWORD')
    })
  })

  describe('复制功能测试', () => {
    // Mock clipboard API
    const mockClipboard = {
      writeText: vi.fn()
    }

    beforeEach(() => {
      // 重置 clipboard mock
      mockClipboard.writeText.mockClear()
      Object.assign(navigator, {
        clipboard: mockClipboard
      })
      // 模拟安全上下文
      Object.defineProperty(window, 'isSecureContext', {
        value: true,
        writable: true
      })
    })

    it('应该显示复制图标', () => {
      const outputs = wrapper.findAll('.demo-output')
      expect(outputs.length).toBeGreaterThan(0)

      outputs.forEach(output => {
        const copyIcon = output.find('.copy-icon')
        expect(copyIcon.exists()).toBe(true)
      })
    })

    it('应该在点击时复制颜色值', async () => {
      const input = wrapper.find('.demo-color-input')

      // 设置有效颜色
      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      const hexOutput = wrapper.findAll('.demo-output').find(output =>
        output.find('.format-label').text() === 'HEX'
      )

      expect(hexOutput).toBeDefined()

      // 点击复制
      await hexOutput.trigger('click')
      await nextTick()

      // 验证 clipboard API 被调用
      expect(mockClipboard.writeText).toHaveBeenCalledWith('#FF0000')
    })

    it('应该显示复制成功状态', async () => {
      const input = wrapper.find('.demo-color-input')

      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      const hexOutput = wrapper.findAll('.demo-output').find(output =>
        output.find('.format-label').text() === 'HEX'
      )

      await hexOutput.trigger('click')
      await nextTick()

      // 验证复制成功状态
      expect(hexOutput.classes()).toContain('copied')

      const successIcon = hexOutput.find('.copy-icon')
      expect(successIcon.text()).toBe('✓')
    })

    it('应该为每种格式提供复制功能', async () => {
      const input = wrapper.find('.demo-color-input')

      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      const outputs = wrapper.findAll('.demo-output')

      for (const output of outputs) {
        const formatLabel = output.find('.format-label').text()
        const formatValue = output.find('.format-value').text()

        // 跳过转换失败的情况
        if (formatValue === '转换失败') continue

        await output.trigger('click')
        await nextTick()

        // 验证每种格式都能复制
        expect(mockClipboard.writeText).toHaveBeenCalledWith(formatValue)
      }
    })

    it('应该处理复制失败的情况', async () => {
      // 模拟复制失败
      mockClipboard.writeText.mockRejectedValue(new Error('复制失败'))

      const input = wrapper.find('.demo-color-input')

      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      const hexOutput = wrapper.findAll('.demo-output').find(output =>
        output.find('.format-label').text() === 'HEX'
      )

      await hexOutput.trigger('click')
      await nextTick()

      // 验证不显示复制成功状态
      expect(hexOutput.classes()).not.toContain('copied')
    })

    it('应该不复制转换失败的值', async () => {
      const input = wrapper.find('.demo-color-input')

      // 输入无效颜色
      await input.setValue('invalid-color')
      await input.trigger('blur')
      await nextTick()

      const outputs = wrapper.findAll('.demo-output')
      const failedOutput = outputs.find(output =>
        output.find('.format-value').text() === '转换失败'
      )

      if (failedOutput) {
        await failedOutput.trigger('click')
        await nextTick()

        // 验证不调用复制 API
        expect(mockClipboard.writeText).not.toHaveBeenCalledWith('转换失败')
        expect(failedOutput.classes()).not.toContain('copied')
      }
    })

    it('应该在不支持 Clipboard API 时使用降级方案', async () => {
      // 移除 Clipboard API 支持
      Object.assign(navigator, { clipboard: undefined })
      Object.defineProperty(window, 'isSecureContext', { value: false })

      // Mock document.execCommand
      const mockExecCommand = vi.fn().mockReturnValue(true)
      document.execCommand = mockExecCommand

      const input = wrapper.find('.demo-color-input')

      await input.setValue('#ff0000')
      await input.trigger('blur')
      await nextTick()

      const hexOutput = wrapper.findAll('.demo-output').find(output =>
        output.find('.format-label').text() === 'HEX'
      )

      await hexOutput.trigger('click')
      await nextTick()

      // 验证使用了降级方案
      expect(mockExecCommand).toHaveBeenCalledWith('copy')
      expect(hexOutput.classes()).toContain('copied')
    })
  })
})
