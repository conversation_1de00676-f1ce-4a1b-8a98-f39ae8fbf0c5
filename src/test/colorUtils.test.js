/**
 * 颜色转换工具函数测试
 */

import { describe, it, expect } from 'vitest'

// 从 LandingPage 组件中提取的颜色转换函数
function hexToRgb(hex) {
  const cleanHex = hex.replace('#', '')
  const fullHex = cleanHex.length === 3 
    ? cleanHex.split('').map(char => char + char).join('')
    : cleanHex
  
  const r = parseInt(fullHex.substring(0, 2), 16)
  const g = parseInt(fullHex.substring(2, 4), 16)
  const b = parseInt(fullHex.substring(4, 6), 16)
  
  return { r, g, b }
}

function rgbToHsl(r, g, b) {
  r /= 255
  g /= 255
  b /= 255
  
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h, s, l = (max + min) / 2
  
  if (max === min) {
    h = s = 0
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }
  
  return {
    h: h * 360,
    s: s * 100,
    l: l * 100
  }
}

describe('颜色转换工具函数', () => {
  describe('hexToRgb', () => {
    it('应该正确转换6位十六进制颜色', () => {
      const result = hexToRgb('#ff0000')
      expect(result).toEqual({ r: 255, g: 0, b: 0 })
    })

    it('应该正确转换3位十六进制颜色', () => {
      const result = hexToRgb('#f00')
      expect(result).toEqual({ r: 255, g: 0, b: 0 })
    })

    it('应该处理没有#前缀的颜色', () => {
      const result = hexToRgb('00ff00')
      expect(result).toEqual({ r: 0, g: 255, b: 0 })
    })
  })

  describe('rgbToHsl', () => {
    it('应该正确转换红色', () => {
      const result = rgbToHsl(255, 0, 0)
      expect(result.h).toBe(0)
      expect(result.s).toBe(100)
      expect(result.l).toBe(50)
    })

    it('应该正确转换绿色', () => {
      const result = rgbToHsl(0, 255, 0)
      expect(result.h).toBe(120)
      expect(result.s).toBe(100)
      expect(result.l).toBe(50)
    })

    it('应该正确转换蓝色', () => {
      const result = rgbToHsl(0, 0, 255)
      expect(result.h).toBe(240)
      expect(result.s).toBe(100)
      expect(result.l).toBe(50)
    })

    it('应该正确转换白色', () => {
      const result = rgbToHsl(255, 255, 255)
      expect(result.h).toBe(0)
      expect(result.s).toBe(0)
      expect(result.l).toBe(100)
    })

    it('应该正确转换黑色', () => {
      const result = rgbToHsl(0, 0, 0)
      expect(result.h).toBe(0)
      expect(result.s).toBe(0)
      expect(result.l).toBe(0)
    })
  })
})
