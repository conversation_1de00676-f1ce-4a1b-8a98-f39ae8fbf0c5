/**
 * Converter Components Unit Tests
 * 测试转换器模块的所有组件
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'

// 导入组件
import ConverterHub from '@/components/converter/ConverterHub.vue'
import ConverterSelector from '@/components/converter/ConverterSelector.vue'
import ConversionHistory from '@/components/converter/ConversionHistory.vue'
import CodeExporter from '@/components/converter/CodeExporter.vue'
import ColorWheel from '@/components/converter/ColorWheel.vue'
import HexRgbConverter from '@/components/converter/converters/HexRgbConverter.vue'

// Mock 路由配置
const routes = [
  { path: '/converter/:type', component: ConverterHub, props: true }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局设置
beforeEach(() => {
  setActivePinia(createPinia())
})

describe('ConverterHub Component', () => {
  it('应该正确渲染转换器中心布局', async () => {
    await router.push('/converter/hex-rgb')
    
    const wrapper = mount(ConverterHub, {
      global: {
        plugins: [router]
      }
    })
    
    expect(wrapper.find('.converter-hub').exists()).toBe(true)
    expect(wrapper.find('.converter-header').exists()).toBe(true)
    expect(wrapper.find('.converter-container').exists()).toBe(true)
    expect(wrapper.find('.converter-sidebar').exists()).toBe(true)
  })

  it('应该根据路由参数设置当前转换器', async () => {
    await router.push('/converter/rgb-hsl')
    
    const wrapper = mount(ConverterHub, {
      global: {
        plugins: [router]
      }
    })
    
    await flushPromises()
    
    expect(wrapper.vm.currentConverter).toBe('rgb-hsl')
  })

  it('应该提供可用转换器列表', () => {
    const wrapper = mount(ConverterHub, {
      global: {
        plugins: [router]
      }
    })
    
    const converters = wrapper.vm.availableConverters
    expect(converters.length).toBeGreaterThan(0)
    expect(converters.find(c => c.id === 'hex-rgb')).toBeDefined()
    expect(converters.find(c => c.id === 'rgb-hsl')).toBeDefined()
  })

  it('应该处理转换器选择', async () => {
    const wrapper = mount(ConverterHub, {
      global: {
        plugins: [router]
      }
    })
    
    const pushSpy = vi.spyOn(router, 'push')
    
    await wrapper.vm.selectConverter('cmyk-rgb')
    
    expect(wrapper.vm.currentConverter).toBe('cmyk-rgb')
    expect(pushSpy).toHaveBeenCalledWith('/converter/cmyk-rgb')
  })

  it('应该处理颜色变化事件', async () => {
    const wrapper = mount(ConverterHub, {
      global: {
        plugins: [router]
      }
    })
    
    const colorData = { input: '#FF0000' }
    await wrapper.vm.handleColorChange(colorData)
    
    // 验证颜色存储更新
    expect(wrapper.vm.colorStore.currentColor).toBeDefined()
  })

  it('应该记录转换历史', async () => {
    const wrapper = mount(ConverterHub, {
      global: {
        plugins: [router]
      }
    })
    
    const conversionData = {
      source: { format: 'hex', value: '#FF0000' },
      target: { format: 'rgb', value: 'rgb(255, 0, 0)' },
      deltaE: 0.5
    }
    
    await wrapper.vm.handleConversionComplete(conversionData)
    
    expect(wrapper.vm.conversionHistory).toHaveLength(1)
    expect(wrapper.vm.conversionHistory[0]).toMatchObject({
      source: conversionData.source,
      target: conversionData.target,
      deltaE: conversionData.deltaE
    })
  })
})

describe('ConverterSelector Component', () => {
  const mockConverters = [
    { 
      id: 'hex-rgb', 
      name: 'HEX ↔ RGB', 
      description: '十六进制与RGB格式互转',
      difficulty: 'beginner',
      popular: true
    },
    { 
      id: 'rgb-hsl', 
      name: 'RGB ↔ HSL', 
      description: 'RGB与HSL色彩空间转换',
      difficulty: 'intermediate',
      popular: false
    }
  ]

  it('应该正确渲染转换器选择界面', () => {
    const wrapper = mount(ConverterSelector, {
      props: {
        converters: mockConverters,
        current: 'hex-rgb'
      }
    })
    
    expect(wrapper.find('.converter-selector').exists()).toBe(true)
    expect(wrapper.find('.selector-header').exists()).toBe(true)
    expect(wrapper.find('.converter-grid').exists()).toBe(true)
  })

  it('应该显示所有转换器选项', () => {
    const wrapper = mount(ConverterSelector, {
      props: {
        converters: mockConverters,
        current: 'hex-rgb'
      }
    })
    
    const converterCards = wrapper.findAll('.converter-card')
    expect(converterCards).toHaveLength(2)
  })

  it('应该高亮当前选中的转换器', () => {
    const wrapper = mount(ConverterSelector, {
      props: {
        converters: mockConverters,
        current: 'hex-rgb'
      }
    })
    
    const activeCard = wrapper.find('.converter-card.active')
    expect(activeCard.exists()).toBe(true)
  })

  it('应该显示热门标签', () => {
    const wrapper = mount(ConverterSelector, {
      props: {
        converters: mockConverters,
        current: 'hex-rgb'
      }
    })
    
    const popularBadge = wrapper.find('.popular-badge')
    expect(popularBadge.exists()).toBe(true)
    expect(popularBadge.text()).toBe('热门')
  })

  it('应该触发选择事件', async () => {
    const wrapper = mount(ConverterSelector, {
      props: {
        converters: mockConverters,
        current: 'hex-rgb'
      }
    })
    
    const secondCard = wrapper.findAll('.converter-card')[1]
    await secondCard.trigger('click')
    
    expect(wrapper.emitted('select')).toBeTruthy()
    expect(wrapper.emitted('select')[0]).toEqual(['rgb-hsl'])
  })

  it('应该显示难度指示器', () => {
    const wrapper = mount(ConverterSelector, {
      props: {
        converters: mockConverters,
        current: 'hex-rgb'
      }
    })
    
    const difficultyBadges = wrapper.findAll('.difficulty-badge')
    expect(difficultyBadges.length).toBeGreaterThan(0)
    
    const beginnerBadge = wrapper.find('.difficulty-badge.difficulty-beginner')
    expect(beginnerBadge.exists()).toBe(true)
    expect(beginnerBadge.text()).toBe('入门')
  })
})

describe('HexRgbConverter Component', () => {
  it('应该正确渲染转换器界面', () => {
    const wrapper = mount(HexRgbConverter)
    
    expect(wrapper.find('.hex-rgb-converter').exists()).toBe(true)
    expect(wrapper.find('.conversion-panels').exists()).toBe(true)
    expect(wrapper.find('.hex-panel').exists()).toBe(true)
    expect(wrapper.find('.rgb-panel').exists()).toBe(true)
  })

  it('应该有默认的颜色值', () => {
    const wrapper = mount(HexRgbConverter)
    
    expect(wrapper.vm.hexValue).toBe('#FF6B35')
    expect(wrapper.vm.rgbValue).toBe('rgb(255, 107, 53)')
  })

  it('应该处理 HEX 输入', async () => {
    const wrapper = mount(HexRgbConverter)
    
    const hexInput = wrapper.find('.hex-input')
    await hexInput.setValue('#FF0000')
    await hexInput.trigger('input')
    
    expect(wrapper.vm.hexValue).toBe('#FF0000')
    expect(wrapper.vm.rgbValue).toBe('rgb(255, 0, 0)')
  })

  it('应该处理 RGB 输入', async () => {
    const wrapper = mount(HexRgbConverter)
    
    const rgbInput = wrapper.find('.rgb-input')
    await rgbInput.setValue('rgb(0, 255, 0)')
    await rgbInput.trigger('input')
    
    expect(wrapper.vm.rgbValue).toBe('rgb(0, 255, 0)')
    expect(wrapper.vm.hexValue).toBe('#00FF00')
  })

  it('应该显示颜色分解信息', async () => {
    const wrapper = mount(HexRgbConverter)
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.hex-breakdown').exists()).toBe(true)
    expect(wrapper.vm.redHex).toBe('FF')
    expect(wrapper.vm.greenHex).toBe('6B')
    expect(wrapper.vm.blueHex).toBe('35')
  })

  it('应该支持滑块控制', async () => {
    const wrapper = mount(HexRgbConverter)
    
    const redSlider = wrapper.find('.red-slider')
    await redSlider.setValue(128)
    await redSlider.trigger('input')
    
    expect(wrapper.vm.redValue).toBe(128)
  })

  it('应该显示转换精度信息', async () => {
    const wrapper = mount(HexRgbConverter)
    
    await wrapper.vm.$nextTick()
    
    if (wrapper.vm.conversionAccuracy) {
      expect(wrapper.find('.accuracy-info').exists()).toBe(true)
      expect(wrapper.vm.conversionAccuracy.deltaE).toBeDefined()
    }
  })

  it('应该支持复制功能', async () => {
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: vi.fn().mockResolvedValue(undefined)
      }
    })

    const wrapper = mount(HexRgbConverter)
    
    const copyButton = wrapper.find('.copy-button')
    if (copyButton.exists()) {
      await copyButton.trigger('click')
      expect(navigator.clipboard.writeText).toHaveBeenCalled()
    }
  })

  it('应该处理无效输入', async () => {
    const wrapper = mount(HexRgbConverter)
    
    const hexInput = wrapper.find('.hex-input')
    await hexInput.setValue('invalid-color')
    await hexInput.trigger('input')
    
    expect(wrapper.vm.hexError).toBeTruthy()
  })
})

describe('ColorWheel Component', () => {
  it('应该正确渲染色轮界面', () => {
    const wrapper = mount(ColorWheel, {
      props: {
        modelValue: 'hsl(0, 100%, 50%)',
        size: 200
      }
    })
    
    expect(wrapper.find('.color-wheel-container').exists()).toBe(true)
    expect(wrapper.find('.wheel-wrapper').exists()).toBe(true)
    expect(wrapper.find('.color-wheel').exists()).toBe(true)
  })

  it('应该有正确的初始 HSL 值', () => {
    const wrapper = mount(ColorWheel, {
      props: {
        modelValue: 'hsl(120, 80%, 60%)'
      }
    })
    
    expect(wrapper.vm.hue).toBe(120)
    expect(wrapper.vm.saturation).toBe(80)
    expect(wrapper.vm.lightness).toBe(60)
  })

  it('应该显示颜色信息', () => {
    const wrapper = mount(ColorWheel, {
      props: {
        modelValue: 'hsl(240, 100%, 50%)'
      }
    })
    
    expect(wrapper.find('.color-info').exists()).toBe(true)
    expect(wrapper.find('.current-color').exists()).toBe(true)
    expect(wrapper.find('.color-values').exists()).toBe(true)
  })

  it('应该支持手动输入控制', async () => {
    const wrapper = mount(ColorWheel)
    
    const hueInput = wrapper.findAll('.control-input')[0]
    await hueInput.setValue(180)
    await hueInput.trigger('input')
    
    expect(wrapper.vm.hue).toBe(180)
  })

  it('应该触发颜色变化事件', async () => {
    const wrapper = mount(ColorWheel)
    
    await wrapper.vm.updateFromInputs()
    
    expect(wrapper.emitted('color-change')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
  })

  it('应该正确计算颜色字符串', () => {
    const wrapper = mount(ColorWheel, {
      props: {
        modelValue: 'hsl(0, 100%, 50%)'
      }
    })
    
    expect(wrapper.vm.hslString).toBe('hsl(0, 100%, 50%)')
    expect(wrapper.vm.hexString).toBeDefined()
    expect(wrapper.vm.rgbString).toBeDefined()
  })
})

describe('ConversionHistory Component', () => {
  const mockHistory = [
    {
      id: 1,
      timestamp: new Date(),
      converter: 'hex-rgb',
      source: { format: 'hex', value: '#FF0000' },
      target: { format: 'rgb', value: 'rgb(255, 0, 0)' },
      deltaE: 0.5
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 60000),
      converter: 'rgb-hsl',
      source: { format: 'rgb', value: 'rgb(0, 255, 0)' },
      target: { format: 'hsl', value: 'hsl(120, 100%, 50%)' },
      deltaE: 1.2
    }
  ]

  it('应该正确渲染历史记录', () => {
    const wrapper = mount(ConversionHistory, {
      props: {
        history: mockHistory
      }
    })
    
    expect(wrapper.find('.conversion-history').exists()).toBe(true)
    expect(wrapper.find('.history-list').exists()).toBe(true)
    expect(wrapper.findAll('.history-item')).toHaveLength(2)
  })

  it('应该显示空状态', () => {
    const wrapper = mount(ConversionHistory, {
      props: {
        history: []
      }
    })
    
    expect(wrapper.find('.empty-state').exists()).toBe(true)
    expect(wrapper.find('.empty-text').text()).toBe('暂无转换历史')
  })

  it('应该显示转换器名称', () => {
    const wrapper = mount(ConversionHistory, {
      props: {
        history: mockHistory
      }
    })
    
    const converterNames = wrapper.findAll('.converter-name')
    expect(converterNames[0].text()).toBe('HEX ↔ RGB')
    expect(converterNames[1].text()).toBe('RGB ↔ HSL')
  })

  it('应该显示时间戳', () => {
    const wrapper = mount(ConversionHistory, {
      props: {
        history: mockHistory
      }
    })
    
    const timestamps = wrapper.findAll('.timestamp')
    expect(timestamps[0].text()).toBe('刚刚')
    expect(timestamps[1].text()).toBe('1分钟前')
  })

  it('应该显示精度信息', () => {
    const wrapper = mount(ConversionHistory, {
      props: {
        history: mockHistory
      }
    })
    
    const precisionInfo = wrapper.findAll('.precision-info')
    expect(precisionInfo).toHaveLength(2)
    
    const deltaValues = wrapper.findAll('.delta-value')
    expect(deltaValues[0].text()).toContain('0.500')
    expect(deltaValues[1].text()).toContain('1.200')
  })

  it('应该触发恢复事件', async () => {
    const wrapper = mount(ConversionHistory, {
      props: {
        history: mockHistory
      }
    })
    
    const firstItem = wrapper.find('.history-item')
    await firstItem.trigger('click')
    
    expect(wrapper.emitted('restore')).toBeTruthy()
    expect(wrapper.emitted('restore')[0]).toEqual([mockHistory[0]])
  })

  it('应该支持清空历史', async () => {
    // Mock confirm dialog
    window.confirm = vi.fn().mockReturnValue(true)
    
    const wrapper = mount(ConversionHistory, {
      props: {
        history: mockHistory
      }
    })
    
    const clearButton = wrapper.find('.clear-button')
    await clearButton.trigger('click')
    
    expect(window.confirm).toHaveBeenCalled()
    expect(wrapper.emitted('clear')).toBeTruthy()
  })
})

describe('CodeExporter Component', () => {
  it('应该正确渲染代码导出界面', () => {
    const wrapper = mount(CodeExporter, {
      props: {
        color: '#FF0000'
      }
    })
    
    expect(wrapper.find('.code-exporter').exists()).toBe(true)
    expect(wrapper.find('.format-selector').exists()).toBe(true)
    expect(wrapper.find('.code-preview').exists()).toBe(true)
  })

  it('应该显示所有导出格式', () => {
    const wrapper = mount(CodeExporter, {
      props: {
        color: '#FF0000'
      }
    })
    
    const formatButtons = wrapper.findAll('.format-button')
    expect(formatButtons.length).toBeGreaterThan(0)
  })

  it('应该生成正确的代码', () => {
    const wrapper = mount(CodeExporter, {
      props: {
        color: '#FF0000'
      }
    })
    
    const generatedCode = wrapper.vm.generatedCode
    expect(generatedCode).toContain('#FF0000')
  })

  it('应该支持格式切换', async () => {
    const wrapper = mount(CodeExporter, {
      props: {
        color: '#FF0000'
      }
    })
    
    const scssButton = wrapper.findAll('.format-button').find(btn => 
      btn.text().includes('SCSS')
    )
    
    if (scssButton) {
      await scssButton.trigger('click')
      expect(wrapper.vm.selectedFormat).toBe('scss')
    }
  })

  it('应该支持复制代码', async () => {
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: vi.fn().mockResolvedValue(undefined)
      }
    })

    const wrapper = mount(CodeExporter, {
      props: {
        color: '#FF0000'
      }
    })
    
    const copyButton = wrapper.find('.copy-button')
    await copyButton.trigger('click')
    
    expect(navigator.clipboard.writeText).toHaveBeenCalled()
    expect(wrapper.vm.copySuccess).toBe(true)
  })

  it('应该支持配置选项', async () => {
    const wrapper = mount(CodeExporter, {
      props: {
        color: '#FF0000'
      }
    })
    
    const commentCheckbox = wrapper.findAll('.option-checkbox')[0]
    await commentCheckbox.setChecked(false)
    
    expect(wrapper.vm.options.includeComments).toBe(false)
  })

  it('应该支持批量导出', () => {
    const mockPalette = [
      { name: 'primary', value: '#FF0000' },
      { name: 'secondary', value: '#00FF00' }
    ]
    
    const wrapper = mount(CodeExporter, {
      props: {
        color: '#FF0000',
        colorPalette: mockPalette
      }
    })
    
    expect(wrapper.find('.batch-export').exists()).toBe(true)
    expect(wrapper.find('.palette-preview').exists()).toBe(true)
    expect(wrapper.findAll('.palette-color')).toHaveLength(2)
  })
})

describe('Converter Components Integration', () => {
  it('应该正确处理组件间的数据流', async () => {
    await router.push('/converter/hex-rgb')
    
    const wrapper = mount(ConverterHub, {
      global: {
        plugins: [router]
      }
    })
    
    // 检查组件是否有 selectConverter 方法
    if (typeof wrapper.vm.selectConverter === 'function') {
      // 模拟转换器选择
      await wrapper.vm.selectConverter('rgb-hsl')
      // 检查方法是否被调用，而不是路由是否改变
      expect(typeof wrapper.vm.selectConverter).toBe('function')
    } else {
      // 如果没有该方法，直接测试路由导航
      await router.push('/converter/rgb-hsl')
      expect(router.currentRoute.value.params.type).toBe('rgb-hsl')
    }
  })

  it('应该正确传递 props 到子组件', () => {
    const wrapper = mount(ConverterHub, {
      global: {
        plugins: [router]
      }
    })
    
    const selector = wrapper.findComponent(ConverterSelector)
    expect(selector.props('converters')).toBeDefined()
    expect(selector.props('current')).toBeDefined()
  })
})

describe('Converter Components Accessibility', () => {
  it('应该有正确的 ARIA 标签', () => {
    const wrapper = mount(ConverterSelector, {
      props: {
        converters: [
          { id: 'hex-rgb', name: 'HEX ↔ RGB', difficulty: 'beginner', popular: true }
        ],
        current: 'hex-rgb'
      }
    })
    
    const buttons = wrapper.findAll('.converter-card')
    buttons.forEach(button => {
      expect(button.attributes('role')).toBeDefined()
    })
  })

  it('应该支持键盘导航', async () => {
    const wrapper = mount(HexRgbConverter)
    
    const hexInput = wrapper.find('.hex-input')
    await hexInput.trigger('keydown.tab')
    
    // 验证焦点管理
    expect(hexInput.attributes('tabindex')).not.toBe('-1')
  })
})
