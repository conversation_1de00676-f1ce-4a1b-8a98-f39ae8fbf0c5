/**
 * Enhanced ColorUtils Tests
 * 测试扩展的颜色工具函数库
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { ColorUtils } from '@/utils/colorUtils.js'

describe('ColorUtils', () => {
  describe('calculateDeltaE', () => {
    it('should calculate Delta E between identical colors', () => {
      const deltaE = ColorUtils.calculateDeltaE('#ff0000', '#ff0000')
      expect(deltaE).toBe(0)
    })

    it('should calculate Delta E between different colors', () => {
      const deltaE = ColorUtils.calculateDeltaE('#ff0000', '#00ff00')
      expect(deltaE).toBeGreaterThan(0)
      expect(deltaE).toBeLessThan(200) // 合理范围
    })

    it('should handle invalid colors gracefully', () => {
      const deltaE = ColorUtils.calculateDeltaE('invalid', '#ff0000')
      expect(deltaE).toBe(0)
    })
  })

  describe('generateColorScale', () => {
    it('should generate default 9-step color scale', () => {
      const scale = ColorUtils.generateColorScale('#3b82f6')
      expect(scale).toHaveLength(9)
      expect(scale[0].level).toBe(100)
      expect(scale[8].level).toBe(900)
    })

    it('should generate custom step color scale', () => {
      const scale = ColorUtils.generateColorScale('#3b82f6', 5)
      expect(scale).toHaveLength(5)
      expect(scale[0].level).toBe(100)
      expect(scale[4].level).toBe(500)
    })

    it('should include all required properties', () => {
      const scale = ColorUtils.generateColorScale('#3b82f6', 3)
      scale.forEach(color => {
        expect(color).toHaveProperty('level')
        expect(color).toHaveProperty('hex')
        expect(color).toHaveProperty('rgb')
        expect(color).toHaveProperty('hsl')
        expect(color.hex).toMatch(/^#[0-9a-f]{6}$/i)
      })
    })

    it('should handle invalid colors', () => {
      const scale = ColorUtils.generateColorScale('invalid')
      expect(scale).toEqual([])
    })
  })

  describe('toOklch', () => {
    it('should convert valid color to OKLCH', () => {
      const oklch = ColorUtils.toOklch('#ff0000')
      expect(oklch).toMatch(/^oklch\([\d.]+\s+[\d.]+\s+[\d.]+\)$/)
    })

    it('should handle invalid colors', () => {
      const oklch = ColorUtils.toOklch('invalid')
      expect(oklch).toBeNull()
    })
  })

  describe('checkWCAGContrast', () => {
    it('should check high contrast colors', () => {
      const result = ColorUtils.checkWCAGContrast('#000000', '#ffffff')
      expect(result.ratio).toBeGreaterThan(20) // 黑白对比度很高
      expect(result.aa).toBe(true)
      expect(result.aaa).toBe(true)
      expect(result.level).toBe('AAA')
    })

    it('should check low contrast colors', () => {
      const result = ColorUtils.checkWCAGContrast('#888888', '#999999')
      expect(result.ratio).toBeLessThan(4.5)
      expect(result.aa).toBe(false)
      expect(result.aaa).toBe(false)
      expect(result.level).toBe('Fail')
    })

    it('should handle invalid colors', () => {
      const result = ColorUtils.checkWCAGContrast('invalid', '#ffffff')
      expect(result.level).toBe('Error')
      expect(result.ratio).toBe(0)
    })
  })

  describe('generateColorScheme', () => {
    it('should generate complementary scheme', () => {
      const scheme = ColorUtils.generateColorScheme('#ff0000', 'complementary')
      expect(scheme).toHaveLength(2)
      expect(scheme[0]).toBe('#FF0000')
      expect(scheme[1]).toMatch(/^#[0-9a-f]{6}$/i)
    })

    it('should generate triadic scheme', () => {
      const scheme = ColorUtils.generateColorScheme('#ff0000', 'triadic')
      expect(scheme).toHaveLength(3)
      expect(scheme[0]).toBe('#FF0000')
    })

    it('should generate analogous scheme', () => {
      const scheme = ColorUtils.generateColorScheme('#ff0000', 'analogous')
      expect(scheme).toHaveLength(3)
      expect(scheme[1]).toBe('#FF0000') // 基础色在中间
    })

    it('should generate monochromatic scheme', () => {
      const scheme = ColorUtils.generateColorScheme('#ff0000', 'monochromatic')
      expect(scheme).toHaveLength(3)
      expect(scheme[1]).toBe('#FF0000') // 基础色在中间
    })

    it('should handle invalid scheme type', () => {
      const scheme = ColorUtils.generateColorScheme('#ff0000', 'invalid')
      expect(scheme).toEqual(['#FF0000'])
    })

    it('should handle invalid colors', () => {
      const scheme = ColorUtils.generateColorScheme('invalid', 'complementary')
      expect(scheme).toEqual([])
    })
  })

  describe('exportCSSVariables', () => {
    it('should export CSS variables with default prefix', () => {
      const colors = { primary: '#3b82f6', secondary: '#10b981' }
      const css = ColorUtils.exportCSSVariables(colors)
      expect(css).toContain(':root {')
      expect(css).toContain('--color-primary: #3b82f6;')
      expect(css).toContain('--color-secondary: #10b981;')
      expect(css).toContain('}')
    })

    it('should export CSS variables with custom prefix', () => {
      const colors = { primary: '#3b82f6' }
      const css = ColorUtils.exportCSSVariables(colors, 'theme')
      expect(css).toContain('--theme-primary: #3b82f6;')
    })
  })

  describe('exportTailwindConfig', () => {
    it('should export Tailwind config format', () => {
      const colors = { primary: '#3b82f6', secondary: '#10b981' }
      const config = ColorUtils.exportTailwindConfig(colors)
      expect(config).toContain('module.exports = {')
      expect(config).toContain('theme: {')
      expect(config).toContain('extend: {')
      expect(config).toContain('colors:')
      expect(config).toContain('"primary": "#3b82f6"')
    })
  })

  describe('getLuminance', () => {
    it('should calculate luminance for white', () => {
      const luminance = ColorUtils.getLuminance('#ffffff')
      expect(luminance).toBeCloseTo(1, 1)
    })

    it('should calculate luminance for black', () => {
      const luminance = ColorUtils.getLuminance('#000000')
      expect(luminance).toBeCloseTo(0, 1)
    })

    it('should handle invalid colors', () => {
      const luminance = ColorUtils.getLuminance('invalid')
      expect(luminance).toBe(0)
    })
  })

  describe('isDark', () => {
    it('should identify dark colors', () => {
      expect(ColorUtils.isDark('#000000')).toBe(true)
      expect(ColorUtils.isDark('#333333')).toBe(true)
    })

    it('should identify light colors', () => {
      expect(ColorUtils.isDark('#ffffff')).toBe(false)
      expect(ColorUtils.isDark('#cccccc')).toBe(false)
    })
  })

  describe('getBestTextColor', () => {
    it('should return white for dark backgrounds', () => {
      expect(ColorUtils.getBestTextColor('#000000')).toBe('#ffffff')
      expect(ColorUtils.getBestTextColor('#333333')).toBe('#ffffff')
    })

    it('should return black for light backgrounds', () => {
      expect(ColorUtils.getBestTextColor('#ffffff')).toBe('#000000')
      expect(ColorUtils.getBestTextColor('#cccccc')).toBe('#000000')
    })
  })

  describe('mixColors', () => {
    it('should mix colors with default ratio', () => {
      const mixed = ColorUtils.mixColors('#ff0000', '#0000ff')
      expect(mixed).toMatch(/^#[0-9a-f]{6}$/i)
      expect(mixed).not.toBe('#ff0000')
      expect(mixed).not.toBe('#0000ff')
    })

    it('should mix colors with custom ratio', () => {
      const mixed = ColorUtils.mixColors('#ff0000', '#0000ff', 0.8)
      expect(mixed).toMatch(/^#[0-9a-f]{6}$/i)
    })

    it('should handle invalid colors', () => {
      const mixed = ColorUtils.mixColors('invalid', '#0000ff')
      expect(mixed).toBe('invalid')
    })
  })

  describe('adjustBrightness', () => {
    it('should brighten colors', () => {
      const brighter = ColorUtils.adjustBrightness('#808080', 1)
      expect(brighter).toMatch(/^#[0-9a-f]{6}$/i)
      expect(brighter).not.toBe('#808080')
    })

    it('should darken colors', () => {
      const darker = ColorUtils.adjustBrightness('#808080', -1)
      expect(darker).toMatch(/^#[0-9a-f]{6}$/i)
      expect(darker).not.toBe('#808080')
    })

    it('should handle invalid colors', () => {
      const result = ColorUtils.adjustBrightness('invalid', 1)
      expect(result).toBe('invalid')
    })
  })

  describe('adjustSaturation', () => {
    it('should increase saturation', () => {
      const saturated = ColorUtils.adjustSaturation('#808080', 1)
      expect(saturated).toMatch(/^#[0-9a-f]{6}$/i)
    })

    it('should decrease saturation', () => {
      const desaturated = ColorUtils.adjustSaturation('#ff0000', -1)
      expect(desaturated).toMatch(/^#[0-9a-f]{6}$/i)
    })

    it('should handle invalid colors', () => {
      const result = ColorUtils.adjustSaturation('invalid', 1)
      expect(result).toBe('invalid')
    })
  })

  describe('isValidColor', () => {
    it('should validate correct color formats', () => {
      expect(ColorUtils.isValidColor('#ff0000')).toBe(true)
      expect(ColorUtils.isValidColor('rgb(255, 0, 0)')).toBe(true)
      expect(ColorUtils.isValidColor('hsl(0, 100%, 50%)')).toBe(true)
      expect(ColorUtils.isValidColor('red')).toBe(true)
    })

    it('should reject invalid color formats', () => {
      expect(ColorUtils.isValidColor('invalid')).toBe(false)
      expect(ColorUtils.isValidColor('#gggggg')).toBe(false)
      expect(ColorUtils.isValidColor('rgb(300, 0, 0)')).toBe(false)
    })
  })

  describe('getColorTemperature', () => {
    it('should identify warm colors', () => {
      expect(ColorUtils.getColorTemperature('#ff0000')).toBe('warm') // 红色
      expect(ColorUtils.getColorTemperature('#ffa500')).toBe('warm') // 橙色
      expect(ColorUtils.getColorTemperature('#ff00ff')).toBe('warm') // 品红
    })

    it('should identify cool colors', () => {
      expect(ColorUtils.getColorTemperature('#0000ff')).toBe('cool') // 蓝色
      expect(ColorUtils.getColorTemperature('#00ffff')).toBe('cool') // 青色
      expect(ColorUtils.getColorTemperature('#008000')).toBe('cool') // 绿色
    })

    it('should identify neutral colors', () => {
      expect(ColorUtils.getColorTemperature('#ffff00')).toBe('neutral') // 黄色
    })

    it('should handle invalid colors', () => {
      expect(ColorUtils.getColorTemperature('invalid')).toBe('neutral')
    })
  })
})
