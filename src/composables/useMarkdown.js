/**
 * useMarkdown Composable - Markdown 渲染组合式函数
 * 提供 Markdown 内容渲染和语法高亮功能
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

import { ref } from 'vue'
import MarkdownIt from 'markdown-it'
import Prism from 'prismjs'

// 按需导入语言支持
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-json'
import 'prismjs/components/prism-scss'
import 'prismjs/components/prism-typescript'
import 'prismjs/components/prism-bash'

// 导入主题样式
import 'prismjs/themes/prism.css'

// 创建 Markdown-it 实例
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && Prism.languages[lang]) {
      try {
        return `<pre class="language-${lang}"><code class="language-${lang}">${
          Prism.highlight(str, Prism.languages[lang], lang)
        }</code></pre>`
      } catch (error) {
        console.warn('Syntax highlighting failed:', error)
      }
    }
    return `<pre><code>${md.utils.escapeHtml(str)}</code></pre>`
  }
})

// 添加自定义规则
md.use(function(md) {
  // 自定义链接渲染，添加 target="_blank"
  const defaultRender = md.renderer.rules.link_open || function(tokens, idx, options, env, renderer) {
    return renderer.renderToken(tokens, idx, options)
  }

  md.renderer.rules.link_open = function (tokens, idx, options, env, renderer) {
    const token = tokens[idx]
    const href = token.attrGet('href')
    
    // 外部链接添加 target="_blank"
    if (href && (href.startsWith('http') || href.startsWith('https'))) {
      token.attrSet('target', '_blank')
      token.attrSet('rel', 'noopener noreferrer')
    }
    
    return defaultRender(tokens, idx, options, env, renderer)
  }

  // 自定义表格渲染，添加响应式类
  const defaultTableRender = md.renderer.rules.table_open || function(tokens, idx, options, env, renderer) {
    return renderer.renderToken(tokens, idx, options)
  }

  md.renderer.rules.table_open = function (tokens, idx, options, env, renderer) {
    tokens[idx].attrSet('class', 'markdown-table')
    return defaultTableRender(tokens, idx, options, env, renderer)
  }
})

/**
 * useMarkdown 组合式函数
 * @returns {Object} Markdown 相关的响应式数据和方法
 */
export function useMarkdown() {
  const isLoading = ref(false)
  const error = ref(null)

  /**
   * 渲染 Markdown 内容
   * @param {string} content - Markdown 内容
   * @returns {string} 渲染后的 HTML
   */
  const renderMarkdown = (content) => {
    try {
      error.value = null
      return md.render(content)
    } catch (err) {
      error.value = err
      console.error('Markdown rendering failed:', err)
      return `<p class="error">渲染失败: ${err.message}</p>`
    }
  }

  /**
   * 从文件加载 Markdown 内容并渲染
   * @param {string} path - 文件路径
   * @returns {Promise<string>} 渲染后的 HTML
   */
  const loadMarkdownFile = async (path) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await fetch(path)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const content = await response.text()
      return renderMarkdown(content)
    } catch (err) {
      error.value = err
      console.error('Failed to load markdown file:', err)
      return `<p class="error">加载失败: ${err.message}</p>`
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 渲染内联 Markdown（不包含块级元素）
   * @param {string} content - Markdown 内容
   * @returns {string} 渲染后的 HTML
   */
  const renderInlineMarkdown = (content) => {
    try {
      error.value = null
      return md.renderInline(content)
    } catch (err) {
      error.value = err
      console.error('Inline markdown rendering failed:', err)
      return content
    }
  }

  /**
   * 提取 Markdown 内容的目录结构
   * @param {string} content - Markdown 内容
   * @returns {Array} 目录结构数组
   */
  const extractTableOfContents = (content) => {
    try {
      const tokens = md.parse(content, {})
      const toc = []
      let currentLevel = 0

      tokens.forEach((token, index) => {
        if (token.type === 'heading_open') {
          const level = parseInt(token.tag.substring(1))
          const nextToken = tokens[index + 1]
          
          if (nextToken && nextToken.type === 'inline') {
            const title = nextToken.content
            const anchor = title.toLowerCase()
              .replace(/[^\w\s-]/g, '')
              .replace(/\s+/g, '-')
            
            toc.push({
              level,
              title,
              anchor,
              children: []
            })
          }
        }
      })

      return toc
    } catch (err) {
      console.error('TOC extraction failed:', err)
      return []
    }
  }

  /**
   * 高亮代码块
   * @param {string} code - 代码内容
   * @param {string} language - 编程语言
   * @returns {string} 高亮后的 HTML
   */
  const highlightCode = (code, language = 'javascript') => {
    try {
      if (Prism.languages[language]) {
        return Prism.highlight(code, Prism.languages[language], language)
      }
      return code
    } catch (err) {
      console.error('Code highlighting failed:', err)
      return code
    }
  }

  /**
   * 验证 Markdown 语法
   * @param {string} content - Markdown 内容
   * @returns {Object} 验证结果
   */
  const validateMarkdown = (content) => {
    try {
      const tokens = md.parse(content, {})
      const warnings = []
      
      // 检查常见问题
      tokens.forEach((token, index) => {
        // 检查空的标题
        if (token.type === 'heading_open') {
          const nextToken = tokens[index + 1]
          if (!nextToken || nextToken.type !== 'inline' || !nextToken.content.trim()) {
            warnings.push({
              line: token.map ? token.map[0] + 1 : 0,
              message: '空标题'
            })
          }
        }
        
        // 检查空的链接
        if (token.type === 'link_open') {
          const href = token.attrGet('href')
          if (!href || href.trim() === '') {
            warnings.push({
              line: token.map ? token.map[0] + 1 : 0,
              message: '空链接'
            })
          }
        }
      })
      
      return {
        valid: warnings.length === 0,
        warnings,
        tokenCount: tokens.length
      }
    } catch (err) {
      return {
        valid: false,
        warnings: [{ line: 0, message: `语法错误: ${err.message}` }],
        tokenCount: 0
      }
    }
  }

  /**
   * 获取 Markdown 统计信息
   * @param {string} content - Markdown 内容
   * @returns {Object} 统计信息
   */
  const getMarkdownStats = (content) => {
    try {
      const tokens = md.parse(content, {})
      const stats = {
        characters: content.length,
        words: content.split(/\s+/).filter(word => word.length > 0).length,
        lines: content.length === 0 ? 0 : content.split('\n').length,
        headings: 0,
        links: 0,
        images: 0,
        codeBlocks: 0,
        tables: 0
      }

      tokens.forEach(token => {
        switch (token.type) {
          case 'heading_open':
            stats.headings++
            break
          case 'link_open':
            stats.links++
            break
          case 'image':
            stats.images++
            break
          case 'code_block':
          case 'fence':
            stats.codeBlocks++
            break
          case 'table_open':
            stats.tables++
            break
        }
      })

      return stats
    } catch (err) {
      console.error('Stats calculation failed:', err)
      return {
        characters: 0,
        words: 0,
        lines: 0,
        headings: 0,
        links: 0,
        images: 0,
        codeBlocks: 0,
        tables: 0
      }
    }
  }

  return {
    // 响应式状态
    isLoading,
    error,
    
    // 方法
    renderMarkdown,
    loadMarkdownFile,
    renderInlineMarkdown,
    extractTableOfContents,
    highlightCode,
    validateMarkdown,
    getMarkdownStats
  }
}
