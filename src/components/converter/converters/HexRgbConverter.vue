<!--
  HexRgbConverter.vue - HEX ↔ RGB 双向转换器
  提供 HEX 和 RGB 格式之间的精确转换
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="hex-rgb-converter">
    <!-- 转换器标题 -->
    <header class="converter-header">
      <h2 class="converter-title">HEX ↔ RGB 转换器</h2>
      <p class="converter-description">
        在十六进制 (HEX) 和红绿蓝 (RGB) 颜色格式之间进行精确转换
      </p>
    </header>

    <!-- 转换面板 -->
    <div class="conversion-panels">
      <!-- HEX 输入面板 -->
      <div class="input-panel hex-panel">
        <div class="panel-header">
          <h3 class="panel-title">HEX 格式</h3>
          <span class="format-info">#RRGGBB</span>
        </div>
        
        <div class="input-group">
          <label class="input-label">十六进制颜色值</label>
          <div class="input-container">
            <input
              v-model="hexValue"
              type="text"
              placeholder="#FF6B35"
              class="color-input hex-input"
              :class="{ 'error': hexError }"
              @input="handleHexInput"
              @paste="handlePaste"
            />
            <button
              v-if="hexValue"
              @click="copyToClipboard(hexValue)"
              class="copy-button"
              title="复制 HEX 值"
            >
              📋
            </button>
          </div>
          <div v-if="hexError" class="error-message">
            {{ hexError }}
          </div>
        </div>

        <!-- HEX 分解显示 -->
        <div v-if="isValidHex" class="hex-breakdown">
          <div class="breakdown-title">颜色分解</div>
          <div class="hex-components">
            <div class="component red">
              <span class="component-label">红色 (R)</span>
              <span class="component-hex">{{ redHex }}</span>
              <span class="component-decimal">{{ redDecimal }}</span>
            </div>
            <div class="component green">
              <span class="component-label">绿色 (G)</span>
              <span class="component-hex">{{ greenHex }}</span>
              <span class="component-decimal">{{ greenDecimal }}</span>
            </div>
            <div class="component blue">
              <span class="component-label">蓝色 (B)</span>
              <span class="component-hex">{{ blueHex }}</span>
              <span class="component-decimal">{{ blueDecimal }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 转换箭头 -->
      <div class="conversion-arrow">
        <button
          @click="swapValues"
          class="swap-button"
          title="交换输入输出"
        >
          ⇄
        </button>
      </div>

      <!-- RGB 输入面板 -->
      <div class="input-panel rgb-panel">
        <div class="panel-header">
          <h3 class="panel-title">RGB 格式</h3>
          <span class="format-info">rgb(R, G, B)</span>
        </div>
        
        <div class="input-group">
          <label class="input-label">RGB 颜色值</label>
          <div class="input-container">
            <input
              v-model="rgbValue"
              type="text"
              placeholder="rgb(255, 107, 53)"
              class="color-input rgb-input"
              :class="{ 'error': rgbError }"
              @input="handleRgbInput"
              @paste="handlePaste"
            />
            <button
              v-if="rgbValue"
              @click="copyToClipboard(rgbValue)"
              class="copy-button"
              title="复制 RGB 值"
            >
              📋
            </button>
          </div>
          <div v-if="rgbError" class="error-message">
            {{ rgbError }}
          </div>
        </div>

        <!-- RGB 滑块控制 -->
        <div v-if="isValidRgb" class="rgb-sliders">
          <div class="slider-title">通道调节</div>
          <div class="slider-group">
            <div class="slider-item">
              <label class="slider-label">红色: {{ redValue }}</label>
              <input
                v-model.number="redValue"
                type="range"
                min="0"
                max="255"
                class="color-slider red-slider"
                @input="updateFromSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">绿色: {{ greenValue }}</label>
              <input
                v-model.number="greenValue"
                type="range"
                min="0"
                max="255"
                class="color-slider green-slider"
                @input="updateFromSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">蓝色: {{ blueValue }}</label>
              <input
                v-model.number="blueValue"
                type="range"
                min="0"
                max="255"
                class="color-slider blue-slider"
                @input="updateFromSliders"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 颜色预览 -->
    <div v-if="currentColor" class="color-preview-section">
      <h3 class="preview-title">颜色预览</h3>
      <div class="preview-container">
        <ColorPreview 
          :color="currentColor" 
          :show-contrast="true"
          :show-luminance="true"
          size="large"
        />
        <div class="preview-info">
          <div class="info-item">
            <span class="info-label">当前颜色:</span>
            <span class="info-value">{{ currentColor }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">亮度:</span>
            <span class="info-value">{{ luminancePercent }}%</span>
          </div>
          <div class="info-item">
            <span class="info-label">对比度 (白色):</span>
            <span class="info-value">{{ contrastRatio }}:1</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 转换精度信息 -->
    <div v-if="conversionAccuracy" class="accuracy-info">
      <h3 class="accuracy-title">转换精度</h3>
      <div class="accuracy-details">
        <div class="accuracy-item">
          <span class="accuracy-label">Delta E:</span>
          <span class="accuracy-value">{{ conversionAccuracy.deltaE }}</span>
          <span class="accuracy-rating" :class="accuracyClass">
            {{ accuracyRating }}
          </span>
        </div>
        <div class="accuracy-description">
          {{ accuracyDescription }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import { ColorUtils } from '@/utils/colorUtils'
import ColorParser from '@/scripts/ColorParser.js'
import ColorPreview from '@/components/common/ColorPreview.vue'

const emit = defineEmits(['color-change', 'conversion-complete'])

const colorStore = useColorStore()

// 响应式数据
const hexValue = ref('#FF6B35')
const rgbValue = ref('rgb(255, 107, 53)')
const hexError = ref('')
const rgbError = ref('')

// RGB 滑块值
const redValue = ref(255)
const greenValue = ref(107)
const blueValue = ref(53)

// 计算属性
const isValidHex = computed(() => {
  return ColorUtils.isValidColor(hexValue.value) && hexValue.value.startsWith('#')
})

const isValidRgb = computed(() => {
  return ColorUtils.isValidColor(rgbValue.value) && rgbValue.value.startsWith('rgb')
})

const currentColor = computed(() => {
  if (isValidHex.value) return hexValue.value
  if (isValidRgb.value) return rgbValue.value
  return null
})

// HEX 分解
const redHex = computed(() => isValidHex.value ? hexValue.value.slice(1, 3) : '')
const greenHex = computed(() => isValidHex.value ? hexValue.value.slice(3, 5) : '')
const blueHex = computed(() => isValidHex.value ? hexValue.value.slice(5, 7) : '')

const redDecimal = computed(() => parseInt(redHex.value, 16) || 0)
const greenDecimal = computed(() => parseInt(greenHex.value, 16) || 0)
const blueDecimal = computed(() => parseInt(blueHex.value, 16) || 0)

// 颜色信息
const luminancePercent = computed(() => {
  if (!currentColor.value) return 0
  return Math.round(ColorUtils.getLuminance(currentColor.value) * 100)
})

const contrastRatio = computed(() => {
  if (!currentColor.value) return 0
  return ColorUtils.getContrastRatio(currentColor.value, '#FFFFFF').toFixed(2)
})

// 转换精度
const conversionAccuracy = computed(() => {
  if (!isValidHex.value || !isValidRgb.value) return null
  
  const deltaE = ColorUtils.calculateDeltaE(hexValue.value, rgbValue.value)
  return { deltaE: deltaE.toFixed(2) }
})

const accuracyClass = computed(() => {
  if (!conversionAccuracy.value) return ''
  const deltaE = parseFloat(conversionAccuracy.value.deltaE)
  if (deltaE < 1) return 'excellent'
  if (deltaE < 3) return 'good'
  if (deltaE < 6) return 'fair'
  return 'poor'
})

const accuracyRating = computed(() => {
  const ratings = {
    excellent: '优秀',
    good: '良好',
    fair: '一般',
    poor: '较差'
  }
  return ratings[accuracyClass.value] || ''
})

const accuracyDescription = computed(() => {
  const descriptions = {
    excellent: '转换精度极高，颜色差异几乎不可察觉',
    good: '转换精度良好，颜色差异很小',
    fair: '转换精度一般，可能有轻微颜色差异',
    poor: '转换精度较低，存在明显颜色差异'
  }
  return descriptions[accuracyClass.value] || ''
})

// 方法
const handleHexInput = () => {
  hexError.value = ''

  if (!hexValue.value.trim()) return

  // 首先检查基本的 HEX 格式
  if (!hexValue.value.startsWith('#') || !/^#[0-9A-Fa-f]{3,6}$/.test(hexValue.value)) {
    hexError.value = '无效的 HEX 格式，请使用 #RRGGBB 或 #RGB 格式'
    return
  }

  const parsed = ColorParser.parseEnhanced(hexValue.value, {
    enableSuggestions: true,
    enableFuzzyMatch: true
  })

  if (parsed.mode === 'unknown') {
    hexError.value = parsed.suggestions?.length > 0
      ? `无效格式。建议: ${parsed.suggestions.join(', ')}`
      : '无效的 HEX 格式'
    return
  }

  if (parsed.mode === 'hex') {
    // 转换为 RGB
    const rgb = ColorUtils.hexToRgb(hexValue.value)
    if (rgb) {
      rgbValue.value = `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
      redValue.value = rgb.r
      greenValue.value = rgb.g
      blueValue.value = rgb.b
      rgbError.value = ''

      emitConversion('hex', 'rgb')
    } else {
      hexError.value = '无法转换此 HEX 值'
    }
  }
}

const handleRgbInput = () => {
  rgbError.value = ''
  
  if (!rgbValue.value.trim()) return
  
  const parsed = ColorParser.parseEnhanced(rgbValue.value, {
    enableSuggestions: true,
    enableFuzzyMatch: true
  })
  
  if (parsed.mode === 'unknown') {
    rgbError.value = parsed.suggestions?.length > 0 
      ? `无效格式。建议: ${parsed.suggestions.join(', ')}`
      : '无效的 RGB 格式'
    return
  }
  
  if (parsed.mode === 'rgb') {
    // 检查 parsed.values 是否存在
    if (!parsed.values || typeof parsed.values !== 'object') {
      rgbError.value = '无法解析 RGB 值'
      return
    }

    // 转换为 HEX
    const hex = ColorUtils.rgbToHex(parsed.values.r, parsed.values.g, parsed.values.b)
    if (hex) {
      hexValue.value = hex
      redValue.value = parsed.values.r
      greenValue.value = parsed.values.g
      blueValue.value = parsed.values.b
      hexError.value = ''

      emitConversion('rgb', 'hex')
    }
  }
}

const updateFromSliders = () => {
  const newRgb = `rgb(${redValue.value}, ${greenValue.value}, ${blueValue.value})`
  const newHex = ColorUtils.rgbToHex(redValue.value, greenValue.value, blueValue.value)
  
  rgbValue.value = newRgb
  hexValue.value = newHex
  
  rgbError.value = ''
  hexError.value = ''
  
  emitConversion('slider', 'both')
}

const swapValues = () => {
  // 交换输入输出值
  const tempHex = hexValue.value
  const tempRgb = rgbValue.value
  
  hexValue.value = tempRgb.includes('rgb') ? ColorUtils.rgbToHex(...ColorUtils.parseRgb(tempRgb)) : tempHex
  rgbValue.value = tempHex.includes('#') ? ColorUtils.hexToRgbString(tempHex) : tempRgb
}

const handlePaste = async (event) => {
  // 处理粘贴事件，自动识别格式
  try {
    const pastedText = (event.clipboardData || window.clipboardData).getData('text')
    const parsed = ColorParser.parseEnhanced(pastedText, {
      enableSuggestions: true,
      enableFuzzyMatch: true
    })
    
    if (parsed.mode !== 'unknown') {
      if (parsed.mode === 'hex') {
        hexValue.value = pastedText
        handleHexInput()
      } else if (parsed.mode === 'rgb') {
        rgbValue.value = pastedText
        handleRgbInput()
      }
    }
  } catch (error) {
    console.warn('Paste handling failed:', error)
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // 可以添加成功提示
  } catch (error) {
    console.warn('Failed to copy to clipboard:', error)
  }
}

const emitConversion = (sourceFormat, targetFormat) => {
  const conversionData = {
    source: { format: sourceFormat, value: sourceFormat === 'hex' ? hexValue.value : rgbValue.value },
    target: { format: targetFormat, value: targetFormat === 'hex' ? hexValue.value : rgbValue.value },
    deltaE: conversionAccuracy.value?.deltaE || null
  }
  
  emit('conversion-complete', conversionData)
  emit('color-change', { input: currentColor.value })
}

// 初始化
handleHexInput()
</script>

<style scoped>
.hex-rgb-converter {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.converter-header {
  text-align: center;
}

.converter-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.converter-description {
  font-size: 1rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
  line-height: 1.6;
}

.conversion-panels {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: start;
}

.input-panel {
  padding: 2rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.panel-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0;
}

.format-info {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700, #374151);
}

.input-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 8px;
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.color-input:focus {
  outline: none;
  border-color: var(--color-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-input.error {
  border-color: var(--color-red-500, #ef4444);
}

.copy-button {
  padding: 0.5rem;
  background: transparent;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: var(--color-gray-100, #f3f4f6);
}

.error-message {
  font-size: 0.75rem;
  color: var(--color-red-600, #dc2626);
  margin-top: 0.25rem;
}

.conversion-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 4rem;
}

.swap-button {
  width: 3rem;
  height: 3rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.swap-button:hover {
  background: var(--color-primary-600, #2563eb);
  transform: scale(1.1);
}

.hex-breakdown,
.rgb-sliders {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-gray-200, #e5e7eb);
}

.breakdown-title,
.slider-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-gray-700, #374151);
  margin-bottom: 1rem;
}

.hex-components {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.component {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 0.75rem;
  align-items: center;
  padding: 0.5rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 6px;
}

.component-label {
  font-size: 0.75rem;
  color: var(--color-gray-600, #6b7280);
}

.component-hex,
.component-decimal {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.75rem;
  font-weight: 600;
}

.slider-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.slider-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.slider-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.color-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.red-slider {
  background: linear-gradient(to right, #000000, #ff0000);
}

.green-slider {
  background: linear-gradient(to right, #000000, #00ff00);
}

.blue-slider {
  background: linear-gradient(to right, #000000, #0000ff);
}

.color-preview-section {
  text-align: center;
}

.preview-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: left;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.info-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  color: var(--color-gray-900, #111827);
}

.accuracy-info {
  padding: 1.5rem;
  background: var(--color-blue-50, #eff6ff);
  border-radius: 12px;
}

.accuracy-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-blue-900, #1e3a8a);
  margin: 0 0 1rem 0;
}

.accuracy-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.accuracy-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.accuracy-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-blue-700, #1d4ed8);
}

.accuracy-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-blue-900, #1e3a8a);
}

.accuracy-rating {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.accuracy-rating.excellent {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-700, #15803d);
}

.accuracy-rating.good {
  background: var(--color-blue-100, #dbeafe);
  color: var(--color-blue-700, #1d4ed8);
}

.accuracy-rating.fair {
  background: var(--color-yellow-100, #fef3c7);
  color: var(--color-yellow-700, #a16207);
}

.accuracy-rating.poor {
  background: var(--color-red-100, #fee2e2);
  color: var(--color-red-700, #b91c1c);
}

.accuracy-description {
  font-size: 0.875rem;
  color: var(--color-blue-700, #1d4ed8);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .conversion-panels {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .conversion-arrow {
    padding-top: 0;
    order: -1;
  }
  
  .swap-button {
    transform: rotate(90deg);
  }
  
  .preview-container {
    flex-direction: column;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .input-panel {
    padding: 1.5rem;
  }
  
  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .converter-title,
  .panel-title,
  .preview-title {
    color: white;
  }
  
  .converter-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .input-panel {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .color-input {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: white;
  }
  
  .component {
    background: var(--color-gray-700, #374151);
  }
  
  .accuracy-info {
    background: var(--color-blue-900, #1e3a8a);
  }
}
</style>
