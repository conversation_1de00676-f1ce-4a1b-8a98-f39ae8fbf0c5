<!--
  RgbHslConverter.vue - RGB ↔ HSL 双向转换器
  提供 RGB 和 HSL 格式之间的精确转换
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="rgb-hsl-converter">
    <!-- 转换器标题 -->
    <header class="converter-header">
      <h2 class="converter-title">RGB ↔ HSL 转换器</h2>
      <p class="converter-description">
        在红绿蓝 (RGB) 和色相饱和度亮度 (HSL) 颜色格式之间进行精确转换
      </p>
    </header>

    <!-- 转换面板 -->
    <div class="conversion-panels">
      <!-- RGB 输入面板 -->
      <div class="input-panel rgb-panel">
        <div class="panel-header">
          <h3 class="panel-title">RGB 格式</h3>
          <span class="format-info">rgb(R, G, B)</span>
        </div>
        
        <div class="input-group">
          <label class="input-label">RGB 颜色值</label>
          <div class="input-container">
            <input
              v-model="rgbValue"
              type="text"
              placeholder="rgb(255, 107, 53)"
              class="color-input rgb-input"
              :class="{ 'error': rgbError }"
              @input="handleRgbInput"
              @paste="handlePaste"
            />
            <button
              v-if="rgbValue"
              @click="copyToClipboard(rgbValue)"
              class="copy-button"
              title="复制 RGB 值"
            >
              📋
            </button>
          </div>
          <div v-if="rgbError" class="error-message">
            {{ rgbError }}
          </div>
        </div>

        <!-- RGB 滑块控制 -->
        <div v-if="isValidRgb" class="rgb-sliders">
          <div class="slider-title">RGB 通道调节</div>
          <div class="slider-group">
            <div class="slider-item">
              <label class="slider-label">红色: {{ redValue }}</label>
              <input
                v-model.number="redValue"
                type="range"
                min="0"
                max="255"
                class="color-slider red-slider"
                @input="updateFromRgbSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">绿色: {{ greenValue }}</label>
              <input
                v-model.number="greenValue"
                type="range"
                min="0"
                max="255"
                class="color-slider green-slider"
                @input="updateFromRgbSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">蓝色: {{ blueValue }}</label>
              <input
                v-model.number="blueValue"
                type="range"
                min="0"
                max="255"
                class="color-slider blue-slider"
                @input="updateFromRgbSliders"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 转换箭头 -->
      <div class="conversion-arrow">
        <button
          @click="swapValues"
          class="swap-button"
          title="交换输入输出"
        >
          ⇄
        </button>
      </div>

      <!-- HSL 输入面板 -->
      <div class="input-panel hsl-panel">
        <div class="panel-header">
          <h3 class="panel-title">HSL 格式</h3>
          <span class="format-info">hsl(H, S%, L%)</span>
        </div>
        
        <div class="input-group">
          <label class="input-label">HSL 颜色值</label>
          <div class="input-container">
            <input
              v-model="hslValue"
              type="text"
              placeholder="hsl(14, 100%, 61%)"
              class="color-input hsl-input"
              :class="{ 'error': hslError }"
              @input="handleHslInput"
              @paste="handlePaste"
            />
            <button
              v-if="hslValue"
              @click="copyToClipboard(hslValue)"
              class="copy-button"
              title="复制 HSL 值"
            >
              📋
            </button>
          </div>
          <div v-if="hslError" class="error-message">
            {{ hslError }}
          </div>
        </div>

        <!-- HSL 滑块控制 -->
        <div v-if="isValidHsl" class="hsl-sliders">
          <div class="slider-title">HSL 参数调节</div>
          <div class="slider-group">
            <div class="slider-item">
              <label class="slider-label">色相: {{ hueValue }}°</label>
              <input
                v-model.number="hueValue"
                type="range"
                min="0"
                max="360"
                class="color-slider hue-slider"
                @input="updateFromHslSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">饱和度: {{ saturationValue }}%</label>
              <input
                v-model.number="saturationValue"
                type="range"
                min="0"
                max="100"
                class="color-slider saturation-slider"
                @input="updateFromHslSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">亮度: {{ lightnessValue }}%</label>
              <input
                v-model.number="lightnessValue"
                type="range"
                min="0"
                max="100"
                class="color-slider lightness-slider"
                @input="updateFromHslSliders"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 颜色预览 -->
    <div v-if="currentColor" class="color-preview-section">
      <h3 class="preview-title">颜色预览</h3>
      <div class="preview-container">
        <ColorPreview 
          :color="currentColor" 
          :show-contrast="true"
          :show-luminance="true"
          size="large"
        />
        <div class="preview-info">
          <div class="info-item">
            <span class="info-label">当前颜色:</span>
            <span class="info-value">{{ currentColor }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">HEX 值:</span>
            <span class="info-value">{{ hexValue }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">颜色温度:</span>
            <span class="info-value">{{ colorTemperature }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 色彩空间说明 -->
    <div class="color-space-info">
      <h3 class="info-title">RGB vs HSL 色彩空间</h3>
      <div class="info-grid">
        <div class="info-card">
          <h4 class="card-title">RGB 色彩空间</h4>
          <p class="card-description">
            基于光的加色混合原理，通过红、绿、蓝三个通道的强度组合产生颜色。
            适合显示器显示和数字图像处理。
          </p>
        </div>
        <div class="info-card">
          <h4 class="card-title">HSL 色彩空间</h4>
          <p class="card-description">
            更符合人类对颜色的感知，通过色相、饱和度、亮度三个维度描述颜色。
            适合颜色调整和配色方案设计。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import { ColorUtils } from '@/utils/colorUtils'
import ColorParser from '@/scripts/ColorParser.js'
import ColorPreview from '@/components/common/ColorPreview.vue'

const emit = defineEmits(['color-change', 'conversion-complete'])

const colorStore = useColorStore()

// 响应式数据
const rgbValue = ref('rgb(255, 107, 53)')
const hslValue = ref('hsl(14, 100%, 61%)')
const rgbError = ref('')
const hslError = ref('')

// RGB 滑块值
const redValue = ref(255)
const greenValue = ref(107)
const blueValue = ref(53)

// HSL 滑块值
const hueValue = ref(14)
const saturationValue = ref(100)
const lightnessValue = ref(61)

// 计算属性
const isValidRgb = computed(() => {
  return ColorUtils.isValidColor(rgbValue.value) && rgbValue.value.includes('rgb')
})

const isValidHsl = computed(() => {
  return ColorUtils.isValidColor(hslValue.value) && hslValue.value.includes('hsl')
})

const currentColor = computed(() => {
  if (isValidRgb.value) return rgbValue.value
  if (isValidHsl.value) return hslValue.value
  return null
})

const hexValue = computed(() => {
  if (!currentColor.value) return '#000000'
  return colorStore.convertToFormat('hex') || '#000000'
})

const colorTemperature = computed(() => {
  if (!currentColor.value) return ''
  return ColorUtils.getColorTemperature(currentColor.value)
})

// 方法
const handleRgbInput = () => {
  rgbError.value = ''
  
  if (!rgbValue.value.trim()) return
  
  const parsed = ColorParser.parseEnhanced(rgbValue.value, {
    enableSuggestions: true,
    enableFuzzyMatch: true
  })
  
  if (parsed.mode === 'unknown') {
    rgbError.value = parsed.suggestions?.length > 0 
      ? `无效格式。建议: ${parsed.suggestions.join(', ')}`
      : '无效的 RGB 格式'
    return
  }
  
  if (parsed.mode === 'rgb') {
    // 检查 parsed.values 是否存在
    if (!parsed.values || typeof parsed.values !== 'object') {
      rgbError.value = '无法解析 RGB 值'
      return
    }

    // 转换为 HSL
    const hsl = ColorUtils.rgbToHsl(parsed.values.r, parsed.values.g, parsed.values.b)
    if (hsl) {
      hslValue.value = `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`
      hueValue.value = hsl.h
      saturationValue.value = hsl.s
      lightnessValue.value = hsl.l
      hslError.value = ''

      // 更新 RGB 滑块
      redValue.value = parsed.values.r
      greenValue.value = parsed.values.g
      blueValue.value = parsed.values.b

      emitConversion('rgb', 'hsl')
    }
  }
}

const handleHslInput = () => {
  hslError.value = ''
  
  if (!hslValue.value.trim()) return
  
  const parsed = ColorParser.parseEnhanced(hslValue.value, {
    enableSuggestions: true,
    enableFuzzyMatch: true
  })
  
  if (parsed.mode === 'unknown') {
    hslError.value = parsed.suggestions?.length > 0 
      ? `无效格式。建议: ${parsed.suggestions.join(', ')}`
      : '无效的 HSL 格式'
    return
  }
  
  if (parsed.mode === 'hsl') {
    // 检查 parsed.values 是否存在
    if (!parsed.values || typeof parsed.values !== 'object') {
      hslError.value = '无法解析 HSL 值'
      return
    }

    // 转换为 RGB
    const rgb = ColorUtils.hslToRgb(parsed.values.h, parsed.values.s, parsed.values.l)
    if (rgb) {
      rgbValue.value = `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
      redValue.value = rgb.r
      greenValue.value = rgb.g
      blueValue.value = rgb.b
      rgbError.value = ''

      // 更新 HSL 滑块
      hueValue.value = parsed.values.h
      saturationValue.value = parsed.values.s
      lightnessValue.value = parsed.values.l

      emitConversion('hsl', 'rgb')
    }
  }
}

const updateFromRgbSliders = () => {
  const newRgb = `rgb(${redValue.value}, ${greenValue.value}, ${blueValue.value})`
  const hsl = ColorUtils.rgbToHsl(redValue.value, greenValue.value, blueValue.value)
  const newHsl = `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`
  
  rgbValue.value = newRgb
  hslValue.value = newHsl
  hueValue.value = hsl.h
  saturationValue.value = hsl.s
  lightnessValue.value = hsl.l
  
  rgbError.value = ''
  hslError.value = ''
  
  emitConversion('rgb-slider', 'both')
}

const updateFromHslSliders = () => {
  const newHsl = `hsl(${hueValue.value}, ${saturationValue.value}%, ${lightnessValue.value}%)`
  const rgb = ColorUtils.hslToRgb(hueValue.value, saturationValue.value, lightnessValue.value)
  const newRgb = `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
  
  hslValue.value = newHsl
  rgbValue.value = newRgb
  redValue.value = rgb.r
  greenValue.value = rgb.g
  blueValue.value = rgb.b
  
  rgbError.value = ''
  hslError.value = ''
  
  emitConversion('hsl-slider', 'both')
}

const swapValues = () => {
  // 交换输入输出值
  const tempRgb = rgbValue.value
  const tempHsl = hslValue.value
  
  rgbValue.value = tempHsl.includes('hsl') ? ColorUtils.hslToRgb(...ColorUtils.parseHsl(tempHsl)) : tempRgb
  hslValue.value = tempRgb.includes('rgb') ? ColorUtils.rgbToHsl(...ColorUtils.parseRgb(tempRgb)) : tempHsl
}

const handlePaste = async (event) => {
  try {
    const pastedText = (event.clipboardData || window.clipboardData).getData('text')
    const parsed = ColorParser.parseEnhanced(pastedText, {
      enableSuggestions: true,
      enableFuzzyMatch: true
    })
    
    if (parsed.mode !== 'unknown') {
      if (parsed.mode === 'rgb') {
        rgbValue.value = pastedText
        handleRgbInput()
      } else if (parsed.mode === 'hsl') {
        hslValue.value = pastedText
        handleHslInput()
      }
    }
  } catch (error) {
    console.warn('Paste handling failed:', error)
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
  } catch (error) {
    console.warn('Failed to copy to clipboard:', error)
  }
}

const emitConversion = (sourceFormat, targetFormat) => {
  const conversionData = {
    source: { format: sourceFormat, value: sourceFormat.includes('rgb') ? rgbValue.value : hslValue.value },
    target: { format: targetFormat, value: targetFormat.includes('rgb') ? rgbValue.value : hslValue.value },
    deltaE: 0 // RGB-HSL 转换是无损的
  }
  
  emit('conversion-complete', conversionData)
  emit('color-change', { input: currentColor.value })
}

// 初始化
handleRgbInput()
</script>

<style scoped>
/* 使用与 HexRgbConverter 相同的样式 */
.rgb-hsl-converter {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.converter-header {
  text-align: center;
}

.converter-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.converter-description {
  font-size: 1rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
  line-height: 1.6;
}

.conversion-panels {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: start;
}

.input-panel {
  padding: 2rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.panel-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0;
}

.format-info {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700, #374151);
}

.input-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 8px;
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.color-input:focus {
  outline: none;
  border-color: var(--color-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-input.error {
  border-color: var(--color-red-500, #ef4444);
}

.copy-button {
  padding: 0.5rem;
  background: transparent;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: var(--color-gray-100, #f3f4f6);
}

.error-message {
  font-size: 0.75rem;
  color: var(--color-red-600, #dc2626);
  margin-top: 0.25rem;
}

.conversion-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 4rem;
}

.swap-button {
  width: 3rem;
  height: 3rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.swap-button:hover {
  background: var(--color-primary-600, #2563eb);
  transform: scale(1.1);
}

.rgb-sliders,
.hsl-sliders {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-gray-200, #e5e7eb);
}

.slider-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-gray-700, #374151);
  margin-bottom: 1rem;
}

.slider-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.slider-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.slider-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.color-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.red-slider {
  background: linear-gradient(to right, #000000, #ff0000);
}

.green-slider {
  background: linear-gradient(to right, #000000, #00ff00);
}

.blue-slider {
  background: linear-gradient(to right, #000000, #0000ff);
}

.hue-slider {
  background: linear-gradient(to right, 
    hsl(0, 100%, 50%), hsl(60, 100%, 50%), hsl(120, 100%, 50%), 
    hsl(180, 100%, 50%), hsl(240, 100%, 50%), hsl(300, 100%, 50%), 
    hsl(360, 100%, 50%));
}

.saturation-slider {
  background: linear-gradient(to right, #808080, #ff0000);
}

.lightness-slider {
  background: linear-gradient(to right, #000000, #808080, #ffffff);
}

.color-preview-section {
  text-align: center;
}

.preview-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: left;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.info-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  color: var(--color-gray-900, #111827);
}

.color-space-info {
  padding: 2rem;
  background: var(--color-blue-50, #eff6ff);
  border-radius: 12px;
}

.info-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-blue-900, #1e3a8a);
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.info-card {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.75rem 0;
}

.card-description {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .conversion-panels {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .conversion-arrow {
    padding-top: 0;
    order: -1;
  }
  
  .swap-button {
    transform: rotate(90deg);
  }
  
  .preview-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .input-panel {
    padding: 1.5rem;
  }
  
  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .converter-title,
  .panel-title,
  .preview-title,
  .info-title,
  .card-title {
    color: white;
  }
  
  .converter-description,
  .card-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .input-panel,
  .info-card {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .color-input {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: white;
  }
  
  .color-space-info {
    background: var(--color-blue-900, #1e3a8a);
  }
}
</style>
