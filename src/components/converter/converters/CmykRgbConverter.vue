<!--
  CmykRgbConverter.vue - CMYK ↔ RGB 双向转换器
  提供 CMYK 和 RGB 格式之间的转换
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="cmyk-rgb-converter">
    <!-- 转换器标题 -->
    <header class="converter-header">
      <h2 class="converter-title">CMYK ↔ RGB 转换器</h2>
      <p class="converter-description">
        在青品黄黑 (CMYK) 印刷色彩和红绿蓝 (RGB) 显示色彩之间进行转换
      </p>
    </header>

    <!-- 转换面板 -->
    <div class="conversion-panels">
      <!-- CMYK 输入面板 -->
      <div class="input-panel cmyk-panel">
        <div class="panel-header">
          <h3 class="panel-title">CMYK 格式</h3>
          <span class="format-info">cmyk(C%, M%, Y%, K%)</span>
        </div>
        
        <div class="input-group">
          <label class="input-label">CMYK 颜色值</label>
          <div class="input-container">
            <input
              v-model="cmykValue"
              type="text"
              placeholder="cmyk(0%, 58%, 79%, 0%)"
              class="color-input cmyk-input"
              :class="{ 'error': cmykError }"
              @input="handleCmykInput"
              @paste="handlePaste"
            />
            <button
              v-if="cmykValue"
              @click="copyToClipboard(cmykValue)"
              class="copy-button"
              title="复制 CMYK 值"
            >
              📋
            </button>
          </div>
          <div v-if="cmykError" class="error-message">
            {{ cmykError }}
          </div>
        </div>

        <!-- CMYK 滑块控制 -->
        <div v-if="isValidCmyk" class="cmyk-sliders">
          <div class="slider-title">CMYK 通道调节</div>
          <div class="slider-group">
            <div class="slider-item">
              <label class="slider-label">青色 (C): {{ cyanValue }}%</label>
              <input
                v-model.number="cyanValue"
                type="range"
                min="0"
                max="100"
                class="color-slider cyan-slider"
                @input="updateFromCmykSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">品红 (M): {{ magentaValue }}%</label>
              <input
                v-model.number="magentaValue"
                type="range"
                min="0"
                max="100"
                class="color-slider magenta-slider"
                @input="updateFromCmykSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">黄色 (Y): {{ yellowValue }}%</label>
              <input
                v-model.number="yellowValue"
                type="range"
                min="0"
                max="100"
                class="color-slider yellow-slider"
                @input="updateFromCmykSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">黑色 (K): {{ blackValue }}%</label>
              <input
                v-model.number="blackValue"
                type="range"
                min="0"
                max="100"
                class="color-slider black-slider"
                @input="updateFromCmykSliders"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 转换箭头 -->
      <div class="conversion-arrow">
        <button
          @click="swapValues"
          class="swap-button"
          title="交换输入输出"
        >
          ⇄
        </button>
      </div>

      <!-- RGB 输入面板 -->
      <div class="input-panel rgb-panel">
        <div class="panel-header">
          <h3 class="panel-title">RGB 格式</h3>
          <span class="format-info">rgb(R, G, B)</span>
        </div>
        
        <div class="input-group">
          <label class="input-label">RGB 颜色值</label>
          <div class="input-container">
            <input
              v-model="rgbValue"
              type="text"
              placeholder="rgb(255, 107, 53)"
              class="color-input rgb-input"
              :class="{ 'error': rgbError }"
              @input="handleRgbInput"
              @paste="handlePaste"
            />
            <button
              v-if="rgbValue"
              @click="copyToClipboard(rgbValue)"
              class="copy-button"
              title="复制 RGB 值"
            >
              📋
            </button>
          </div>
          <div v-if="rgbError" class="error-message">
            {{ rgbError }}
          </div>
        </div>

        <!-- RGB 滑块控制 -->
        <div v-if="isValidRgb" class="rgb-sliders">
          <div class="slider-title">RGB 通道调节</div>
          <div class="slider-group">
            <div class="slider-item">
              <label class="slider-label">红色: {{ redValue }}</label>
              <input
                v-model.number="redValue"
                type="range"
                min="0"
                max="255"
                class="color-slider red-slider"
                @input="updateFromRgbSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">绿色: {{ greenValue }}</label>
              <input
                v-model.number="greenValue"
                type="range"
                min="0"
                max="255"
                class="color-slider green-slider"
                @input="updateFromRgbSliders"
              />
            </div>
            <div class="slider-item">
              <label class="slider-label">蓝色: {{ blueValue }}</label>
              <input
                v-model.number="blueValue"
                type="range"
                min="0"
                max="255"
                class="color-slider blue-slider"
                @input="updateFromRgbSliders"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 颜色预览 -->
    <div v-if="currentColor" class="color-preview-section">
      <h3 class="preview-title">颜色预览</h3>
      <div class="preview-container">
        <ColorPreview 
          :color="currentColor" 
          :show-contrast="true"
          :show-luminance="true"
          size="large"
        />
        <div class="preview-info">
          <div class="info-item">
            <span class="info-label">当前颜色:</span>
            <span class="info-value">{{ currentColor }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">HEX 值:</span>
            <span class="info-value">{{ hexValue }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">色彩空间:</span>
            <span class="info-value">{{ colorSpace }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 色彩空间说明 -->
    <div class="color-space-info">
      <h3 class="info-title">CMYK vs RGB 色彩空间</h3>
      <div class="info-grid">
        <div class="info-card">
          <h4 class="card-title">CMYK 色彩空间</h4>
          <p class="card-description">
            减色混合原理，用于印刷行业。通过青、品红、黄、黑四种油墨的叠加产生颜色。
            色域相对较小，但适合物理印刷。
          </p>
        </div>
        <div class="info-card">
          <h4 class="card-title">RGB 色彩空间</h4>
          <p class="card-description">
            加色混合原理，用于显示设备。通过红、绿、蓝三种光的组合产生颜色。
            色域较大，适合屏幕显示。
          </p>
        </div>
      </div>
      
      <div class="conversion-note">
        <h4 class="note-title">⚠️ 转换注意事项</h4>
        <ul class="note-list">
          <li>CMYK 到 RGB 的转换可能存在色域差异</li>
          <li>某些鲜艳的 RGB 颜色无法在 CMYK 中准确表示</li>
          <li>建议在实际印刷前进行色彩校准</li>
          <li>不同的印刷标准（如 ISO Coated v2）会影响转换结果</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import { ColorUtils } from '@/utils/colorUtils'
import ColorParser from '@/scripts/ColorParser.js'
import ColorPreview from '@/components/common/ColorPreview.vue'

const emit = defineEmits(['color-change', 'conversion-complete'])

const colorStore = useColorStore()

// 响应式数据
const cmykValue = ref('cmyk(0%, 58%, 79%, 0%)')
const rgbValue = ref('rgb(255, 107, 53)')
const cmykError = ref('')
const rgbError = ref('')

// CMYK 滑块值
const cyanValue = ref(0)
const magentaValue = ref(58)
const yellowValue = ref(79)
const blackValue = ref(0)

// RGB 滑块值
const redValue = ref(255)
const greenValue = ref(107)
const blueValue = ref(53)

// 计算属性
const isValidCmyk = computed(() => {
  return cmykValue.value.includes('cmyk') && 
         /cmyk\(\s*\d+%?\s*,\s*\d+%?\s*,\s*\d+%?\s*,\s*\d+%?\s*\)/.test(cmykValue.value)
})

const isValidRgb = computed(() => {
  return ColorUtils.isValidColor(rgbValue.value) && rgbValue.value.includes('rgb')
})

const currentColor = computed(() => {
  if (isValidRgb.value) return rgbValue.value
  if (isValidCmyk.value) {
    // 将 CMYK 转换为 RGB 用于预览
    const rgb = cmykToRgb(cyanValue.value, magentaValue.value, yellowValue.value, blackValue.value)
    return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
  }
  return null
})

const hexValue = computed(() => {
  if (!currentColor.value) return '#000000'
  return colorStore.convertToFormat('hex') || '#000000'
})

const colorSpace = computed(() => {
  if (isValidCmyk.value) return 'CMYK (印刷)'
  if (isValidRgb.value) return 'RGB (显示)'
  return '未知'
})

// CMYK 转换函数
const cmykToRgb = (c, m, y, k) => {
  const cDecimal = c / 100
  const mDecimal = m / 100
  const yDecimal = y / 100
  const kDecimal = k / 100
  
  const r = Math.round(255 * (1 - cDecimal) * (1 - kDecimal))
  const g = Math.round(255 * (1 - mDecimal) * (1 - kDecimal))
  const b = Math.round(255 * (1 - yDecimal) * (1 - kDecimal))
  
  return { r, g, b }
}

const rgbToCmyk = (r, g, b) => {
  const rDecimal = r / 255
  const gDecimal = g / 255
  const bDecimal = b / 255
  
  const k = 1 - Math.max(rDecimal, gDecimal, bDecimal)
  const c = k === 1 ? 0 : (1 - rDecimal - k) / (1 - k)
  const m = k === 1 ? 0 : (1 - gDecimal - k) / (1 - k)
  const y = k === 1 ? 0 : (1 - bDecimal - k) / (1 - k)
  
  return {
    c: Math.round(c * 100),
    m: Math.round(m * 100),
    y: Math.round(y * 100),
    k: Math.round(k * 100)
  }
}

// 方法
const handleCmykInput = () => {
  cmykError.value = ''
  
  if (!cmykValue.value.trim()) return
  
  // 解析 CMYK 值
  const match = cmykValue.value.match(/cmyk\(\s*(\d+)%?\s*,\s*(\d+)%?\s*,\s*(\d+)%?\s*,\s*(\d+)%?\s*\)/)
  
  if (!match) {
    cmykError.value = '无效的 CMYK 格式，请使用 cmyk(C%, M%, Y%, K%) 格式'
    return
  }
  
  const c = parseInt(match[1])
  const m = parseInt(match[2])
  const y = parseInt(match[3])
  const k = parseInt(match[4])
  
  if ([c, m, y, k].some(val => val < 0 || val > 100)) {
    cmykError.value = 'CMYK 值必须在 0-100 之间'
    return
  }
  
  // 更新滑块值
  cyanValue.value = c
  magentaValue.value = m
  yellowValue.value = y
  blackValue.value = k
  
  // 转换为 RGB
  const rgb = cmykToRgb(c, m, y, k)
  rgbValue.value = `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
  redValue.value = rgb.r
  greenValue.value = rgb.g
  blueValue.value = rgb.b
  rgbError.value = ''
  
  emitConversion('cmyk', 'rgb')
}

const handleRgbInput = () => {
  rgbError.value = ''
  
  if (!rgbValue.value.trim()) return
  
  const parsed = ColorParser.parseEnhanced(rgbValue.value, {
    enableSuggestions: true,
    enableFuzzyMatch: true
  })
  
  if (parsed.mode === 'unknown') {
    rgbError.value = parsed.suggestions?.length > 0 
      ? `无效格式。建议: ${parsed.suggestions.join(', ')}`
      : '无效的 RGB 格式'
    return
  }
  
  if (parsed.mode === 'rgb') {
    // 检查 parsed.values 是否存在
    if (!parsed.values || typeof parsed.values !== 'object') {
      rgbError.value = '无法解析 RGB 值'
      return
    }

    // 更新 RGB 滑块
    redValue.value = parsed.values.r
    greenValue.value = parsed.values.g
    blueValue.value = parsed.values.b

    // 转换为 CMYK
    const cmyk = rgbToCmyk(parsed.values.r, parsed.values.g, parsed.values.b)
    cmykValue.value = `cmyk(${cmyk.c}%, ${cmyk.m}%, ${cmyk.y}%, ${cmyk.k}%)`
    cyanValue.value = cmyk.c
    magentaValue.value = cmyk.m
    yellowValue.value = cmyk.y
    blackValue.value = cmyk.k
    cmykError.value = ''

    emitConversion('rgb', 'cmyk')
  }
}

const updateFromCmykSliders = () => {
  const newCmyk = `cmyk(${cyanValue.value}%, ${magentaValue.value}%, ${yellowValue.value}%, ${blackValue.value}%)`
  const rgb = cmykToRgb(cyanValue.value, magentaValue.value, yellowValue.value, blackValue.value)
  const newRgb = `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
  
  cmykValue.value = newCmyk
  rgbValue.value = newRgb
  redValue.value = rgb.r
  greenValue.value = rgb.g
  blueValue.value = rgb.b
  
  cmykError.value = ''
  rgbError.value = ''
  
  emitConversion('cmyk-slider', 'both')
}

const updateFromRgbSliders = () => {
  const newRgb = `rgb(${redValue.value}, ${greenValue.value}, ${blueValue.value})`
  const cmyk = rgbToCmyk(redValue.value, greenValue.value, blueValue.value)
  const newCmyk = `cmyk(${cmyk.c}%, ${cmyk.m}%, ${cmyk.y}%, ${cmyk.k}%)`
  
  rgbValue.value = newRgb
  cmykValue.value = newCmyk
  cyanValue.value = cmyk.c
  magentaValue.value = cmyk.m
  yellowValue.value = cmyk.y
  blackValue.value = cmyk.k
  
  rgbError.value = ''
  cmykError.value = ''
  
  emitConversion('rgb-slider', 'both')
}

const swapValues = () => {
  // CMYK 和 RGB 之间的交换
  const tempCmyk = cmykValue.value
  const tempRgb = rgbValue.value
  
  if (tempRgb.includes('rgb')) {
    handleRgbInput()
  }
  if (tempCmyk.includes('cmyk')) {
    handleCmykInput()
  }
}

const handlePaste = async (event) => {
  try {
    const pastedText = (event.clipboardData || window.clipboardData).getData('text')
    
    if (pastedText.includes('cmyk')) {
      cmykValue.value = pastedText
      handleCmykInput()
    } else if (pastedText.includes('rgb')) {
      rgbValue.value = pastedText
      handleRgbInput()
    }
  } catch (error) {
    console.warn('Paste handling failed:', error)
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
  } catch (error) {
    console.warn('Failed to copy to clipboard:', error)
  }
}

const emitConversion = (sourceFormat, targetFormat) => {
  const conversionData = {
    source: { format: sourceFormat, value: sourceFormat.includes('cmyk') ? cmykValue.value : rgbValue.value },
    target: { format: targetFormat, value: targetFormat.includes('cmyk') ? cmykValue.value : rgbValue.value },
    deltaE: 2.5 // CMYK-RGB 转换通常有一定的色差
  }
  
  emit('conversion-complete', conversionData)
  emit('color-change', { input: currentColor.value })
}

// 初始化
handleCmykInput()
</script>

<style scoped>
/* 使用与其他转换器相同的基础样式 */
.cmyk-rgb-converter {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.converter-header {
  text-align: center;
}

.converter-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.converter-description {
  font-size: 1rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
  line-height: 1.6;
}

.conversion-panels {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: start;
}

.input-panel {
  padding: 2rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.panel-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0;
}

.format-info {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700, #374151);
}

.input-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 8px;
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.color-input:focus {
  outline: none;
  border-color: var(--color-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-input.error {
  border-color: var(--color-red-500, #ef4444);
}

.copy-button {
  padding: 0.5rem;
  background: transparent;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: var(--color-gray-100, #f3f4f6);
}

.error-message {
  font-size: 0.75rem;
  color: var(--color-red-600, #dc2626);
  margin-top: 0.25rem;
}

.conversion-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 4rem;
}

.swap-button {
  width: 3rem;
  height: 3rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.swap-button:hover {
  background: var(--color-primary-600, #2563eb);
  transform: scale(1.1);
}

.cmyk-sliders,
.rgb-sliders {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--color-gray-200, #e5e7eb);
}

.slider-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-gray-700, #374151);
  margin-bottom: 1rem;
}

.slider-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.slider-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.slider-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.color-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.cyan-slider {
  background: linear-gradient(to right, #ffffff, #00ffff);
}

.magenta-slider {
  background: linear-gradient(to right, #ffffff, #ff00ff);
}

.yellow-slider {
  background: linear-gradient(to right, #ffffff, #ffff00);
}

.black-slider {
  background: linear-gradient(to right, #ffffff, #000000);
}

.red-slider {
  background: linear-gradient(to right, #000000, #ff0000);
}

.green-slider {
  background: linear-gradient(to right, #000000, #00ff00);
}

.blue-slider {
  background: linear-gradient(to right, #000000, #0000ff);
}

.color-preview-section {
  text-align: center;
}

.preview-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.preview-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: left;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.info-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  color: var(--color-gray-900, #111827);
}

.color-space-info {
  padding: 2rem;
  background: var(--color-orange-50, #fff7ed);
  border-radius: 12px;
}

.info-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-orange-900, #9a3412);
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.info-card {
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.75rem 0;
}

.card-description {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.5;
  margin: 0;
}

.conversion-note {
  padding: 1.5rem;
  background: var(--color-yellow-50, #fefce8);
  border: 1px solid var(--color-yellow-200, #fde047);
  border-radius: 8px;
}

.note-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-yellow-800, #a16207);
  margin: 0 0 1rem 0;
}

.note-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.note-list li {
  font-size: 0.875rem;
  color: var(--color-yellow-700, #a16207);
  line-height: 1.5;
  padding-left: 1.5rem;
  position: relative;
}

.note-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-yellow-600, #ca8a04);
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .conversion-panels {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .conversion-arrow {
    padding-top: 0;
    order: -1;
  }
  
  .swap-button {
    transform: rotate(90deg);
  }
  
  .preview-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .input-panel {
    padding: 1.5rem;
  }
  
  .panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .converter-title,
  .panel-title,
  .preview-title,
  .info-title,
  .card-title,
  .note-title {
    color: white;
  }
  
  .converter-description,
  .card-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .input-panel,
  .info-card {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .color-input {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: white;
  }
  
  .color-space-info {
    background: var(--color-orange-900, #9a3412);
  }
  
  .conversion-note {
    background: var(--color-yellow-900, #a16207);
    border-color: var(--color-yellow-700, #a16207);
  }
}
</style>
