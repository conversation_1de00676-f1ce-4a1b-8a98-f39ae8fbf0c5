<!--
  ConverterSkeleton.vue - 转换器加载骨架屏组件
  在转换器加载时显示占位符
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="converter-skeleton">
    <!-- 转换器标题骨架 -->
    <div class="skeleton-header">
      <div class="skeleton-title"></div>
      <div class="skeleton-description"></div>
    </div>
    
    <!-- 转换面板骨架 -->
    <div class="skeleton-panel">
      <!-- 输入区域 -->
      <div class="skeleton-input-section">
        <div class="skeleton-label"></div>
        <div class="skeleton-input-group">
          <div class="skeleton-input"></div>
          <div class="skeleton-swatch"></div>
        </div>
      </div>
      
      <!-- 转换箭头 -->
      <div class="skeleton-arrow"></div>
      
      <!-- 输出区域 -->
      <div class="skeleton-output-section">
        <div class="skeleton-label"></div>
        <div class="skeleton-output-group">
          <div class="skeleton-output"></div>
          <div class="skeleton-swatch"></div>
        </div>
      </div>
    </div>
    
    <!-- 色轮骨架 -->
    <div class="skeleton-color-wheel">
      <div class="skeleton-wheel-title"></div>
      <div class="skeleton-wheel"></div>
      <div class="skeleton-slider"></div>
    </div>
    
    <!-- 精度信息骨架 -->
    <div class="skeleton-precision">
      <div class="skeleton-precision-title"></div>
      <div class="skeleton-precision-items">
        <div class="skeleton-precision-item"></div>
        <div class="skeleton-precision-item"></div>
      </div>
    </div>
    
    <!-- 代码导出骨架 -->
    <div class="skeleton-export">
      <div class="skeleton-export-title"></div>
      <div class="skeleton-export-tabs">
        <div class="skeleton-tab"></div>
        <div class="skeleton-tab"></div>
        <div class="skeleton-tab"></div>
        <div class="skeleton-tab"></div>
      </div>
      <div class="skeleton-code-block"></div>
    </div>
  </div>
</template>

<script setup>
// 无需 props 或逻辑，纯展示组件
</script>

<style scoped>
.converter-skeleton {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 3rem;
  animation: pulse 2s ease-in-out infinite;
}

.skeleton-header {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.skeleton-title {
  width: 300px;
  height: 32px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
}

.skeleton-description {
  width: 400px;
  height: 20px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 6px;
}

.skeleton-panel {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
  background: var(--skeleton-bg, #f9fafb);
  border-radius: 16px;
}

.skeleton-input-section,
.skeleton-output-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-label {
  width: 120px;
  height: 16px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 4px;
}

.skeleton-input-group,
.skeleton-output-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.skeleton-input,
.skeleton-output {
  flex: 1;
  height: 48px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
}

.skeleton-swatch {
  width: 48px;
  height: 48px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
}

.skeleton-arrow {
  width: 40px;
  height: 40px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 50%;
}

.skeleton-color-wheel {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.skeleton-wheel-title {
  width: 150px;
  height: 24px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 6px;
}

.skeleton-wheel {
  width: 280px;
  height: 280px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 50%;
}

.skeleton-slider {
  width: 200px;
  height: 24px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 12px;
}

.skeleton-precision {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-precision-title {
  width: 120px;
  height: 24px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 6px;
}

.skeleton-precision-items {
  display: flex;
  gap: 2rem;
}

.skeleton-precision-item {
  width: 150px;
  height: 40px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
}

.skeleton-export {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-export-title {
  width: 100px;
  height: 24px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 6px;
}

.skeleton-export-tabs {
  display: flex;
  gap: 0.5rem;
}

.skeleton-tab {
  width: 80px;
  height: 32px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 6px;
}

.skeleton-code-block {
  width: 100%;
  height: 120px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 渐变动画效果 */
.skeleton-title,
.skeleton-description,
.skeleton-label,
.skeleton-input,
.skeleton-output,
.skeleton-swatch,
.skeleton-arrow,
.skeleton-wheel-title,
.skeleton-wheel,
.skeleton-slider,
.skeleton-precision-title,
.skeleton-precision-item,
.skeleton-export-title,
.skeleton-tab,
.skeleton-code-block {
  background: linear-gradient(
    90deg,
    var(--skeleton-color, #e5e7eb) 25%,
    var(--skeleton-highlight, #f3f4f6) 50%,
    var(--skeleton-color, #e5e7eb) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .converter-skeleton {
    padding: 1rem;
    gap: 2rem;
  }
  
  .skeleton-panel {
    flex-direction: column;
    padding: 1.5rem;
  }
  
  .skeleton-title {
    width: 250px;
  }
  
  .skeleton-description {
    width: 300px;
  }
  
  .skeleton-wheel {
    width: 200px;
    height: 200px;
  }
  
  .skeleton-precision-items {
    flex-direction: column;
  }
  
  .skeleton-export-tabs {
    flex-wrap: wrap;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .converter-skeleton {
    --skeleton-color: #374151;
    --skeleton-highlight: #4b5563;
    --skeleton-bg: #1f2937;
  }
}

/* CSS 变量定义 */
:root {
  --skeleton-color: #e5e7eb;
  --skeleton-highlight: #f3f4f6;
  --skeleton-bg: #f9fafb;
}

[data-theme="dark"] {
  --skeleton-color: #374151;
  --skeleton-highlight: #4b5563;
  --skeleton-bg: #1f2937;
}
</style>
