<!--
  ConversionHistory.vue - 转换历史组件
  显示和管理颜色转换历史记录
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="conversion-history">
    <div class="history-header">
      <h3 class="history-title">转换历史</h3>
      <button 
        v-if="history.length > 0"
        @click="clearHistory"
        class="clear-button"
        title="清空历史"
      >
        清空
      </button>
    </div>
    
    <div v-if="history.length === 0" class="empty-state">
      <div class="empty-icon">📝</div>
      <p class="empty-text">暂无转换历史</p>
      <p class="empty-hint">开始转换颜色后，历史记录将显示在这里</p>
    </div>
    
    <div v-else class="history-list">
      <div
        v-for="item in history"
        :key="item.id"
        class="history-item"
        @click="$emit('restore', item)"
      >
        <div class="item-header">
          <span class="converter-name">{{ getConverterName(item.converter) }}</span>
          <span class="timestamp">{{ formatTime(item.timestamp) }}</span>
        </div>
        
        <div class="conversion-flow">
          <div class="color-value source">
            <ColorSwatch 
              :color="item.source.value" 
              :size="'small'"
              :clickable="false"
            />
            <span class="value-text">{{ item.source.value }}</span>
          </div>
          
          <div class="arrow">→</div>
          
          <div class="color-value target">
            <ColorSwatch 
              :color="item.target.value" 
              :size="'small'"
              :clickable="false"
            />
            <span class="value-text">{{ item.target.value }}</span>
          </div>
        </div>
        
        <div v-if="item.deltaE !== null" class="precision-info">
          <span class="delta-label">色差:</span>
          <span class="delta-value" :class="getPrecisionClass(item.deltaE)">
            ΔE = {{ typeof item.deltaE === 'number' ? item.deltaE.toFixed(3) : parseFloat(item.deltaE || 0).toFixed(3) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ColorSwatch from '@/components/common/ColorSwatch.vue'

const props = defineProps({
  history: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['restore', 'clear'])

// 转换器名称映射
const converterNames = {
  'hex-rgb': 'HEX ↔ RGB',
  'rgb-hsl': 'RGB ↔ HSL',
  'hsl-hsv': 'HSL ↔ HSV',
  'cmyk-rgb': 'CMYK ↔ RGB',
  'oklch-hsl': 'OKLCH ↔ HSL',
  'lch-lab': 'LCH ↔ LAB',
  'xyz-rgb': 'XYZ ↔ RGB',
  'p3-rgb': 'P3 ↔ RGB'
}

// 方法
const getConverterName = (converterId) => {
  return converterNames[converterId] || converterId.toUpperCase()
}

const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return time.toLocaleDateString()
  }
}

const getPrecisionClass = (deltaE) => {
  if (deltaE < 1) return 'precision-excellent'
  if (deltaE < 2) return 'precision-good'
  if (deltaE < 3) return 'precision-fair'
  return 'precision-poor'
}

const clearHistory = () => {
  if (confirm('确定要清空所有转换历史吗？')) {
    emit('clear')
  }
}
</script>

<style scoped>
.conversion-history {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
}

.history-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0;
  flex: 1;
}

.clear-button {
  padding: 0.25rem 0.75rem;
  background: transparent;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 6px;
  font-size: 0.75rem;
  color: var(--color-gray-600, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background: var(--color-red-50, #fef2f2);
  border-color: var(--color-red-300, #fca5a5);
  color: var(--color-red-600, #dc2626);
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-text {
  font-size: 1rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 0.5rem 0;
}

.empty-hint {
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
  margin: 0;
  line-height: 1.4;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.history-item {
  padding: 1rem;
  background: var(--color-gray-50, #f9fafb);
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  background: var(--color-gray-100, #f3f4f6);
  border-color: var(--color-primary, #3b82f6);
  transform: translateY(-1px);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.converter-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-900, #111827);
}

.timestamp {
  font-size: 0.75rem;
  color: var(--color-gray-500, #6b7280);
}

.conversion-flow {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.color-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.value-text {
  font-size: 0.75rem;
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  color: var(--color-gray-700, #374151);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.arrow {
  color: var(--color-gray-400, #9ca3af);
  font-size: 0.875rem;
  flex-shrink: 0;
}

.precision-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.delta-label {
  color: var(--color-gray-600, #6b7280);
}

.delta-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-weight: 500;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.delta-value.precision-excellent {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-700, #15803d);
}

.delta-value.precision-good {
  background: var(--color-blue-100, #dbeafe);
  color: var(--color-blue-700, #1d4ed8);
}

.delta-value.precision-fair {
  background: var(--color-yellow-100, #fef3c7);
  color: var(--color-yellow-700, #a16207);
}

.delta-value.precision-poor {
  background: var(--color-red-100, #fee2e2);
  color: var(--color-red-700, #b91c1c);
}

/* 滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 4px;
}

.history-list::-webkit-scrollbar-track {
  background: transparent;
}

.history-list::-webkit-scrollbar-thumb {
  background: var(--color-gray-300, #d1d5db);
  border-radius: 2px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400, #9ca3af);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .history-title,
  .converter-name {
    color: white;
  }
  
  .history-header {
    border-color: var(--color-gray-700, #374151);
  }
  
  .clear-button {
    border-color: var(--color-gray-600, #4b5563);
    color: var(--color-gray-300, #d1d5db);
  }
  
  .empty-text {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .empty-hint {
    color: var(--color-gray-400, #9ca3af);
  }
  
  .history-item {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
  }
  
  .history-item:hover {
    background: var(--color-gray-600, #4b5563);
  }
  
  .value-text {
    color: var(--color-gray-200, #e5e7eb);
  }
  
  .timestamp {
    color: var(--color-gray-400, #9ca3af);
  }
  
  .delta-label {
    color: var(--color-gray-300, #d1d5db);
  }
}
</style>
