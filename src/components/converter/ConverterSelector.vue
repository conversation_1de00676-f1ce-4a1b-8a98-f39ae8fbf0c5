<!--
  ConverterSelector.vue - 转换器选择组件
  提供不同转换器类型的选择界面
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="converter-selector">
    <div class="selector-header">
      <h1 class="selector-title">颜色转换工具</h1>
      <p class="selector-description">
        选择您需要的颜色格式转换工具，支持高精度转换和实时预览
      </p>
    </div>
    
    <div class="converter-grid">
      <button
        v-for="converter in converters"
        :key="converter.id"
        @click="$emit('select', converter.id)"
        @keydown.enter="$emit('select', converter.id)"
        class="converter-card"
        :class="{
          active: converter.id === current,
          popular: converter.popular
        }"
        role="button"
        :aria-label="`选择 ${converter.name} 转换器`"
        :aria-pressed="converter.id === current"
        :aria-describedby="`desc-${converter.id}`"
      >
        <!-- 热门标签 -->
        <div v-if="converter.popular" class="popular-badge">
          热门
        </div>
        
        <!-- 转换器图标 -->
        <div class="converter-icon">
          <div class="icon-from">{{ getFormatIcon(converter.id.split('-')[0]) }}</div>
          <div class="icon-arrow">⇄</div>
          <div class="icon-to">{{ getFormatIcon(converter.id.split('-')[1]) }}</div>
        </div>
        
        <!-- 转换器信息 -->
        <div class="converter-info">
          <h3 class="converter-name">{{ converter.name }}</h3>
          <p class="converter-description" :id="`desc-${converter.id}`">{{ converter.description }}</p>
          
          <!-- 元信息 -->
          <div class="converter-meta">
            <span class="difficulty-badge" :class="`difficulty-${converter.difficulty}`">
              {{ getDifficultyText(converter.difficulty) }}
            </span>
            <span v-if="converter.popular" class="usage-count">
              高使用率
            </span>
          </div>
        </div>
        
        <!-- 选中指示器 -->
        <div v-if="converter.id === current" class="selected-indicator">
          ✓
        </div>
      </button>
    </div>
    
    <!-- 快速访问 -->
    <div class="quick-access">
      <h3 class="quick-title">快速访问</h3>
      <div class="quick-buttons">
        <button
          v-for="popular in popularConverters"
          :key="`quick-${popular.id}`"
          @click="$emit('select', popular.id)"
          class="quick-button"
          :class="{ active: popular.id === current }"
        >
          {{ popular.name }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  converters: {
    type: Array,
    required: true
  },
  current: {
    type: String,
    default: ''
  }
})

defineEmits(['select'])

// 格式图标映射
const formatIcons = {
  hex: '#',
  rgb: 'R',
  hsl: 'H',
  hsv: 'V',
  cmyk: 'C',
  oklch: 'O',
  lch: 'L',
  xyz: 'X',
  p3: 'P',
  rec2020: '2',
  lab: 'L'
}

// 计算属性
const popularConverters = computed(() => {
  return props.converters.filter(c => c.popular)
})

// 方法
const getFormatIcon = (format) => {
  return formatIcons[format] || format.charAt(0).toUpperCase()
}

const getDifficultyText = (difficulty) => {
  const texts = {
    beginner: '入门',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家'
  }
  return texts[difficulty] || '未知'
}
</script>

<style scoped>
.converter-selector {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.selector-header {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.selector-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.selector-description {
  font-size: 1.125rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
  line-height: 1.6;
}

.converter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.converter-card {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background: white;
  border: 2px solid var(--color-gray-200, #e5e7eb);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.converter-card:hover {
  border-color: var(--color-primary, #3b82f6);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.converter-card.active {
  border-color: var(--color-primary, #3b82f6);
  background: var(--color-primary-50, #eff6ff);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.converter-card.popular {
  border-color: var(--color-yellow-400, #fbbf24);
}

.popular-badge {
  position: absolute;
  top: -8px;
  right: 1rem;
  background: var(--color-yellow-400, #fbbf24);
  color: var(--color-yellow-900, #78350f);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.converter-icon {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon-from,
.icon-to {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-gray-100, #f3f4f6);
  border-radius: 12px;
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--color-gray-700, #374151);
}

.converter-card.active .icon-from,
.converter-card.active .icon-to {
  background: var(--color-primary-200, #bfdbfe);
  color: var(--color-primary-700, #1d4ed8);
}

.icon-arrow {
  font-size: 1.5rem;
  color: var(--color-gray-400, #9ca3af);
  animation: pulse 2s infinite;
}

.converter-card.active .icon-arrow {
  color: var(--color-primary, #3b82f6);
}

.converter-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.converter-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0;
}

.converter-description {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
  line-height: 1.5;
}

.converter-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.difficulty-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.difficulty-badge.difficulty-beginner {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-700, #15803d);
}

.difficulty-badge.difficulty-intermediate {
  background: var(--color-yellow-100, #fef3c7);
  color: var(--color-yellow-700, #a16207);
}

.difficulty-badge.difficulty-advanced {
  background: var(--color-orange-100, #ffedd5);
  color: var(--color-orange-700, #c2410c);
}

.difficulty-badge.difficulty-expert {
  background: var(--color-red-100, #fee2e2);
  color: var(--color-red-700, #b91c1c);
}

.usage-count {
  font-size: 0.75rem;
  color: var(--color-gray-500, #6b7280);
  font-weight: 500;
}

.selected-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 2rem;
  height: 2rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.875rem;
}

.quick-access {
  text-align: center;
}

.quick-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.quick-buttons {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.quick-button {
  padding: 0.5rem 1rem;
  background: var(--color-gray-100, #f3f4f6);
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700, #374151);
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-button:hover {
  background: var(--color-gray-200, #e5e7eb);
}

.quick-button.active {
  background: var(--color-primary, #3b82f6);
  border-color: var(--color-primary, #3b82f6);
  color: white;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .converter-grid {
    grid-template-columns: 1fr;
  }
  
  .converter-card {
    padding: 1.5rem;
  }
  
  .selector-title {
    font-size: 1.5rem;
  }
  
  .quick-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .quick-button {
    width: 200px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .selector-title,
  .converter-name,
  .quick-title {
    color: white;
  }
  
  .selector-description,
  .converter-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .converter-card {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .icon-from,
  .icon-to {
    background: var(--color-gray-700, #374151);
    color: var(--color-gray-200, #e5e7eb);
  }
  
  .quick-button {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: var(--color-gray-200, #e5e7eb);
  }
  
  .quick-button:hover {
    background: var(--color-gray-600, #4b5563);
  }
}
</style>
