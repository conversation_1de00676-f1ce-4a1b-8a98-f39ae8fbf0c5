<!--
  CodeExporter.vue - 代码导出组件
  将颜色导出为各种代码格式
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="code-exporter">
    <div class="exporter-header">
      <h3 class="exporter-title">代码导出</h3>
      <p class="exporter-description">
        将颜色导出为各种编程语言和框架格式
      </p>
    </div>
    
    <!-- 格式选择器 -->
    <div class="format-selector">
      <button
        v-for="format in exportFormats"
        :key="format.id"
        @click="selectedFormat = format.id"
        class="format-button"
        :class="{ active: selectedFormat === format.id }"
      >
        <div class="format-icon">{{ format.icon }}</div>
        <span class="format-name">{{ format.name }}</span>
      </button>
    </div>
    
    <!-- 代码预览 -->
    <div class="code-preview">
      <div class="preview-header">
        <span class="preview-title">{{ currentFormat.name }} 代码</span>
        <div class="preview-actions">
          <button
            @click="copyCode"
            class="copy-button"
            :class="{ copied: copySuccess }"
          >
            {{ copySuccess ? '已复制!' : '复制代码' }}
          </button>
          <button
            @click="downloadCode"
            class="download-button"
          >
            下载文件
          </button>
        </div>
      </div>
      
      <div class="code-container">
        <pre class="code-block"><code :class="currentFormat.language">{{ generatedCode }}</code></pre>
      </div>
    </div>
    
    <!-- 配置选项 -->
    <div class="export-options">
      <h4 class="options-title">导出选项</h4>
      
      <div class="option-group">
        <label class="option-label">
          <input
            v-model="options.includeComments"
            type="checkbox"
            class="option-checkbox"
          />
          包含注释
        </label>
        
        <label class="option-label">
          <input
            v-model="options.useVariables"
            type="checkbox"
            class="option-checkbox"
          />
          使用变量
        </label>
        
        <label class="option-label">
          <input
            v-model="options.includeAlpha"
            type="checkbox"
            class="option-checkbox"
          />
          包含透明度
        </label>
      </div>
      
      <div v-if="options.useVariables" class="variable-options">
        <div class="input-group">
          <label class="input-label">变量前缀:</label>
          <input
            v-model="options.variablePrefix"
            type="text"
            placeholder="color-"
            class="variable-input"
          />
        </div>
        
        <div class="input-group">
          <label class="input-label">变量名称:</label>
          <input
            v-model="options.variableName"
            type="text"
            placeholder="primary"
            class="variable-input"
          />
        </div>
      </div>
    </div>
    
    <!-- 批量导出 -->
    <div v-if="colorPalette.length > 1" class="batch-export">
      <h4 class="batch-title">批量导出</h4>
      <p class="batch-description">导出整个调色板</p>
      
      <div class="palette-preview">
        <div
          v-for="(color, index) in colorPalette"
          :key="index"
          class="palette-color"
          :style="{ backgroundColor: color.value }"
          :title="color.name || color.value"
        ></div>
      </div>
      
      <button
        @click="exportPalette"
        class="export-palette-button"
      >
        导出调色板
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ColorUtils } from '@/utils/colorUtils'

const props = defineProps({
  color: {
    type: String,
    required: true
  },
  colorPalette: {
    type: Array,
    default: () => []
  }
})

// 响应式数据
const selectedFormat = ref('css')
const copySuccess = ref(false)

const options = ref({
  includeComments: true,
  useVariables: true,
  includeAlpha: false,
  variablePrefix: 'color-',
  variableName: 'primary'
})

// 导出格式配置
const exportFormats = [
  { id: 'css', name: 'CSS', icon: '🎨', language: 'css', extension: 'css' },
  { id: 'scss', name: 'SCSS', icon: '💎', language: 'scss', extension: 'scss' },
  { id: 'tailwind', name: 'Tailwind', icon: '🌊', language: 'javascript', extension: 'js' },
  { id: 'javascript', name: 'JavaScript', icon: '📜', language: 'javascript', extension: 'js' },
  { id: 'typescript', name: 'TypeScript', icon: '🔷', language: 'typescript', extension: 'ts' },
  { id: 'json', name: 'JSON', icon: '📋', language: 'json', extension: 'json' },
  { id: 'swift', name: 'Swift', icon: '🦉', language: 'swift', extension: 'swift' },
  { id: 'kotlin', name: 'Kotlin', icon: '🎯', language: 'kotlin', extension: 'kt' }
]

// 计算属性
const currentFormat = computed(() => {
  return exportFormats.find(f => f.id === selectedFormat.value) || exportFormats[0]
})

const generatedCode = computed(() => {
  return generateCode(selectedFormat.value, props.color)
})

// 方法
const generateCode = (format, color) => {
  const rgb = ColorUtils.parseColor(color)
  if (!rgb) return ''
  
  const { r, g, b } = rgb
  const hex = ColorUtils.rgbToHex(r, g, b)
  const hsl = ColorUtils.rgbToHsl(r, g, b)
  
  const varName = options.value.useVariables 
    ? `${options.value.variablePrefix}${options.value.variableName}`
    : null
  
  const comment = options.value.includeComments 
    ? `/* ${color} - RGB(${r}, ${g}, ${b}) */`
    : ''
  
  switch (format) {
    case 'css':
      return generateCSS(hex, varName, comment)
    case 'scss':
      return generateSCSS(hex, varName, comment)
    case 'tailwind':
      return generateTailwind(hex, varName, comment)
    case 'javascript':
      return generateJavaScript(hex, varName, comment)
    case 'typescript':
      return generateTypeScript(hex, varName, comment)
    case 'json':
      return generateJSON(hex, r, g, b, hsl)
    case 'swift':
      return generateSwift(r, g, b, varName, comment)
    case 'kotlin':
      return generateKotlin(r, g, b, varName, comment)
    default:
      return ''
  }
}

const generateCSS = (hex, varName, comment) => {
  if (options.value.useVariables) {
    return `${comment ? comment + '\n' : ''}:root {
  --${varName}: ${hex};
}

.element {
  color: var(--${varName});
  background-color: var(--${varName});
  border-color: var(--${varName});
}`
  }
  
  return `${comment ? comment + '\n' : ''}.element {
  color: ${hex};
  background-color: ${hex};
  border-color: ${hex};
}`
}

const generateSCSS = (hex, varName, comment) => {
  if (options.value.useVariables) {
    return `${comment ? comment + '\n' : ''}$${varName}: ${hex};

.element {
  color: $${varName};
  background-color: $${varName};
  border-color: $${varName};
  
  &:hover {
    background-color: darken($${varName}, 10%);
  }
}`
  }
  
  return `${comment ? comment + '\n' : ''}.element {
  color: ${hex};
  background-color: ${hex};
  border-color: ${hex};
}`
}

const generateTailwind = (hex, varName, comment) => {
  const configName = varName || 'primary'
  
  return `${comment ? '// ' + comment + '\n' : ''}module.exports = {
  theme: {
    extend: {
      colors: {
        '${configName}': '${hex}',
      }
    }
  }
}

// 使用示例:
// <div className="bg-${configName} text-white">
//   Hello World
// </div>`
}

const generateJavaScript = (hex, varName, comment) => {
  const name = varName || 'primaryColor'
  
  return `${comment ? '// ' + comment + '\n' : ''}const ${name} = '${hex}';

// 使用示例
const styles = {
  backgroundColor: ${name},
  color: ${name},
  borderColor: ${name}
};

export { ${name} };`
}

const generateTypeScript = (hex, varName, comment) => {
  const name = varName || 'primaryColor'
  
  return `${comment ? '// ' + comment + '\n' : ''}export const ${name}: string = '${hex}';

// 类型定义
interface ColorTheme {
  primary: string;
  secondary: string;
  accent: string;
}

// 使用示例
const theme: ColorTheme = {
  primary: ${name},
  secondary: '#ffffff',
  accent: '#000000'
};`
}

const generateJSON = (hex, r, g, b, hsl) => {
  return JSON.stringify({
    name: options.value.variableName || 'primary',
    hex: hex,
    rgb: { r, g, b },
    hsl: { h: hsl.h, s: hsl.s, l: hsl.l },
    formats: {
      css: hex,
      rgb: `rgb(${r}, ${g}, ${b})`,
      hsl: `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`
    }
  }, null, 2)
}

const generateSwift = (r, g, b, varName, comment) => {
  const name = varName || 'primaryColor'
  
  return `${comment ? '// ' + comment + '\n' : ''}import UIKit

extension UIColor {
    static let ${name} = UIColor(
        red: ${(r / 255).toFixed(3)},
        green: ${(g / 255).toFixed(3)},
        blue: ${(b / 255).toFixed(3)},
        alpha: 1.0
    )
}

// 使用示例
view.backgroundColor = .${name}`
}

const generateKotlin = (r, g, b, varName, comment) => {
  const name = varName || 'primaryColor'
  
  return `${comment ? '// ' + comment + '\n' : ''}import android.graphics.Color

object AppColors {
    val ${name} = Color.rgb(${r}, ${g}, ${b})
}

// 使用示例
view.setBackgroundColor(AppColors.${name})`
}

const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(generatedCode.value)
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (error) {
    console.warn('Failed to copy code:', error)
  }
}

const downloadCode = () => {
  const blob = new Blob([generatedCode.value], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `color-export.${currentFormat.value.extension}`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const exportPalette = () => {
  const paletteCode = props.colorPalette.map((color, index) => {
    const name = color.name || `color${index + 1}`
    return generateCode(selectedFormat.value, color.value)
      .replace(options.value.variableName, name)
  }).join('\n\n')
  
  const blob = new Blob([paletteCode], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `color-palette.${currentFormat.value.extension}`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// 监听器
watch(() => props.color, () => {
  copySuccess.value = false
})
</script>

<style scoped>
.code-exporter {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.exporter-header {
  text-align: center;
}

.exporter-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.exporter-description {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
}

.format-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.75rem;
}

.format-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.format-button:hover {
  border-color: var(--color-primary, #3b82f6);
  transform: translateY(-2px);
}

.format-button.active {
  border-color: var(--color-primary, #3b82f6);
  background: var(--color-primary-50, #eff6ff);
}

.format-icon {
  font-size: 1.5rem;
}

.format-name {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-700, #374151);
}

.code-preview {
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 8px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--color-gray-50, #f9fafb);
  border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
}

.preview-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-900, #111827);
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
}

.copy-button,
.download-button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 6px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-button {
  background: var(--color-primary, #3b82f6);
  border-color: var(--color-primary, #3b82f6);
  color: white;
}

.copy-button.copied {
  background: var(--color-green-500, #10b981);
  border-color: var(--color-green-500, #10b981);
}

.download-button {
  background: white;
  color: var(--color-gray-700, #374151);
}

.download-button:hover {
  background: var(--color-gray-100, #f3f4f6);
}

.code-container {
  max-height: 300px;
  overflow-y: auto;
}

.code-block {
  margin: 0;
  padding: 1rem;
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-gray-800, #1f2937);
  background: transparent;
  white-space: pre-wrap;
  word-break: break-all;
}

.export-options {
  padding: 1rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 8px;
}

.options-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.option-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--color-gray-700, #374151);
  cursor: pointer;
}

.option-checkbox {
  width: 1rem;
  height: 1rem;
}

.variable-options {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.input-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.variable-input {
  padding: 0.5rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 4px;
  font-size: 0.875rem;
  width: 120px;
}

.batch-export {
  padding: 1rem;
  background: var(--color-blue-50, #eff6ff);
  border-radius: 8px;
}

.batch-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-blue-900, #1e3a8a);
  margin: 0 0 0.5rem 0;
}

.batch-description {
  font-size: 0.875rem;
  color: var(--color-blue-700, #1d4ed8);
  margin: 0 0 1rem 0;
}

.palette-preview {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.palette-color {
  width: 2rem;
  height: 2rem;
  border-radius: 4px;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.export-palette-button {
  padding: 0.75rem 1.5rem;
  background: var(--color-blue-600, #2563eb);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.export-palette-button:hover {
  background: var(--color-blue-700, #1d4ed8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .format-selector {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  }
  
  .preview-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }
  
  .variable-options {
    flex-direction: column;
  }
  
  .variable-input {
    width: 100%;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .exporter-title,
  .options-title,
  .preview-title {
    color: white;
  }
  
  .exporter-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .format-button {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .format-name {
    color: var(--color-gray-200, #e5e7eb);
  }
  
  .code-preview {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .preview-header {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
  }
  
  .code-block {
    color: var(--color-gray-200, #e5e7eb);
  }
  
  .export-options {
    background: var(--color-gray-800, #1f2937);
  }
  
  .variable-input {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: white;
  }
}
</style>
