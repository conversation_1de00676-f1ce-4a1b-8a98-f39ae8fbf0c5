<!--
  ConverterLayout.vue - 转换器页面布局组件
  提供转换器页面的整体布局和导航结构
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="converter-layout">
    <!-- 页面头部 -->
    <header class="converter-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">颜色转换器</h1>
          <p class="page-description">专业级颜色格式转换工具</p>
        </div>
        <div class="header-right">
          <div class="converter-stats">
            <div class="stat-item">
              <span class="stat-value">{{ totalConversions }}</span>
              <span class="stat-label">转换次数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ supportedFormats }}</span>
              <span class="stat-label">支持格式</span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="converter-main">
      <div class="converter-container">
        <!-- 转换器选择器 -->
        <div class="converter-selector-section">
          <ConverterSelector 
            :current="currentConverter"
            @select="handleConverterSelect"
          />
        </div>

        <!-- 转换器内容 -->
        <div class="converter-content">
          <Suspense>
            <template #default>
              <router-view 
                @color-change="handleColorChange"
                @conversion-complete="handleConversionComplete"
              />
            </template>
            <template #fallback>
              <div class="converter-loading">
                <div class="loading-spinner"></div>
                <p class="loading-text">加载转换器中...</p>
              </div>
            </template>
          </Suspense>
        </div>

        <!-- 侧边栏 -->
        <aside class="converter-sidebar">
          <!-- 转换历史 -->
          <div class="sidebar-section">
            <h3 class="sidebar-title">转换历史</h3>
            <ConversionHistory 
              :history="conversionHistory"
              @restore="handleHistoryRestore"
              @clear="handleHistoryClear"
            />
          </div>

          <!-- 代码导出 -->
          <div class="sidebar-section">
            <h3 class="sidebar-title">代码导出</h3>
            <CodeExporter 
              :color="currentColor"
              @export="handleCodeExport"
            />
          </div>
        </aside>
      </div>
    </main>

    <!-- 页面底部 -->
    <footer class="converter-footer">
      <div class="footer-content">
        <div class="footer-links">
          <router-link to="/wiki" class="footer-link">颜色知识库</router-link>
          <router-link to="/tools" class="footer-link">设计工具</router-link>
          <router-link to="/api" class="footer-link">API 文档</router-link>
        </div>
        <div class="footer-info">
          <span class="footer-text">ColorCode.cc - 专业颜色工具平台</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useColorStore } from '@/stores/colorStore'
import ConverterSelector from './ConverterSelector.vue'
import ConversionHistory from './ConversionHistory.vue'
import CodeExporter from './CodeExporter.vue'

const route = useRoute()
const router = useRouter()
const colorStore = useColorStore()

// 响应式数据
const currentColor = ref('#6366f1')
const totalConversions = ref(0)

// 计算属性
const currentConverter = computed(() => {
  return route.params.type || 'hex-rgb'
})

const supportedFormats = computed(() => {
  return 8 // HEX, RGB, HSL, HSV, CMYK, LAB, OKLCH, XYZ
})

const conversionHistory = computed(() => {
  return colorStore.conversionHistory || []
})

// 方法
const handleConverterSelect = (converterType) => {
  router.push(`/converter/${converterType}`)
}

const handleColorChange = (colorData) => {
  currentColor.value = colorData.input
  colorStore.parseColor(colorData.input)
}

const handleConversionComplete = (conversionData) => {
  // 添加到转换历史
  colorStore.addConversionHistory({
    ...conversionData,
    timestamp: new Date().toISOString(),
    converter: currentConverter.value
  })
  
  totalConversions.value++
}

const handleHistoryRestore = (historyItem) => {
  currentColor.value = historyItem.source.value
  // 触发颜色变化事件
  handleColorChange({ input: historyItem.source.value })
}

const handleHistoryClear = () => {
  colorStore.clearHistory()
}

const handleCodeExport = (exportData) => {
  // 处理代码导出
  console.log('Code exported:', exportData)
}

// 监听路由变化
watch(() => route.params.type, (newType) => {
  if (newType && newType !== currentConverter.value) {
    // 路由参数变化时的处理
  }
})

// 生命周期
onMounted(() => {
  // 初始化转换器
  totalConversions.value = conversionHistory.value.length
})
</script>

<style scoped>
.converter-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--color-gray-50, #f9fafb);
}

.converter-header {
  background: white;
  border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
  padding: 2rem 0;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.page-description {
  font-size: 1rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
}

.header-right {
  flex-shrink: 0;
}

.converter-stats {
  display: flex;
  gap: 2rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary, #3b82f6);
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  margin-top: 0.25rem;
}

.converter-main {
  flex: 1;
  padding: 2rem 0;
}

.converter-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  align-items: start;
}

.converter-selector-section {
  grid-column: 1 / -1;
  margin-bottom: 1rem;
}

.converter-content {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  overflow: hidden;
}

.converter-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--color-gray-200, #e5e7eb);
  border-top: 2px solid var(--color-primary, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: var(--color-gray-600, #6b7280);
  margin: 0;
}

.converter-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-section {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  padding: 1.5rem;
}

.sidebar-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.converter-footer {
  background: white;
  border-top: 1px solid var(--color-gray-200, #e5e7eb);
  padding: 1.5rem 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.footer-link {
  color: var(--color-gray-600, #6b7280);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--color-primary, #3b82f6);
}

.footer-info {
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .converter-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .converter-sidebar {
    order: -1;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .converter-stats {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .converter-container {
    padding: 0 1rem;
  }
  
  .header-content {
    padding: 0 1rem;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-links {
    justify-content: center;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .converter-layout {
    background: var(--color-gray-900, #111827);
  }
  
  .converter-header,
  .converter-content,
  .sidebar-section,
  .converter-footer {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .page-title,
  .sidebar-title {
    color: white;
  }
  
  .page-description,
  .stat-label,
  .loading-text,
  .footer-text {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .footer-link {
    color: var(--color-gray-400, #9ca3af);
  }
  
  .footer-link:hover {
    color: var(--color-primary-400, #60a5fa);
  }
}
</style>
