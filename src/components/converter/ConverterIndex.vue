<!--
  ConverterIndex.vue - 转换器首页组件
  显示所有可用的颜色转换器和快速转换功能
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="converter-index">
    <!-- 页面头部 -->
    <header class="converter-index-header">
      <div class="header-content">
        <h1 class="page-title">专业颜色转换工具</h1>
        <p class="page-description">
          高精度颜色格式转换，支持实时预览和代码导出
        </p>
        
        <!-- 快速转换 -->
        <div class="quick-converter">
          <div class="converter-input">
            <input
              v-model="quickColor"
              type="text"
              placeholder="输入任意颜色值进行快速转换..."
              class="color-input"
              @input="handleQuickConvert"
            />
            <div class="input-icon">🎨</div>
          </div>
          
          <!-- 快速结果 -->
          <div v-if="quickResult" class="quick-results">
            <div
              v-for="format in quickResult"
              :key="format.name"
              class="result-item"
            >
              <span class="format-label">{{ format.name }}</span>
              <code class="format-value">{{ format.value }}</code>
              <button
                @click="copyToClipboard(format.value)"
                class="copy-btn"
                :title="`复制 ${format.name} 值`"
              >
                📋
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 转换器列表 -->
    <main class="converter-index-main">
      <div class="converters-container">
        <!-- 热门转换器 -->
        <section class="converter-category">
          <h2 class="category-title">
            <span class="category-icon">🔥</span>
            热门转换器
          </h2>
          <p class="category-description">
            最常用的颜色格式转换工具
          </p>
          <div class="converter-grid">
            <router-link
              v-for="converter in popularConverters"
              :key="converter.id"
              :to="`/converter/${converter.id}`"
              class="converter-card popular"
            >
              <div class="card-header">
                <div class="converter-icon">{{ converter.icon }}</div>
                <div class="converter-info">
                  <h3 class="converter-name">{{ converter.name }}</h3>
                  <span class="converter-desc">{{ converter.description }}</span>
                </div>
                <div class="popularity-badge">{{ converter.usage }}+ 使用</div>
              </div>
              <div class="converter-preview">
                <div class="preview-input">
                  <span class="preview-label">输入:</span>
                  <code>{{ converter.example.input }}</code>
                </div>
                <div class="preview-arrow">→</div>
                <div class="preview-output">
                  <span class="preview-label">输出:</span>
                  <code>{{ converter.example.output }}</code>
                </div>
              </div>
            </router-link>
          </div>
        </section>

        <!-- 基础转换器 -->
        <section class="converter-category">
          <h2 class="category-title">
            <span class="category-icon">⚡</span>
            基础转换器
          </h2>
          <p class="category-description">
            常用颜色格式之间的转换
          </p>
          <div class="converter-grid">
            <router-link
              v-for="converter in basicConverters"
              :key="converter.id"
              :to="`/converter/${converter.id}`"
              class="converter-card basic"
            >
              <div class="card-header">
                <div class="converter-icon">{{ converter.icon }}</div>
                <div class="converter-info">
                  <h3 class="converter-name">{{ converter.name }}</h3>
                  <span class="converter-desc">{{ converter.description }}</span>
                </div>
                <div class="difficulty-badge beginner">入门</div>
              </div>
              <div class="converter-preview">
                <div class="preview-input">
                  <span class="preview-label">输入:</span>
                  <code>{{ converter.example.input }}</code>
                </div>
                <div class="preview-arrow">→</div>
                <div class="preview-output">
                  <span class="preview-label">输出:</span>
                  <code>{{ converter.example.output }}</code>
                </div>
              </div>
            </router-link>
          </div>
        </section>

        <!-- 高级转换器 -->
        <section class="converter-category">
          <h2 class="category-title">
            <span class="category-icon">🔬</span>
            高级转换器
          </h2>
          <p class="category-description">
            专业级颜色空间转换工具
          </p>
          <div class="converter-grid">
            <router-link
              v-for="converter in advancedConverters"
              :key="converter.id"
              :to="`/converter/${converter.id}`"
              class="converter-card advanced"
            >
              <div class="card-header">
                <div class="converter-icon">{{ converter.icon }}</div>
                <div class="converter-info">
                  <h3 class="converter-name">{{ converter.name }}</h3>
                  <span class="converter-desc">{{ converter.description }}</span>
                </div>
                <div class="difficulty-badge advanced">高级</div>
              </div>
              <div class="converter-preview">
                <div class="preview-input">
                  <span class="preview-label">输入:</span>
                  <code>{{ converter.example.input }}</code>
                </div>
                <div class="preview-arrow">→</div>
                <div class="preview-output">
                  <span class="preview-label">输出:</span>
                  <code>{{ converter.example.output }}</code>
                </div>
              </div>
            </router-link>
          </div>
        </section>
      </div>

      <!-- 侧边栏 -->
      <aside class="converter-sidebar">
        <!-- 转换统计 -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">转换统计</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-value">{{ totalConversions }}</span>
              <span class="stat-label">总转换次数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ supportedFormats }}</span>
              <span class="stat-label">支持格式</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ activeUsers }}</span>
              <span class="stat-label">活跃用户</span>
            </div>
          </div>
        </div>

        <!-- 最近使用 -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">最近使用</h3>
          <div class="recent-converters">
            <router-link
              v-for="converter in recentConverters"
              :key="converter.id"
              :to="`/converter/${converter.id}`"
              class="recent-item"
            >
              <span class="converter-icon">{{ converter.icon }}</span>
              <span class="converter-name">{{ converter.name }}</span>
              <span class="usage-time">{{ converter.lastUsed }}</span>
            </router-link>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">快速链接</h3>
          <nav class="quick-links">
            <router-link to="/wiki" class="quick-link">
              📚 颜色知识库
            </router-link>
            <router-link to="/tools" class="quick-link">
              🛠️ 设计工具
            </router-link>
            <a href="#" class="quick-link">
              💾 批量转换
            </a>
            <a href="#" class="quick-link">
              📊 转换历史
            </a>
          </nav>
        </div>
      </aside>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import ColorParser from '@/scripts/ColorParser'

const colorStore = useColorStore()

// 响应式数据
const quickColor = ref('')
const quickResult = ref(null)

// 统计数据
const totalConversions = ref(125430)
const supportedFormats = ref(12)
const activeUsers = ref(8920)

// 热门转换器
const popularConverters = [
  {
    id: 'hex-rgb',
    name: 'HEX ↔ RGB',
    description: '十六进制与RGB格式互转',
    icon: '#R',
    usage: 15420,
    example: {
      input: '#FF5733',
      output: 'rgb(255, 87, 51)'
    }
  },
  {
    id: 'rgb-hsl',
    name: 'RGB ↔ HSL',
    description: 'RGB与HSL格式互转',
    icon: 'RH',
    usage: 12350,
    example: {
      input: 'rgb(255, 87, 51)',
      output: 'hsl(9, 100%, 60%)'
    }
  },
  {
    id: 'hsl-hsv',
    name: 'HSL ↔ HSV',
    description: 'HSL与HSV格式互转',
    icon: 'HV',
    usage: 8970,
    example: {
      input: 'hsl(9, 100%, 60%)',
      output: 'hsv(9, 80%, 100%)'
    }
  }
]

// 基础转换器
const basicConverters = [
  {
    id: 'cmyk-rgb',
    name: 'CMYK ↔ RGB',
    description: 'CMYK与RGB格式互转',
    icon: 'CR',
    example: {
      input: 'cmyk(0%, 83%, 80%, 0%)',
      output: 'rgb(255, 43, 51)'
    }
  }
]

// 高级转换器
const advancedConverters = [
  {
    id: 'oklch-hsl',
    name: 'OKLCH ↔ HSL',
    description: 'OKLCH与HSL格式互转',
    icon: 'OH',
    example: {
      input: 'oklch(0.7 0.15 180)',
      output: 'hsl(180, 50%, 65%)'
    }
  },
  {
    id: 'lch-lab',
    name: 'LCH ↔ LAB',
    description: 'LCH与LAB格式互转',
    icon: 'LL',
    example: {
      input: 'lch(70% 50 180)',
      output: 'lab(70% -25 25)'
    }
  }
]

// 计算属性
const recentConverters = computed(() => {
  return [
    { id: 'hex-rgb', name: 'HEX ↔ RGB', icon: '#R', lastUsed: '2分钟前' },
    { id: 'rgb-hsl', name: 'RGB ↔ HSL', icon: 'RH', lastUsed: '15分钟前' },
    { id: 'oklch-hsl', name: 'OKLCH ↔ HSL', icon: 'OH', lastUsed: '1小时前' }
  ]
})

// 方法
const handleQuickConvert = () => {
  if (!quickColor.value.trim()) {
    quickResult.value = null
    return
  }

  try {
    const parsed = ColorParser.parseEnhanced ? 
      ColorParser.parseEnhanced(quickColor.value) : 
      ColorParser.parse(quickColor.value)

    if (parsed.mode !== 'unknown' && parsed.mode !== 'error') {
      // 生成多种格式
      quickResult.value = [
        { name: 'HEX', value: '#FF5733' },
        { name: 'RGB', value: 'rgb(255, 87, 51)' },
        { name: 'HSL', value: 'hsl(9, 100%, 60%)' },
        { name: 'OKLCH', value: 'oklch(0.7 0.15 9)' }
      ]
    } else {
      quickResult.value = null
    }
  } catch (error) {
    quickResult.value = null
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // 显示复制成功提示
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
.converter-index {
  min-height: 100vh;
  background: var(--color-gray-50, #f9fafb);
}

.converter-index-header {
  background: white;
  border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
  padding: 3rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.page-description {
  font-size: 1.125rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 2rem 0;
}

.quick-converter {
  max-width: 600px;
  margin: 0 auto;
}

.converter-input {
  position: relative;
  margin-bottom: 1rem;
}

.color-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid var(--color-gray-300, #d1d5db);
  border-radius: 12px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.color-input:focus {
  outline: none;
  border-color: var(--color-primary, #3b82f6);
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.25rem;
}

.quick-results {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--color-gray-100, #f3f4f6);
  border-radius: 8px;
}

.format-label {
  font-weight: 500;
  color: var(--color-gray-700, #374151);
  min-width: 3rem;
}

.format-value {
  flex: 1;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  color: var(--color-gray-800, #1f2937);
}

.copy-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.copy-btn:hover {
  background: var(--color-gray-200, #e5e7eb);
}

.converter-index-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  align-items: start;
}

.converter-category {
  margin-bottom: 3rem;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.category-icon {
  font-size: 1.25rem;
}

.category-description {
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 1.5rem 0;
}

.converter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.converter-card {
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
  padding: 1.5rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.converter-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary, #3b82f6);
}

.converter-card.popular::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #f59e0b, #ef4444);
}

.converter-card.basic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #10b981, #3b82f6);
}

.converter-card.advanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #8b5cf6, #ec4899);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.converter-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--color-gray-100, #f3f4f6);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--color-primary, #3b82f6);
  font-size: 0.875rem;
}

.converter-info {
  flex: 1;
}

.converter-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.25rem 0;
}

.converter-desc {
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
}

.popularity-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background: #fef3c7;
  color: #92400e;
}

.difficulty-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.difficulty-badge.beginner {
  background: #dcfce7;
  color: #166534;
}

.difficulty-badge.advanced {
  background: #fce7f3;
  color: #be185d;
}

.converter-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 8px;
}

.preview-input,
.preview-output {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.preview-label {
  font-size: 0.75rem;
  color: var(--color-gray-500, #6b7280);
  font-weight: 500;
}

.preview-input code,
.preview-output code {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  color: var(--color-gray-800, #1f2937);
  background: white;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--color-gray-200, #e5e7eb);
}

.preview-arrow {
  color: var(--color-primary, #3b82f6);
  font-weight: 600;
  font-size: 1.25rem;
}

.converter-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-section {
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
  padding: 1.5rem;
}

.sidebar-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 8px;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary, #3b82f6);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
}

.recent-converters {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
  transition: background-color 0.2s ease;
}

.recent-item:hover {
  background: var(--color-gray-50, #f9fafb);
}

.recent-item .converter-icon {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 0.75rem;
}

.recent-item .converter-name {
  flex: 1;
  font-weight: 500;
  color: var(--color-gray-900, #111827);
}

.usage-time {
  font-size: 0.75rem;
  color: var(--color-gray-500, #6b7280);
}

.quick-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-link {
  color: var(--color-gray-600, #6b7280);
  text-decoration: none;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quick-link:hover {
  background: var(--color-gray-50, #f9fafb);
  color: var(--color-primary, #3b82f6);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .converter-index-main {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .converter-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .converter-index-main {
    padding: 1rem;
  }
  
  .converter-grid {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .header-content {
    padding: 0 1rem;
  }
  
  .converter-preview {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .preview-arrow {
    transform: rotate(90deg);
  }
}
</style>
