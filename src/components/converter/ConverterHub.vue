<!--
  ConverterHub.vue - 转换器中心组件
  提供颜色转换工具的主要界面
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="converter-hub">
    <!-- 转换器选择器 -->
    <header class="converter-header">
      <ConverterSelector 
        :converters="availableConverters" 
        :current="currentConverter"
        @select="selectConverter" 
      />
    </header>
    
    <!-- 当前转换器 -->
    <main class="converter-container">
      <Suspense>
        <template #default>
          <component 
            :is="currentConverterComponent" 
            :key="converterKey"
            @color-change="handleColorChange"
            @conversion-complete="handleConversionComplete"
          />
        </template>
        <template #fallback>
          <ConverterSkeleton />
        </template>
      </Suspense>
    </main>
    
    <!-- 转换历史 -->
    <aside class="converter-sidebar">
      <ConversionHistory 
        :history="conversionHistory"
        @restore="restoreFromHistory"
      />
    </aside>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useColorStore } from '@/stores/colorStore'
import ColorParser from '@/scripts/ColorParser.js'

// 导入子组件
import ConverterSelector from './ConverterSelector.vue'
import ConversionHistory from './ConversionHistory.vue'
import ConverterSkeleton from './ConverterSkeleton.vue'

const route = useRoute()
const router = useRouter()
const colorStore = useColorStore()

// 响应式状态
const currentConverter = ref(route.params.type || 'hex-rgb')
const converterKey = ref(0)
const conversionHistory = ref([])

// 可用的转换器配置
const availableConverters = [
  { 
    id: 'hex-rgb', 
    name: 'HEX ↔ RGB', 
    component: 'HexRgbConverter',
    description: '十六进制与RGB格式互转',
    difficulty: 'beginner',
    popular: true
  },
  { 
    id: 'rgb-hsl', 
    name: 'RGB ↔ HSL', 
    component: 'RgbHslConverter',
    description: 'RGB与HSL色彩空间转换',
    difficulty: 'intermediate',
    popular: true
  },
  { 
    id: 'cmyk-rgb', 
    name: 'CMYK ↔ RGB', 
    component: 'CmykRgbConverter',
    description: '印刷CMYK与显示RGB转换',
    difficulty: 'advanced',
    popular: false
  },
  { 
    id: 'oklch-hsl', 
    name: 'OKLCH ↔ HSL', 
    component: 'OklchHslConverter',
    description: '现代OKLCH与传统HSL转换',
    difficulty: 'expert',
    popular: false
  }
]

// 计算属性
// 动态加载转换器组件
const currentConverterComponent = computed(() => {
  const converter = availableConverters.find(c => c.id === currentConverter.value)
  return defineAsyncComponent(() => 
    import(`./converters/${converter?.component}.vue`)
  )
})

// 方法
const selectConverter = (converterId) => {
  currentConverter.value = converterId
  converterKey.value++
  router.push(`/converter/${converterId}`)
}

const handleColorChange = (colorData) => {
  // 使用现有的 ColorParser 进行验证
  const parsed = ColorParser.parseEnhanced(colorData.input, {
    enableCache: true,
    enableSuggestions: true,
    enableFuzzyMatch: true
  })
  
  if (parsed.mode !== 'unknown') {
    colorStore.parseColor(colorData.input)
  }
}

const handleConversionComplete = (conversionData) => {
  // 记录转换历史
  conversionHistory.value.unshift({
    id: Date.now(),
    timestamp: new Date(),
    converter: currentConverter.value,
    source: conversionData.source,
    target: conversionData.target,
    deltaE: conversionData.deltaE || null
  })
  
  // 限制历史记录数量
  if (conversionHistory.value.length > 50) {
    conversionHistory.value = conversionHistory.value.slice(0, 50)
  }
  
  // 同步到 store
  colorStore.addConversionHistory(conversionData)
}

const restoreFromHistory = (historyItem) => {
  // 从历史记录恢复转换
  if (historyItem.converter !== currentConverter.value) {
    selectConverter(historyItem.converter)
  }
  
  // 触发颜色变化事件
  handleColorChange({ input: historyItem.source.value })
}

// 监听路由变化
watch(() => route.params.type, (newType) => {
  if (newType && newType !== currentConverter.value) {
    currentConverter.value = newType
    converterKey.value++
  }
})
</script>

<style scoped>
.converter-hub {
  display: grid;
  grid-template-columns: 1fr 320px;
  grid-template-rows: auto 1fr;
  min-height: 100vh;
  background: var(--color-bg-section-light);
  gap: 0;
}

.converter-header {
  grid-column: 1 / -1;
  background: var(--color-bg-card);
  border-bottom: 1px solid var(--color-border);
  padding: var(--spacing-4) var(--spacing-8);
  box-shadow: var(--shadow-sm);
}

.converter-container {
  padding: var(--spacing-8);
  overflow-y: auto;
  max-width: var(--layout-max-width);
  margin: 0 auto;
  width: 100%;
}

.converter-sidebar {
  background: var(--color-bg-card);
  border-left: 1px solid var(--color-border);
  padding: var(--spacing-6);
  overflow-y: auto;
  box-shadow: var(--shadow-sm);
}

/* 响应式设计 - 基于设计系统断点 */
@media (max-width: 1024px) {
  .converter-hub {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }

  .converter-sidebar {
    border-left: none;
    border-top: 1px solid var(--color-border);
    max-height: 320px;
  }
}

@media (max-width: 768px) {
  .converter-header {
    padding: var(--spacing-4);
  }

  .converter-container {
    padding: var(--spacing-4);
  }

  .converter-sidebar {
    padding: var(--spacing-4);
    max-height: 280px;
  }
}

@media (max-width: 480px) {
  .converter-header {
    padding: var(--spacing-3);
  }

  .converter-container {
    padding: var(--spacing-3);
  }

  .converter-sidebar {
    padding: var(--spacing-3);
    max-height: 240px;
  }
}
</style>
