<!--
  ColorWheel.vue - 交互式色轮组件
  使用 Canvas API 实现 HSL 色彩空间可视化和拖拽选择
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="color-wheel-container">
    <div class="wheel-wrapper">
      <!-- 主色轮 Canvas -->
      <canvas
        ref="wheelCanvas"
        :width="wheelSize"
        :height="wheelSize"
        class="color-wheel"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseUp"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      ></canvas>
      
      <!-- 色相指示器 -->
      <div
        class="hue-indicator"
        :style="hueIndicatorStyle"
      ></div>
      
      <!-- 饱和度/亮度选择器 -->
      <canvas
        ref="slCanvas"
        :width="slSize"
        :height="slSize"
        class="sl-picker"
        :style="slPickerStyle"
        @mousedown="handleSLMouseDown"
        @mousemove="handleSLMouseMove"
        @mouseup="handleSLMouseUp"
        @mouseleave="handleSLMouseUp"
        @touchstart="handleSLTouchStart"
        @touchmove="handleSLTouchMove"
        @touchend="handleSLTouchEnd"
      ></canvas>
      
      <!-- 饱和度/亮度指示器 -->
      <div
        class="sl-indicator"
        :style="slIndicatorStyle"
      ></div>
    </div>
    
    <!-- 颜色信息显示 -->
    <div class="color-info">
      <div class="current-color" :style="{ backgroundColor: currentColor }"></div>
      <div class="color-values">
        <div class="value-item">
          <span class="value-label">HSL:</span>
          <span class="value-text">{{ hslString }}</span>
        </div>
        <div class="value-item">
          <span class="value-label">HEX:</span>
          <span class="value-text">{{ hexString }}</span>
        </div>
        <div class="value-item">
          <span class="value-label">RGB:</span>
          <span class="value-text">{{ rgbString }}</span>
        </div>
      </div>
    </div>
    
    <!-- 数值输入控制 -->
    <div class="manual-controls">
      <div class="control-group">
        <label class="control-label">色相 (H)</label>
        <input
          v-model.number="hue"
          type="number"
          min="0"
          max="360"
          class="control-input"
          @input="updateFromInputs"
        />
        <span class="control-unit">°</span>
      </div>
      
      <div class="control-group">
        <label class="control-label">饱和度 (S)</label>
        <input
          v-model.number="saturation"
          type="number"
          min="0"
          max="100"
          class="control-input"
          @input="updateFromInputs"
        />
        <span class="control-unit">%</span>
      </div>
      
      <div class="control-group">
        <label class="control-label">亮度 (L)</label>
        <input
          v-model.number="lightness"
          type="number"
          min="0"
          max="100"
          class="control-input"
          @input="updateFromInputs"
        />
        <span class="control-unit">%</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ColorUtils } from '@/utils/colorUtils'

const props = defineProps({
  modelValue: {
    type: String,
    default: 'hsl(0, 100%, 50%)'
  },
  size: {
    type: Number,
    default: 300
  }
})

const emit = defineEmits(['update:modelValue', 'color-change'])

// 响应式数据
const wheelCanvas = ref(null)
const slCanvas = ref(null)
const hue = ref(0)
const saturation = ref(100)
const lightness = ref(50)

const isDraggingWheel = ref(false)
const isDraggingSL = ref(false)

// 计算属性
const wheelSize = computed(() => props.size)
const slSize = computed(() => Math.round(props.size * 0.3))
const wheelRadius = computed(() => wheelSize.value / 2)
const slRadius = computed(() => slSize.value / 2)

const currentColor = computed(() => {
  return `hsl(${hue.value}, ${saturation.value}%, ${lightness.value}%)`
})

const hslString = computed(() => {
  return `hsl(${Math.round(hue.value)}, ${Math.round(saturation.value)}%, ${Math.round(lightness.value)}%)`
})

const hexString = computed(() => {
  return ColorUtils.hslToHex(hue.value, saturation.value, lightness.value)
})

const rgbString = computed(() => {
  const rgb = ColorUtils.hslToRgb(hue.value, saturation.value, lightness.value)
  return `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`
})

// 指示器位置
const hueIndicatorStyle = computed(() => {
  const angle = (hue.value - 90) * Math.PI / 180
  const radius = wheelRadius.value - 20
  const x = wheelRadius.value + radius * Math.cos(angle)
  const y = wheelRadius.value + radius * Math.sin(angle)
  
  return {
    left: `${x - 8}px`,
    top: `${y - 8}px`
  }
})

const slPickerStyle = computed(() => {
  const x = wheelRadius.value - slRadius.value
  const y = wheelRadius.value - slRadius.value
  
  return {
    left: `${x}px`,
    top: `${y}px`
  }
})

const slIndicatorStyle = computed(() => {
  const baseX = wheelRadius.value - slRadius.value
  const baseY = wheelRadius.value - slRadius.value
  
  const x = baseX + (saturation.value / 100) * slSize.value
  const y = baseY + (1 - lightness.value / 100) * slSize.value
  
  return {
    left: `${x - 6}px`,
    top: `${y - 6}px`
  }
})

// 方法
const drawColorWheel = () => {
  const canvas = wheelCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  const centerX = wheelRadius.value
  const centerY = wheelRadius.value
  const radius = wheelRadius.value - 30
  
  // 清除画布
  ctx.clearRect(0, 0, wheelSize.value, wheelSize.value)
  
  // 绘制色相环
  for (let angle = 0; angle < 360; angle += 1) {
    const startAngle = (angle - 1) * Math.PI / 180
    const endAngle = angle * Math.PI / 180
    
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, startAngle, endAngle)
    ctx.arc(centerX, centerY, radius - 20, endAngle, startAngle, true)
    ctx.closePath()
    
    ctx.fillStyle = `hsl(${angle}, 100%, 50%)`
    ctx.fill()
  }
  
  // 绘制内圆边框
  ctx.beginPath()
  ctx.arc(centerX, centerY, radius - 20, 0, 2 * Math.PI)
  ctx.strokeStyle = '#ffffff'
  ctx.lineWidth = 2
  ctx.stroke()
  
  // 绘制外圆边框
  ctx.beginPath()
  ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
  ctx.strokeStyle = '#ffffff'
  ctx.lineWidth = 2
  ctx.stroke()
}

const drawSLPicker = () => {
  const canvas = slCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  const size = slSize.value
  
  // 清除画布
  ctx.clearRect(0, 0, size, size)
  
  // 创建饱和度-亮度渐变
  const imageData = ctx.createImageData(size, size)
  const data = imageData.data
  
  for (let y = 0; y < size; y++) {
    for (let x = 0; x < size; x++) {
      const s = (x / size) * 100
      const l = ((size - y) / size) * 100
      
      const rgb = ColorUtils.hslToRgb(hue.value, s, l)
      const index = (y * size + x) * 4
      
      data[index] = rgb.r     // Red
      data[index + 1] = rgb.g // Green
      data[index + 2] = rgb.b // Blue
      data[index + 3] = 255   // Alpha
    }
  }
  
  ctx.putImageData(imageData, 0, 0)
  
  // 绘制边框
  ctx.strokeStyle = '#ffffff'
  ctx.lineWidth = 2
  ctx.strokeRect(0, 0, size, size)
}

const getMousePosition = (event, canvas) => {
  const rect = canvas.getBoundingClientRect()
  return {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
}

const getTouchPosition = (event, canvas) => {
  const rect = canvas.getBoundingClientRect()
  const touch = event.touches[0] || event.changedTouches[0]
  return {
    x: touch.clientX - rect.left,
    y: touch.clientY - rect.top
  }
}

const updateHueFromPosition = (x, y) => {
  const centerX = wheelRadius.value
  const centerY = wheelRadius.value
  const dx = x - centerX
  const dy = y - centerY
  const distance = Math.sqrt(dx * dx + dy * dy)
  
  // 检查是否在色相环范围内
  const innerRadius = wheelRadius.value - 50
  const outerRadius = wheelRadius.value - 10
  
  if (distance >= innerRadius && distance <= outerRadius) {
    let angle = Math.atan2(dy, dx) * 180 / Math.PI + 90
    if (angle < 0) angle += 360
    hue.value = Math.round(angle) % 360
    emitColorChange()
  }
}

const updateSLFromPosition = (x, y) => {
  const size = slSize.value
  const newSaturation = Math.max(0, Math.min(100, (x / size) * 100))
  const newLightness = Math.max(0, Math.min(100, ((size - y) / size) * 100))
  
  saturation.value = Math.round(newSaturation)
  lightness.value = Math.round(newLightness)
  emitColorChange()
}

// 鼠标事件处理
const handleMouseDown = (event) => {
  isDraggingWheel.value = true
  const pos = getMousePosition(event, wheelCanvas.value)
  updateHueFromPosition(pos.x, pos.y)
}

const handleMouseMove = (event) => {
  if (isDraggingWheel.value) {
    const pos = getMousePosition(event, wheelCanvas.value)
    updateHueFromPosition(pos.x, pos.y)
  }
}

const handleMouseUp = () => {
  isDraggingWheel.value = false
}

const handleSLMouseDown = (event) => {
  isDraggingSL.value = true
  const pos = getMousePosition(event, slCanvas.value)
  updateSLFromPosition(pos.x, pos.y)
}

const handleSLMouseMove = (event) => {
  if (isDraggingSL.value) {
    const pos = getMousePosition(event, slCanvas.value)
    updateSLFromPosition(pos.x, pos.y)
  }
}

const handleSLMouseUp = () => {
  isDraggingSL.value = false
}

// 触摸事件处理
const handleTouchStart = (event) => {
  event.preventDefault()
  isDraggingWheel.value = true
  const pos = getTouchPosition(event, wheelCanvas.value)
  updateHueFromPosition(pos.x, pos.y)
}

const handleTouchMove = (event) => {
  event.preventDefault()
  if (isDraggingWheel.value) {
    const pos = getTouchPosition(event, wheelCanvas.value)
    updateHueFromPosition(pos.x, pos.y)
  }
}

const handleTouchEnd = (event) => {
  event.preventDefault()
  isDraggingWheel.value = false
}

const handleSLTouchStart = (event) => {
  event.preventDefault()
  isDraggingSL.value = true
  const pos = getTouchPosition(event, slCanvas.value)
  updateSLFromPosition(pos.x, pos.y)
}

const handleSLTouchMove = (event) => {
  event.preventDefault()
  if (isDraggingSL.value) {
    const pos = getTouchPosition(event, slCanvas.value)
    updateSLFromPosition(pos.x, pos.y)
  }
}

const handleSLTouchEnd = (event) => {
  event.preventDefault()
  isDraggingSL.value = false
}

const updateFromInputs = () => {
  // 确保数值在有效范围内
  hue.value = Math.max(0, Math.min(360, hue.value || 0))
  saturation.value = Math.max(0, Math.min(100, saturation.value || 0))
  lightness.value = Math.max(0, Math.min(100, lightness.value || 0))
  
  emitColorChange()
  nextTick(() => {
    drawSLPicker()
  })
}

const emitColorChange = () => {
  const color = currentColor.value
  emit('update:modelValue', color)
  emit('color-change', {
    hsl: { h: hue.value, s: saturation.value, l: lightness.value },
    hex: hexString.value,
    rgb: rgbString.value,
    color: color
  })
}

const parseInitialColor = () => {
  if (props.modelValue) {
    const hsl = ColorUtils.parseHsl(props.modelValue)
    if (hsl) {
      hue.value = hsl.h
      saturation.value = hsl.s
      lightness.value = hsl.l
    }
  }
}

// 监听器
watch(() => props.modelValue, () => {
  parseInitialColor()
  nextTick(() => {
    drawSLPicker()
  })
})

watch(hue, () => {
  nextTick(() => {
    drawSLPicker()
  })
})

// 生命周期
onMounted(() => {
  parseInitialColor()
  drawColorWheel()
  drawSLPicker()
})
</script>

<style scoped>
.color-wheel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  user-select: none;
}

.wheel-wrapper {
  position: relative;
  display: inline-block;
}

.color-wheel,
.sl-picker {
  display: block;
  cursor: crosshair;
  border-radius: 50%;
}

.sl-picker {
  position: absolute;
  border-radius: 8px;
  cursor: crosshair;
}

.hue-indicator {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  pointer-events: none;
  transform: translate(-50%, -50%);
}

.sl-indicator {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  pointer-events: none;
  transform: translate(-50%, -50%);
}

.color-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 8px;
}

.current-color {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.color-values {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.value-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.value-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
  min-width: 40px;
}

.value-text {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  color: var(--color-gray-900, #111827);
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.manual-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.control-input {
  width: 60px;
  padding: 0.5rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 4px;
  text-align: center;
  font-size: 0.875rem;
}

.control-input:focus {
  outline: none;
  border-color: var(--color-primary, #3b82f6);
}

.control-unit {
  font-size: 0.75rem;
  color: var(--color-gray-500, #6b7280);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .color-info {
    flex-direction: column;
    text-align: center;
  }
  
  .manual-controls {
    justify-content: center;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .color-info {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .value-text {
    background: var(--color-gray-700, #374151);
    color: white;
  }
  
  .control-input {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: white;
  }
}
</style>
