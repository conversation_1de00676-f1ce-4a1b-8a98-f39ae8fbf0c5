<template>
  <div class="landing-page">
    <main>
      <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <div class="hero-badges">
            <span class="badge badge-success">
              <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              专业级精度保障 ΔE≤0.5
            </span>
            <span class="badge badge-info">
              <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
              </svg>
              极速响应 &lt;50ms
            </span>
            <span class="badge badge-warning">
              <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              OKLCH 标准领先支持
            </span>
          </div>
          
          <h1 class="hero-title">
            专业级在线颜色工具平台
            <span class="hero-subtitle">ColorCode.cc</span>
          </h1>
          
          <p class="hero-description">
            为 UI/UX 设计师、前端开发者、品牌营销团队提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务。
            支持从基础 HEX/RGB 转换到专业印刷级 LAB/CMYK 转换的全场景覆盖。
          </p>
          
          <div class="hero-actions">
            <button class="btn btn-primary btn-lg" @click="startTrial">
              <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
              </svg>
              立即开始使用
            </button>
            <button class="btn btn-outline btn-lg" @click="viewDemo">
              <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
              </svg>
              查看演示
            </button>
          </div>
        </div>
        
        <div class="hero-visual">
          <div class="color-demo-container">
            <div class="color-picker-demo">
              <div class="demo-input">
                <input
                  ref="colorInputRef"
                  v-model="demoColor"
                  type="text"
                  :placeholder="inputPlaceholder"
                  class="demo-color-input"
                  :class="inputClasses"
                  @click="handleInputClick"
                  @focus="handleInputFocus"
                  @paste="handleInputPaste"
                  @input="handleInputChange"
                  @blur="processColorInput"
                />

                <!-- 错误信息和智能建议 -->
                <div v-if="colorError" class="error-message">
                  <div class="error-title">
                    <span class="error-icon">⚠️</span>
                    {{ getErrorTitle(errorType) }}
                  </div>
                  <div class="error-description">{{ colorError }}</div>

                  <!-- 智能建议 -->
                  <div v-if="suggestions.length > 0" class="suggestions">
                    <div class="suggestions-title">💡 建议：</div>
                    <ul class="suggestions-list">
                      <li
                        v-for="(suggestion, index) in suggestions"
                        :key="index"
                        class="suggestion-item"
                        @click="applySuggestion(suggestion)"
                      >
                        {{ suggestion }}
                      </li>
                    </ul>
                  </div>
                </div>

                <!-- 自动修正提示 -->
                <div v-if="correctionInfo" class="correction-info">
                  <span class="correction-icon">🔧</span>
                  已自动修正：{{ correctionInfo.original }} → {{ correctionInfo.corrected }}
                </div>

                <!-- 格式检测结果 -->
                <div v-if="detectedFormat && !colorError" class="format-detected">
                  <span class="format-icon">✨</span>
                  检测到格式: {{ detectedFormat }}
                  <span v-if="confidence < 1" class="confidence">(置信度: {{ Math.round(confidence * 100) }}%)</span>
                </div>
              </div>
              <div class="demo-outputs">
                <div
                  class="demo-output"
                  v-for="format in demoFormats"
                  :key="format.name"
                  :class="{ 'copied': copiedFormat === format.name }"
                  @click="copyToClipboard(format.value, format.name)"
                  :title="`点击复制 ${format.name} 值`"
                >
                  <span class="format-label">{{ format.name }}</span>
                  <span class="format-value">{{ format.value }}</span>
                  <span class="copy-icon" v-if="copiedFormat === format.name">✓</span>
                  <span class="copy-icon" v-else>📋</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">核心功能特性</h2>
          <p class="section-description">
            基于 Vue 3.x + Vite 7.x + WebAssembly 技术栈，提供专业级颜色处理能力
          </p>
        </div>
        
        <div class="features-grid">
          <div class="feature-card" v-for="feature in coreFeatures" :key="feature.id">
            <div class="feature-icon" :class="`icon-${feature.color}`">
              <component :is="feature.icon" class="icon" />
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
            <div class="feature-specs">
              <span class="spec-badge" v-for="spec in feature.specs" :key="spec">
                {{ spec }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- User Groups Section -->
    <section class="user-groups-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">为不同用户群体量身定制</h2>
          <p class="section-description">
            针对四大核心用户群体，提供差异化的专业功能
          </p>
        </div>
        
        <div class="user-groups-tabs">
          <div class="tab-nav">
            <button 
              v-for="group in userGroups" 
              :key="group.id"
              :class="['tab-btn', { active: activeUserGroup === group.id }]"
              @click="activeUserGroup = group.id"
            >
              <component :is="group.icon" class="icon" />
              {{ group.name }}
            </button>
          </div>
          
          <div class="tab-content">
            <div v-if="currentUserGroup" class="user-group-content">
              <div class="group-info">
                <h3 class="group-title">{{ currentUserGroup.title }}</h3>
                <p class="group-description">{{ currentUserGroup.description }}</p>
                <ul class="group-features">
                  <li v-for="feature in currentUserGroup.features" :key="feature">
                    <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    {{ feature }}
                  </li>
                </ul>
              </div>
              <div class="group-visual">
                <div class="demo-showcase">
                  <div class="showcase-item" v-for="demo in currentUserGroup.demos" :key="demo.title">
                    <h4>{{ demo.title }}</h4>
                    <div class="demo-preview" :style="{ backgroundColor: demo.color }">
                      <span>{{ demo.value }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Technical Advantages Section -->
    <section class="tech-advantages-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">技术优势</h2>
          <p class="section-description">
            基于现代 Web 技术栈，确保性能与精度的完美平衡
          </p>
        </div>
        
        <div class="tech-grid">
          <div class="tech-card" v-for="tech in techAdvantages" :key="tech.id">
            <div class="tech-header">
              <div class="tech-icon">
                <component :is="tech.icon" class="icon" />
              </div>
              <div class="tech-info">
                <h3 class="tech-title">{{ tech.title }}</h3>
                <span class="tech-version">{{ tech.version }}</span>
              </div>
            </div>
            <p class="tech-description">{{ tech.description }}</p>
            <div class="tech-metrics">
              <div class="metric" v-for="metric in tech.metrics" :key="metric.label">
                <span class="metric-value">{{ metric.value }}</span>
                <span class="metric-label">{{ metric.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-card" v-for="stat in platformStats" :key="stat.id">
            <div class="stat-number">{{ stat.number }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-description">{{ stat.description }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">选择适合的方案</h2>
          <p class="section-description">
            从个人开发者到企业团队，我们为每个需求层级提供专业解决方案
          </p>
        </div>
        
        <div class="pricing-grid">
          <div class="pricing-card" v-for="plan in pricingPlans" :key="plan.id" :class="{ featured: plan.featured }">
            <div class="plan-header">
              <h3 class="plan-name">{{ plan.name }}</h3>
              <div class="plan-price">
                <span class="price-currency">¥</span>
                <span class="price-amount">{{ plan.price }}</span>
                <span class="price-period">{{ plan.period }}</span>
              </div>
              <p class="plan-description">{{ plan.description }}</p>
            </div>
            
            <div class="plan-features">
              <ul>
                <li v-for="feature in plan.features" :key="feature">
                  <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                  {{ feature }}
                </li>
              </ul>
            </div>
            
            <div class="plan-action">
              <button :class="['btn', plan.featured ? 'btn-primary' : 'btn-outline']" @click="selectPlan(plan)">
                {{ plan.buttonText }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2 class="cta-title">准备开始您的专业颜色管理之旅？</h2>
          <p class="cta-description">
            立即体验 ColorCode.cc 的强大功能，提升您的设计和开发效率
          </p>
          <div class="cta-actions">
            <button class="btn btn-primary btn-lg" @click="startTrial">
              免费开始使用
            </button>
            <button class="btn btn-secondary btn-lg" @click="contactSales">
              联系销售团队
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer Section -->
    <footer class="footer-section">
      <div class="container">
        <div class="footer-content">
          <!-- 主要导航 -->
          <div class="footer-nav">
            <div class="footer-nav-group">
              <h3 class="footer-nav-title">核心工具</h3>
              <ul class="footer-nav-list">
                <li>
                  <router-link to="/converter" class="footer-nav-link">
                    <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
                    </svg>
                    专业转换器工具
                  </router-link>
                </li>
                <li>
                  <router-link to="/wiki" class="footer-nav-link">
                    <svg class="icon" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                    </svg>
                    Wiki 知识库系统
                  </router-link>
                </li>
              </ul>
            </div>

            <div class="footer-nav-group">
              <h3 class="footer-nav-title">颜色格式</h3>
              <ul class="footer-nav-list">
                <li><router-link to="/wiki/hex" class="footer-nav-link">HEX 十六进制</router-link></li>
                <li><router-link to="/wiki/rgb" class="footer-nav-link">RGB 三原色</router-link></li>
                <li><router-link to="/wiki/hsl" class="footer-nav-link">HSL 色相饱和度</router-link></li>
                <li><router-link to="/wiki/oklch" class="footer-nav-link">OKLCH 感知色彩</router-link></li>
              </ul>
            </div>

            <div class="footer-nav-group">
              <h3 class="footer-nav-title">转换工具</h3>
              <ul class="footer-nav-list">
                <li><router-link to="/converter/hex-rgb" class="footer-nav-link">HEX ↔ RGB</router-link></li>
                <li><router-link to="/converter/rgb-hsl" class="footer-nav-link">RGB ↔ HSL</router-link></li>
                <li><router-link to="/converter/hsl-hsv" class="footer-nav-link">HSL ↔ HSV</router-link></li>
                <li><router-link to="/converter/oklch-hsl" class="footer-nav-link">OKLCH ↔ HSL</router-link></li>
              </ul>
            </div>

            <div class="footer-nav-group">
              <h3 class="footer-nav-title">关于我们</h3>
              <ul class="footer-nav-list">
                <li><a href="#about" class="footer-nav-link">产品介绍</a></li>
                <li><a href="#contact" class="footer-nav-link">联系我们</a></li>
                <li><a href="#privacy" class="footer-nav-link">隐私政策</a></li>
                <li><a href="#terms" class="footer-nav-link">服务条款</a></li>
              </ul>
            </div>
          </div>

          <!-- 分隔线 -->
          <div class="footer-divider"></div>

          <!-- 底部信息 -->
          <div class="footer-bottom">
            <div class="footer-info">
              <div class="footer-logo">
                <span class="logo-text">ColorCode.cc</span>
                <span class="logo-version">v2.0</span>
              </div>
              <p class="footer-description">
                专业级在线颜色工具平台，为设计师和开发者提供高精度颜色转换服务
              </p>
            </div>

            <div class="footer-meta">
              <p class="copyright">
                © 2024 ColorCode.cc. All rights reserved.
              </p>
              <div class="footer-badges">
                <span class="badge badge-outline">Phase 1 完成</span>
                <span class="badge badge-outline">Wiki 知识库</span>
                <span class="badge badge-outline">专业转换器</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
    </main>
  </div>
</template>

<script setup>
/**
 * ColorCode.cc Landing Page Component
 * 基于 Vue 3.x Composition API 构建的专业级颜色工具平台首页
 * 
 * 技术栈：Vue 3.x + Vite 7.x + JavaScript ES6+
 * 设计系统：遵循 colorcode_design_system.json 规范
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import chroma from 'chroma-js'
import ColorParser from '../scripts/ColorParser.js'

// ============================================================================
// 路由和组合式API
// ============================================================================

/**
 * Vue Router 实例
 */
const router = useRouter()

// ============================================================================
// 响应式数据定义
// ============================================================================

/**
 * 演示颜色值，用于 Hero 区域的实时颜色转换展示
 * @type {import('vue').Ref<string>}
 */
const demoColor = ref('#6366f1')

/**
 * 颜色输入错误信息
 * @type {import('vue').Ref<string>}
 */
const colorError = ref('')

/**
 * 检测到的颜色格式
 * @type {import('vue').Ref<string>}
 */
const detectedFormat = ref('')

/**
 * 当前颜色是否有效
 * @type {import('vue').Ref<boolean>}
 */
const isValidColor = ref(true)

/**
 * 智能建议列表
 * @type {import('vue').Ref<Array<string>>}
 */
const suggestions = ref([])

/**
 * 自动修正信息
 * @type {import('vue').Ref<Object|null>}
 */
const correctionInfo = ref(null)

/**
 * 解析置信度
 * @type {import('vue').Ref<number>}
 */
const confidence = ref(1)

/**
 * 错误类型
 * @type {import('vue').Ref<string>}
 */
const errorType = ref('')

/**
 * 当前已复制的格式名称（用于显示复制成功状态）
 * @type {import('vue').Ref<string>}
 */
const copiedFormat = ref('')

/**
 * 颜色输入框引用
 * @type {import('vue').Ref<HTMLInputElement>}
 */
const colorInputRef = ref(null)

/**
 * 是否正在处理粘贴
 * @type {import('vue').Ref<boolean>}
 */
const isPasting = ref(false)

/**
 * 输入变化防抖定时器
 * @type {import('vue').Ref<number|null>}
 */
const inputDebounceTimer = ref(null)

/**
 * 动态输入提示
 * @type {import('vue').ComputedRef<string>}
 */
const inputPlaceholder = computed(() => {
  // 安全获取支持的格式
  let formats = []
  try {
    if (typeof ColorParser.getSupportedFormats === 'function') {
      formats = ColorParser.getSupportedFormats()
    } else {
      // 降级到默认格式列表
      formats = ['hex', 'rgb', 'hsl', 'hsv', 'cmyk', 'oklch', 'keyword']
    }
  } catch (error) {
    formats = ['hex', 'rgb', 'hsl', 'hsv', 'cmyk', 'oklch', 'keyword']
  }

  const examples = {
    hex: '#ff0000',
    rgb: 'rgb(255,0,0)',
    hsl: 'hsl(0,100%,50%)',
    lch: 'lch(50% 20 180)',
    p3: 'color(display-p3 1 0 0)',
    oklch: 'oklch(0.5 0.2 0)',
    keyword: 'red'
  }

  const randomFormat = formats[Math.floor(Math.random() * Math.min(formats.length, 7))]
  const example = examples[randomFormat] || '#6366f1'
  return `输入颜色值，如 ${example}，支持 ${formats.length} 种格式`
})

/**
 * 增强的输入样式类
 * @type {import('vue').ComputedRef<Object>}
 */
const inputClasses = computed(() => ({
  'error': colorError.value,
  'success': isValidColor.value && !colorError.value,
  'corrected': correctionInfo.value,
  'low-confidence': confidence.value < 0.8,
  'pasting': isPasting.value
}))

/**
 * 当前激活的用户群体标签页
 * @type {import('vue').Ref<string>}
 */
const activeUserGroup = ref('designers')

/**
 * 当前激活的用户群体数据
 * @type {import('vue').ComputedRef<Object>}
 */
const currentUserGroup = computed(() => {
  return userGroups.find(group => group.id === activeUserGroup.value)
})

// ============================================================================
// 计算属性 - 实时颜色格式转换演示
// ============================================================================

/**
 * 根据输入的颜色值，使用 ColorParser 和 chroma-js 进行高精度颜色格式转换
 * 展示平台的核心转换能力
 */
const demoFormats = computed(() => {
  // 首先检查输入是否为空或过短
  const trimmedValue = demoColor.value?.trim() || ''
  if (!trimmedValue || trimmedValue.length < 3) {
    // 输入为空或过短，显示默认格式
    return [
      { name: 'HEX', value: '#6366f1' },
      { name: 'RGB', value: 'rgb(99, 102, 241)' },
      { name: 'HSL', value: 'hsl(239, 84%, 67%)' },
      { name: 'CMYK', value: 'cmyk(59%, 58%, 0%, 5%)' },
      { name: 'OKLCH', value: 'oklch(0.525 0.15 239)' }
    ]
  }

  // 使用 ColorParser 进行颜色格式验证
  // 这样可以避免在前端重复验证逻辑，保持架构一致性
  try {
    let parseResult

    // 尝试使用增强解析，如果不可用则使用基础解析
    if (typeof ColorParser.parseEnhanced === 'function') {
      parseResult = ColorParser.parseEnhanced(trimmedValue, {
        enableCache: true,
        enableSuggestions: false,
        enableFuzzyMatch: false,
        strictMode: true
      })
    } else {
      // 降级到基础解析（测试环境兼容）
      parseResult = ColorParser.parse(trimmedValue)
    }

    // 如果 ColorParser 无法解析，说明格式无效
    if (parseResult.mode === 'error' || parseResult.mode === 'unknown') {
      return [
        { name: 'HEX', value: '#6366f1' },
        { name: 'RGB', value: 'rgb(99, 102, 241)' },
        { name: 'HSL', value: 'hsl(239, 84%, 67%)' },
        { name: 'CMYK', value: 'cmyk(59%, 58%, 0%, 5%)' },
        { name: 'OKLCH', value: 'oklch(0.525 0.15 239)' }
      ]
    }

    // ColorParser 验证通过，使用 chroma-js 进行转换
    const color = chroma(trimmedValue)

    // 获取各种格式的颜色值
    const hex = color.hex().toUpperCase()
    const [r, g, b] = color.rgb()
    const [h, s, l] = color.hsl()

    // 计算 CMYK 值（chroma-js 可能不直接支持，需要手动计算）
    let cmykValue = 'cmyk(0%, 0%, 0%, 0%)'
    try {
      if (color.cmyk) {
        const [c, m, y, k] = color.cmyk()
        cmykValue = `cmyk(${Math.round(c)}%, ${Math.round(m)}%, ${Math.round(y)}%, ${Math.round(k)}%)`
      } else {
        // 手动计算 CMYK
        const rNorm = r / 255
        const gNorm = g / 255
        const bNorm = b / 255

        const k = 1 - Math.max(rNorm, gNorm, bNorm)
        const c = k === 1 ? 0 : (1 - rNorm - k) / (1 - k)
        const m = k === 1 ? 0 : (1 - gNorm - k) / (1 - k)
        const y = k === 1 ? 0 : (1 - bNorm - k) / (1 - k)

        cmykValue = `cmyk(${Math.round(c * 100)}%, ${Math.round(m * 100)}%, ${Math.round(y * 100)}%, ${Math.round(k * 100)}%)`
      }
    } catch (e) {
      // CMYK 计算失败时使用默认值
      cmykValue = 'cmyk(0%, 0%, 0%, 0%)'
    }

    // 尝试获取 OKLCH 值（如果 chroma-js 支持）
    let oklchValue = 'oklch(0.525 0.15 239)' // 默认值
    try {
      if (color.oklch) {
        const [oklchL, oklchC, oklchH] = color.oklch()
        oklchValue = `oklch(${(oklchL || 0).toFixed(3)} ${(oklchC || 0).toFixed(3)} ${Math.round(oklchH || 0)})`
      }
    } catch (e) {
      // OKLCH 不支持时使用近似值
      const [labL] = color.lab()
      oklchValue = `oklch(${(labL/100).toFixed(3)} 0.15 ${Math.round(h || 0)})`
    }

    // 计算 LAB 值
    let labValue = 'lab(50 0 0)'
    try {
      const [labL, labA, labB] = color.lab()
      labValue = `lab(${(labL || 0).toFixed(1)} ${(labA || 0).toFixed(1)} ${(labB || 0).toFixed(1)})`
    } catch (e) {
      // LAB 不支持时使用近似值
      const lab = convertRgbToLab(r, g, b)
      labValue = `lab(${lab.l.toFixed(1)} ${lab.a.toFixed(1)} ${lab.b.toFixed(1)})`
    }

    return [
      { name: 'HEX', value: hex },
      { name: 'RGB', value: `rgb(${Math.round(r)}, ${Math.round(g)}, ${Math.round(b)})` },
      { name: 'HSL', value: `hsl(${Math.round(h || 0)}, ${Math.round((s || 0) * 100)}%, ${Math.round((l || 0) * 100)}%)` },
      { name: 'LAB', value: labValue },
      { name: 'CMYK', value: cmykValue },
      { name: 'OKLCH', value: oklchValue }
    ]
  } catch (error) {
    console.error('颜色转换错误:', error)
    // 转换失败时返回错误提示
    return [
      { name: 'HEX', value: '转换失败' },
      { name: 'RGB', value: '转换失败' },
      { name: 'HSL', value: '转换失败' },
      { name: 'LAB', value: '转换失败' },
      { name: 'CMYK', value: '转换失败' },
      { name: 'OKLCH', value: '转换失败' }
    ]
  }
})

// ============================================================================
// 静态数据配置
// ============================================================================

/**
 * 核心功能特性配置
 * 突出展示高优先级功能：OKLCH支持、无障碍检测、CSS生成器、PWA功能
 */
const coreFeatures = [
  {
    id: 'oklch',
    title: 'OKLCH 标准支持',
    description: '领先支持最新 OKLCH 颜色标准，提供感知均匀的颜色空间转换，确保跨设备色彩一致性。',
    icon: 'ColorSwatchIcon',
    color: 'purple',
    specs: ['感知均匀', '跨设备一致', '领先6个月']
  },
  {
    id: 'accessibility',
    title: '智能无障碍检测',
    description: '集成 WCAG 3.0 标准，实时检测对比度和色盲友好性，确保设计符合无障碍规范。',
    icon: 'EyeIcon',
    color: 'green',
    specs: ['WCAG 3.0', '实时检测', '色盲友好']
  },
  {
    id: 'css-generator',
    title: 'CSS 变量生成器',
    description: '一键生成 CSS Custom Properties、Sass 变量、Tailwind 配置，提升开发效率 300%。',
    icon: 'CodeIcon',
    color: 'blue',
    specs: ['多框架支持', '一键生成', '热更新']
  },
  {
    id: 'pwa',
    title: 'PWA 离线功能',
    description: '完整的 PWA 支持，离线可用性 100%，本地 WebAssembly 计算，无需网络依赖。',
    icon: 'CloudIcon',
    color: 'orange',
    specs: ['离线可用', '本地计算', '即时响应']
  }
]

/**
 * 四大用户群体配置
 * 针对 UI/UX设计师、前端开发者、品牌营销团队、教育用户的差异化展示
 */
const userGroups = [
  {
    id: 'designers',
    name: 'UI/UX 设计师',
    title: '为设计师量身定制的专业工具',
    description: '提供设计系统集成、实时协作和无障碍检测，让设计更专业、更高效。',
    icon: 'PaintBrushIcon',
    features: [
      '设计系统集成插件（Figma/Sketch/Adobe XD）',
      '智能无障碍检测（WCAG 3.0 标准）',
      '色彩理论可视化实验室',
      '品牌色彩一致性检测'
    ],
    demos: [
      { title: '主色调', color: '#6366f1', value: 'Primary' },
      { title: '辅助色', color: '#8b5cf6', value: 'Secondary' },
      { title: '成功色', color: '#10b981', value: 'Success' },
      { title: '警告色', color: '#f59e0b', value: 'Warning' }
    ]
  },
  {
    id: 'developers',
    name: '前端开发者',
    title: '开发者友好的代码生成工具',
    description: '自动生成各种框架的主题配置，支持热更新，提升开发效率 300%。',
    icon: 'CodeBracketIcon',
    features: [
      'CSS Custom Properties 自动生成',
      'Tailwind CSS 配置一键导出',
      'Vue/React/Angular 主题包生成',
      'WebAssembly 高性能计算引擎'
    ],
    demos: [
      { title: '--primary', color: '#6366f1', value: 'CSS Variable' },
      { title: 'primary-500', color: '#6366f1', value: 'Tailwind' },
      { title: 'theme.primary', color: '#6366f1', value: 'JS Object' },
      { title: '$primary', color: '#6366f1', value: 'Sass Variable' }
    ]
  },
  {
    id: 'marketing',
    name: '品牌营销团队',
    title: '品牌色彩管理专家',
    description: '确保品牌色彩在各种媒介中的一致性，提供专业的色彩分析和管理工具。',
    icon: 'MegaphoneIcon',
    features: [
      '品牌色彩 DNA 分析器',
      '跨媒介色彩管理系统',
      '印刷级 CMYK 精确转换',
      '色彩情感分析报告'
    ],
    demos: [
      { title: '品牌主色', color: '#1f2937', value: 'Brand Primary' },
      { title: '印刷色', color: '#374151', value: 'Print CMYK' },
      { title: '数字色', color: '#4b5563', value: 'Digital RGB' },
      { title: '辅助色', color: '#6b7280', value: 'Supporting' }
    ]
  },
  {
    id: 'education',
    name: '教育用户',
    title: '交互式色彩学习平台',
    description: '通过可视化演示和实践操作，深入理解色彩理论和应用原理。',
    icon: 'AcademicCapIcon',
    features: [
      '交互式色彩学习路径',
      '色彩理论可视化实验室',
      '实时参数调节演示',
      '色彩空间对比分析'
    ],
    demos: [
      { title: 'RGB 模式', color: '#ff0000', value: 'Red Channel' },
      { title: 'HSL 模式', color: '#00ff00', value: 'Hue 120°' },
      { title: 'LAB 模式', color: '#0000ff', value: 'Blue Lab' },
      { title: 'OKLCH 模式', color: '#ff00ff', value: 'Magenta' }
    ]
  }
]

/**
 * 技术优势配置
 * 展示 Vue 3.x + Vite 7.x + WebAssembly 技术栈的优势
 */
const techAdvantages = [
  {
    id: 'vue3',
    title: 'Vue 3.x',
    version: 'Composition API',
    description: '基于最新 Vue 3 Composition API，提供更好的逻辑复用和状态管理，响应式系统完美适配颜色数据实时更新。',
    icon: 'CpuChipIcon',
    metrics: [
      { value: '40%', label: '性能提升' },
      { value: '<100ms', label: '热更新' },
      { value: '100%', label: '类型安全' }
    ]
  },
  {
    id: 'vite',
    title: 'Vite 7.x',
    version: 'Lightning Fast',
    description: '极速构建工具，原生 ES 模块支持，为颜色工具提供最佳的开发和构建体验。',
    icon: 'BoltIcon',
    metrics: [
      { value: '50%', label: '构建提速' },
      { value: '<50ms', label: '响应时间' },
      { value: '∞', label: '插件生态' }
    ]
  },
  {
    id: 'webassembly',
    title: 'WebAssembly',
    version: 'High Performance',
    description: 'Rust 编译的 WebAssembly 模块，提供原生级性能的颜色计算，支持万级颜色批量处理。',
    icon: 'RocketLaunchIcon',
    metrics: [
      { value: '1000%', label: '计算提速' },
      { value: 'ΔE≤0.5', label: '精度保障' },
      { value: '10K+', label: '批量处理' }
    ]
  },
  {
    id: 'pwa',
    title: 'PWA 技术',
    version: 'Offline First',
    description: '完整的 PWA 支持，Service Worker 缓存策略，确保离线可用性和原生应用体验。',
    icon: 'DevicePhoneMobileIcon',
    metrics: [
      { value: '100%', label: '离线可用' },
      { value: '80%', label: '留存提升' },
      { value: '<1s', label: '启动时间' }
    ]
  }
]

/**
 * 平台统计数据
 * 展示平台的核心指标和成就
 */
const platformStats = [
  {
    id: 'precision',
    number: 'ΔE≤0.5',
    label: '专业级精度',
    description: '色彩转换精度达到专业印刷标准'
  },
  {
    id: 'speed',
    number: '<50ms',
    label: '极速响应',
    description: '实时颜色转换，无感知延迟'
  },
  {
    id: 'formats',
    number: '12+',
    label: '颜色格式',
    description: '支持从基础到专业的全格式转换'
  },
  {
    id: 'standards',
    number: '100%',
    label: '标准兼容',
    description: 'WCAG 3.0、OKLCH 等最新标准支持'
  }
]

/**
 * 定价方案配置
 * 基于产品需求文档中的商业化路径设计
 */
const pricingPlans = [
  {
    id: 'free',
    name: '免费版',
    price: '0',
    period: '永久免费',
    description: '适合个人开发者和学习使用',
    features: [
      '基础颜色格式转换（HEX/RGB/HSL）',
      '实时颜色预览',
      '基础无障碍检测',
      '社区技术支持',
      '每日转换限制 1000 次'
    ],
    buttonText: '立即开始',
    featured: false
  },
  {
    id: 'pro',
    name: '专业版',
    price: '99',
    period: '/月',
    description: '适合专业设计师和开发团队',
    features: [
      '全格式颜色转换（包含 LAB/CMYK/OKLCH）',
      '无限制转换次数',
      '设计工具插件（Figma/Sketch/Adobe XD）',
      'CSS 变量生成器',
      'WebAssembly 高性能计算',
      'PWA 离线功能',
      '优先技术支持'
    ],
    buttonText: '开始免费试用',
    featured: true
  },
  {
    id: 'enterprise',
    name: '企业版',
    price: '299',
    period: '/月',
    description: '适合大型团队和企业级应用',
    features: [
      '专业版全部功能',
      '品牌色彩管理系统',
      '跨媒介色彩一致性检测',
      'API 接口调用',
      '私有部署支持',
      '定制化开发服务',
      '7x24 专属技术支持',
      'SLA 服务保障'
    ],
    buttonText: '联系销售',
    featured: false
  }
]

// ============================================================================
// 颜色转换工具函数
// ============================================================================

/**
 * 十六进制颜色转 RGB
 * @param {string} hex - 十六进制颜色值 (如: "#ff0000")
 * @returns {Object} RGB 对象 {r, g, b}
 */
function hexToRgb(hex) {
  // 移除 # 符号
  const cleanHex = hex.replace('#', '')

  // 处理 3 位和 6 位十六进制
  const fullHex = cleanHex.length === 3
    ? cleanHex.split('').map(char => char + char).join('')
    : cleanHex

  const r = parseInt(fullHex.substring(0, 2), 16)
  const g = parseInt(fullHex.substring(2, 4), 16)
  const b = parseInt(fullHex.substring(4, 6), 16)

  return { r, g, b }
}

/**
 * RGB 转 HSL
 * @param {number} r - 红色分量 (0-255)
 * @param {number} g - 绿色分量 (0-255)
 * @param {number} b - 蓝色分量 (0-255)
 * @returns {Object} HSL 对象 {h, s, l}
 */
function rgbToHsl(r, g, b) {
  r /= 255
  g /= 255
  b /= 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h, s, l = (max + min) / 2

  if (max === min) {
    h = s = 0 // 无色彩
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }

  return {
    h: h * 360,
    s: s * 100,
    l: l * 100
  }
}

/**
 * RGB 转 LAB (简化版，实际项目中使用 WebAssembly 高精度计算)
 * @param {number} r - 红色分量 (0-255)
 * @param {number} g - 绿色分量 (0-255)
 * @param {number} b - 蓝色分量 (0-255)
 * @returns {Object} LAB 对象 {l, a, b}
 */
function convertRgbToLab(r, g, b) {
  // 简化的 RGB 到 LAB 转换（实际实现会更复杂）
  // 这里仅用于演示，实际项目中会使用 WebAssembly 进行高精度计算

  // 归一化 RGB 值
  r = r / 255
  g = g / 255
  b = b / 255

  // 简化的 LAB 计算（不是真实的转换算法）
  const l = (0.299 * r + 0.587 * g + 0.114 * b) * 100
  const a_component = (r - g) * 128
  const b_component = (g - b) * 128

  return {
    l: Math.round(l * 10) / 10,
    a: Math.round(a_component * 10) / 10,
    b: Math.round(b_component * 10) / 10
  }
}

// ============================================================================
// 事件处理函数
// ============================================================================

/**
 * 处理颜色输入 - 智能增强版
 * 当用户在 Hero 区域输入颜色值并失去焦点时触发
 * 使用智能颜色识别引擎的完整功能
 */
function processColorInput() {
  // 重置所有状态
  resetInputState()

  // 如果输入为空，恢复默认值
  if (!demoColor.value.trim()) {
    setDefaultColor()
    return
  }

  try {
    // 使用智能颜色识别引擎 - 先尝试增强功能，失败则使用基础功能
    let parseResult

    try {
      if (typeof ColorParser.parseEnhanced === 'function') {
        parseResult = ColorParser.parseEnhanced(demoColor.value.trim(), {
          enableCache: true,
          enableSuggestions: true,
          enableFuzzyMatch: true,
          strictMode: false
        })
      } else {
        // 降级到基础解析
        parseResult = ColorParser.parse(demoColor.value.trim())
      }
    } catch (enhancedError) {
      // 如果增强功能失败，使用基础解析
      parseResult = ColorParser.parse(demoColor.value.trim())
    }

    if (parseResult.mode === 'error' || parseResult.mode === 'unknown') {
      handleParseError(parseResult)
      return
    }

    // 处理成功解析
    handleParseSuccess(parseResult)

  } catch (error) {
    handleUnexpectedError(error)
  }
}

/**
 * 重置输入状态
 */
function resetInputState() {
  colorError.value = ''
  detectedFormat.value = ''
  isValidColor.value = false
  suggestions.value = []
  correctionInfo.value = null
  confidence.value = 1
  errorType.value = ''
}

/**
 * 设置默认颜色
 */
function setDefaultColor() {
  demoColor.value = '#6366f1'
  detectedFormat.value = 'HEX'
  isValidColor.value = true
  confidence.value = 1
}

/**
 * 处理解析错误
 */
function handleParseError(parseResult) {
  isValidColor.value = false
  errorType.value = parseResult.error || 'UNKNOWN_FORMAT'
  colorError.value = parseResult.message || '无法识别的颜色格式'
  suggestions.value = parseResult.suggestions || []
  confidence.value = parseResult.confidence || 0

  // 发送错误事件
  trackEvent('color_processed', {
    input_value: demoColor.value,
    error: parseResult.error,
    suggestions_count: suggestions.value.length,
    success: false
  })
}

/**
 * 处理解析成功
 */
function handleParseSuccess(parseResult) {
  // 设置检测到的格式
  detectedFormat.value = parseResult.mode.toUpperCase()
  confidence.value = parseResult.confidence || 1

  // 处理自动修正
  if (parseResult.corrected && parseResult.corrected !== demoColor.value.trim()) {
    correctionInfo.value = {
      original: demoColor.value.trim(),
      corrected: parseResult.corrected
    }
    demoColor.value = parseResult.corrected
  } else {
    demoColor.value = parseResult.value
  }

  // 使用检测到的值进行 chroma-js 验证
  try {
    let colorValue = parseResult.value

    // 对于关键字颜色，使用 hex 值
    if (parseResult.hex) {
      colorValue = parseResult.hex
    }

    // 验证颜色是否能被 chroma-js 解析
    const chromaColor = chroma(colorValue)
    isValidColor.value = true

    // 发送成功事件
    trackEvent('color_processed', {
      input_format: parseResult.mode,
      input_value: parseResult.value,
      confidence: confidence.value,
      was_corrected: !!correctionInfo.value,
      success: true
    })

  } catch (chromaError) {
    handleChromaError(chromaError, parseResult)
  }
}

/**
 * 处理 chroma-js 错误
 */
function handleChromaError(chromaError, parseResult) {
  isValidColor.value = false
  errorType.value = 'CHROMA_ERROR'
  colorError.value = `格式 ${parseResult.mode} 被识别，但无法进行颜色转换。请检查颜色值是否正确。`

  // 提供格式特定的建议
  suggestions.value = getFormatSpecificSuggestions(parseResult.mode)
}

/**
 * 获取格式特定的建议
 */
function getFormatSpecificSuggestions(format) {
  const suggestionMap = {
    hex: ['确保 HEX 值为 3、6 或 8 位', '示例：#f00, #ff0000, #ff0000ff'],
    rgb: ['RGB 值应在 0-255 范围内', '示例：rgb(255, 0, 0)'],
    hsl: ['H: 0-360, S: 0-100%, L: 0-100%', '示例：hsl(0, 100%, 50%)'],
    lch: ['L: 0-100%, C: 0-150, H: 0-360', '示例：lch(50% 20 180)'],
    p3: ['值应在 0-1 范围内', '示例：color(display-p3 1 0 0)']
  }
  return suggestionMap[format] || ['请检查颜色值格式']
}

/**
 * 处理意外错误
 */
function handleUnexpectedError(error) {
  isValidColor.value = false
  errorType.value = 'UNEXPECTED_ERROR'
  colorError.value = '颜色处理过程中发生意外错误，请稍后重试。'

  console.error('颜色处理意外错误:', error)

  trackEvent('color_processed', {
    input_value: demoColor.value,
    error: 'UNEXPECTED_ERROR',
    error_message: error.message,
    success: false
  })
}

/**
 * 应用建议
 */
function applySuggestion(suggestion) {
  // 如果建议包含示例颜色，直接应用
  const colorMatch = suggestion.match(/(#[0-9a-f]{3,8}|rgb\([^)]+\)|hsl\([^)]+\)|lch\([^)]+\)|color\([^)]+\))/i)
  if (colorMatch) {
    demoColor.value = colorMatch[1]
    processColorInput()
  }
}

/**
 * 处理输入框点击事件 - 全选文本
 */
function handleInputClick() {
  if (colorInputRef.value) {
    // 延迟执行全选，确保点击事件完成
    nextTick(() => {
      colorInputRef.value.select()
    })
  }
}

/**
 * 处理输入框获得焦点事件 - 全选文本
 */
function handleInputFocus() {
  if (colorInputRef.value) {
    // 延迟执行全选，确保焦点事件完成
    nextTick(() => {
      colorInputRef.value.select()
    })
  }
}

/**
 * 处理粘贴事件 - 即时识别颜色
 */
function handleInputPaste(event) {
  // 阻止默认粘贴行为，避免重复内容
  event.preventDefault()

  isPasting.value = true

  // 获取粘贴的文本（兼容不同浏览器）
  let pastedText = ''
  try {
    pastedText = event.clipboardData?.getData('text') || ''
  } catch (error) {
    console.warn('无法获取粘贴内容:', error)
    isPasting.value = false
    return
  }

  if (pastedText.trim()) {
    // 清除之前的防抖定时器
    if (inputDebounceTimer.value) {
      clearTimeout(inputDebounceTimer.value)
    }

    // 直接设置粘贴的值（不需要 nextTick，因为我们阻止了默认行为）
    demoColor.value = pastedText.trim()

    // 立即处理颜色识别
    processColorInput()

    // 重置粘贴状态
    setTimeout(() => {
      isPasting.value = false
    }, 100)
  } else {
    // 粘贴内容为空，立即重置状态
    isPasting.value = false
  }
}

/**
 * 处理输入变化事件 - 防抖处理
 */
function handleInputChange() {
  // 如果正在粘贴，跳过防抖处理
  if (isPasting.value) {
    return
  }

  // 清除之前的定时器
  if (inputDebounceTimer.value) {
    clearTimeout(inputDebounceTimer.value)
  }

  // 设置防抖延迟（500ms）
  inputDebounceTimer.value = setTimeout(() => {
    if (demoColor.value.trim()) {
      processColorInput()
    }
  }, 500)
}

/**
 * 获取错误标题
 */
function getErrorTitle(errorType) {
  const titles = {
    'INVALID_INPUT': '输入错误',
    'MISSING_HASH': 'HEX 格式错误',
    'INVALID_HEX_LENGTH': 'HEX 长度错误',
    'INVALID_RGB_RANGE': 'RGB 值超出范围',
    'INVALID_HSL_RANGE': 'HSL 值超出范围',
    'MALFORMED_FUNCTION': '函数格式错误',
    'UNKNOWN_KEYWORD': '未知颜色关键字',
    'UNKNOWN_FORMAT': '未知格式',
    'CHROMA_ERROR': '颜色转换错误',
    'UNEXPECTED_ERROR': '系统错误'
  }
  return titles[errorType] || '解析错误'
}

/**
 * 复制颜色值到剪切板
 * @param {string} value - 要复制的颜色值
 * @param {string} formatName - 颜色格式名称
 */
async function copyToClipboard(value, formatName) {
  try {
    // 检查是否为转换失败的情况
    if (value === '转换失败') {
      // 显示错误提示，不进行复制
      copiedFormat.value = ''
      return
    }

    // 使用现代 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(value)
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = value
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      const successful = document.execCommand('copy')
      document.body.removeChild(textArea)

      if (!successful) {
        throw new Error('复制失败')
      }
    }

    // 显示复制成功状态
    copiedFormat.value = formatName

    // 发送复制成功事件
    trackEvent('color_copied', {
      format: formatName,
      value: value,
      success: true
    })

    // 2秒后清除复制状态
    setTimeout(() => {
      copiedFormat.value = ''
    }, 2000)

  } catch (error) {
    console.error('复制到剪切板失败:', error)

    // 发送复制失败事件
    trackEvent('color_copied', {
      format: formatName,
      value: value,
      error: error.message,
      success: false
    })

    // 可以在这里添加用户友好的错误提示
    // 例如显示一个临时的错误消息
    copiedFormat.value = ''
  }
}

/**
 * 开始试用
 * 引导用户进入专业转换器工具
 */
function startTrial() {
  console.log('开始试用 ColorCode.cc - 导航到专业转换器')

  // 发送用户行为分析事件
  trackEvent('cta_click', {
    source: 'hero_section',
    action: 'start_trial'
  })

  // 导航到专业转换器工具
  router.push('/converter')
}

/**
 * 查看演示
 * 导航到Wiki知识库系统
 */
function viewDemo() {
  console.log('查看产品演示 - 导航到Wiki知识库')

  trackEvent('cta_click', {
    source: 'hero_section',
    action: 'view_demo'
  })

  // 导航到Wiki知识库系统
  router.push('/wiki')
}

/**
 * 选择定价方案
 * @param {Object} plan - 选择的定价方案
 */
function selectPlan(plan) {
  console.log(`选择定价方案: ${plan.name}`)

  trackEvent('pricing_click', {
    plan_id: plan.id,
    plan_name: plan.name,
    plan_price: plan.price
  })

  if (plan.id === 'free') {
    // 免费版直接开始使用
    startTrial()
  } else if (plan.id === 'enterprise') {
    // 企业版联系销售
    contactSales()
  } else {
    // 专业版开始试用
    window.open('/signup?plan=' + plan.id, '_blank')
  }
}

/**
 * 联系销售团队
 */
function contactSales() {
  console.log('联系销售团队')

  trackEvent('contact_click', {
    source: 'pricing_section',
    action: 'contact_sales'
  })

  // 实际项目中会打开联系表单或客服系统
  window.open('mailto:<EMAIL>?subject=企业版咨询', '_blank')
}

/**
 * 用户行为分析事件追踪
 * @param {string} eventName - 事件名称
 * @param {Object} properties - 事件属性
 */
function trackEvent(eventName, properties = {}) {
  // 实际项目中会集成 Google Analytics、百度统计等分析工具
  if (typeof gtag !== 'undefined') {
    gtag('event', eventName, properties)
  }

  // 开发环境下的日志输出
  if (import.meta.env.DEV) {
    console.log(`[Analytics] ${eventName}:`, properties)
  }
}

// ============================================================================
// 生命周期钩子
// ============================================================================

/**
 * 组件挂载后的初始化操作
 */
onMounted(() => {
  // 页面加载完成事件追踪
  trackEvent('page_view', {
    page: 'landing_page',
    timestamp: new Date().toISOString()
  })

  // 初始化演示颜色转换
  processColorInput()

  // 添加键盘快捷键支持
  document.addEventListener('keydown', handleKeyboardShortcuts)

  // 添加滚动动画效果（可选）
  initScrollAnimations()
})

/**
 * 键盘快捷键处理
 * @param {KeyboardEvent} event - 键盘事件
 */
function handleKeyboardShortcuts(event) {
  // Ctrl/Cmd + K 快速开始试用
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    startTrial()
  }

  // Escape 键关闭任何打开的模态框（如果有的话）
  if (event.key === 'Escape') {
    // 处理模态框关闭逻辑
  }
}

/**
 * 初始化滚动动画效果
 * 为页面元素添加滚动时的淡入动画
 */
function initScrollAnimations() {
  // 使用 Intersection Observer API 实现滚动动画
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-fade-in')
      }
    })
  }, observerOptions)

  // 观察需要动画的元素
  const animatedElements = document.querySelectorAll(
    '.feature-card, .user-group-content, .tech-card, .stat-card, .pricing-card'
  )

  animatedElements.forEach(el => observer.observe(el))
}
</script>

<style scoped>
/* ============================================================================
   智能颜色识别引擎 - 增强样式
   ============================================================================ */

/* 输入框增强样式 */
.demo-color-input {
  transition: all var(--transition-fast);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--text-base);
  background: #ffffff;
  color: #1f2937;
  width: 100%;
}

.demo-color-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.demo-color-input.error {
  border-color: var(--color-error);
  background: rgba(239, 68, 68, 0.05);
}

.demo-color-input.success {
  border-color: var(--color-success);
  background: rgba(34, 197, 94, 0.05);
}

.demo-color-input.corrected {
  border-color: var(--color-primary);
  background: rgba(59, 130, 246, 0.05);
  animation: pulse-correction 2s ease-in-out;
}

.demo-color-input.low-confidence {
  border-color: var(--color-warning);
  background: rgba(245, 158, 11, 0.05);
}

.demo-color-input.pasting {
  border-color: var(--color-primary);
  background: rgba(59, 130, 246, 0.1);
  animation: pulse-pasting 0.6s ease-in-out;
}

@keyframes pulse-correction {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes pulse-pasting {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.01);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* 错误信息样式 */
.error-message {
  margin-top: var(--spacing-3);
  padding: var(--spacing-4);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-lg);
  color: var(--color-error);
}

.error-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 600;
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-2);
}

.error-icon {
  font-size: var(--text-base);
}

.error-description {
  font-size: var(--text-sm);
  line-height: 1.5;
  color: rgba(239, 68, 68, 0.8);
}

/* 智能建议样式 */
.suggestions {
  margin-top: var(--spacing-3);
  padding-top: var(--spacing-3);
  border-top: 1px solid rgba(239, 68, 68, 0.2);
}

.suggestions-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: 600;
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-2);
  color: var(--color-error);
}

.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestion-item {
  padding: var(--spacing-2) var(--spacing-3);
  margin: var(--spacing-1) 0;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(239, 68, 68, 0.1);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
  line-height: 1.4;
}

.suggestion-item:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  transform: translateX(4px);
}

.suggestion-item:active {
  transform: translateX(2px) scale(0.98);
}

/* 自动修正提示样式 */
.correction-info {
  margin-top: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-lg);
  color: var(--color-primary);
  font-size: var(--text-sm);
  text-align: center;
  animation: slide-in-correction 0.5s ease-out;
}

.correction-icon {
  margin-right: var(--spacing-2);
  font-size: var(--text-base);
}

@keyframes slide-in-correction {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 格式检测结果样式 */
.format-detected {
  margin-top: var(--spacing-3);
  padding: var(--spacing-2) var(--spacing-3);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: var(--radius-md);
  color: var(--color-success);
  font-size: var(--text-sm);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.format-icon {
  font-size: var(--text-base);
}

.confidence {
  font-weight: 500;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-color-input {
    font-size: var(--text-sm);
    padding: var(--spacing-2) var(--spacing-3);
  }

  .error-message,
  .correction-info,
  .format-detected {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--text-xs);
  }

  .suggestion-item {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--text-xs);
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .demo-color-input {
    background: var(--color-background-dark, #1f2937);
    /* color: var(--color-text-dark, #f9fafb); */
    border-color: var(--color-border-dark, #374151);
  }

  .suggestion-item {
    background: rgba(255, 255, 255, 0.1);
    /* color: var(--color-text-dark, #f9fafb); */
  }

  .suggestion-item:hover {
    background: rgba(239, 68, 68, 0.2);
  }
}

/* ============================================================================
   Footer 样式 - 基于设计系统规范
   ============================================================================ */

.footer-section {
  background: var(--color-bg-footer);
  border-top: 1px solid var(--color-border);
  margin-top: var(--spacing-section-padding);
  padding: var(--spacing-section-padding) 0 var(--spacing-8);
  color: var(--color-text-inverse);
}

.footer-content {
  max-width: var(--layout-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-container-padding);
}

.footer-nav {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
}

.footer-nav-group {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: var(--transition-medium);
}

.footer-nav-group:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.footer-nav-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-inverse);
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.footer-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.footer-nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: var(--font-size-sm);
  padding: var(--spacing-2) var(--spacing-3);
  transition: var(--transition-medium);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
}

.footer-nav-link:hover {
  color: var(--color-text-inverse);
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(4px);
}

.footer-nav-link .icon {
  width: 16px;
  height: 16px;
  opacity: 0.8;
  transition: var(--transition-medium);
}

.footer-nav-link:hover .icon {
  opacity: 1;
  transform: scale(1.1);
}

.footer-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  margin: var(--spacing-8) 0;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-8);
  flex-wrap: wrap;
  padding-top: var(--spacing-8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-info {
  flex: 1;
  min-width: 300px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-3);
}

.logo-text {
  font-size: var(--text-xl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-version {
  font-size: var(--text-xs);
  color: var(--color-text-tertiary);
  background: var(--color-background-secondary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-weight: 500;
}

.footer-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.footer-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-3);
}

.copyright {
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--font-size-xs);
  margin: 0;
}

.footer-badges {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.footer-badges .badge {
  font-size: var(--font-size-xs);
  padding: var(--spacing-badge-padding);
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
}

.badge-outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
}

/* 响应式设计 - 基于设计系统断点 */
@media (max-width: 768px) {
  .footer-section {
    padding: var(--spacing-12) 0 var(--spacing-6);
    margin-top: var(--spacing-12);
  }

  .footer-nav {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
  }

  .footer-nav-group {
    padding: var(--spacing-4);
  }

  .footer-bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-4);
  }

  .footer-meta {
    align-items: flex-start;
    width: 100%;
  }

  .footer-badges {
    justify-content: flex-start;
  }

  .footer-info {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .footer-nav {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .footer-nav-group {
    padding: var(--spacing-3);
    margin-bottom: var(--spacing-3);
  }

  .footer-nav-group:last-child {
    margin-bottom: 0;
  }

  .footer-nav-title {
    font-size: var(--font-size-base);
  }

  .footer-nav-link {
    padding: var(--spacing-2);
    font-size: var(--font-size-xs);
  }
}

/* Footer 已使用设计系统的深色背景，提供一致的视觉体验 */
</style>
