/**
 * ColorCode.cc Icons Library
 * 基于 Heroicons 设计的图标组件库
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { defineComponent, h } from 'vue'

// ============================================================================
// 图标组件定义
// ============================================================================

/**
 * 颜色样本图标 - 用于 OKLCH 功能
 */
export const ColorSwatchIcon = defineComponent({
  name: 'ColorSwatchIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zM3 15a1 1 0 011-1h1a1 1 0 011 1v1a1 1 0 01-1 1H4a1 1 0 01-1-1v-1zm6-11a1 1 0 011-1h4a1 1 0 011 1v7a1 1 0 01-1 1h-4a1 1 0 01-1-1V4zm6 7a1 1 0 011-1h1a1 1 0 011 1v4a1 1 0 01-1 1h-1a1 1 0 01-1-1v-4z',
      clipRule: 'evenodd'
    })
  ])
})

/**
 * 搜索图标 - 用于搜索功能
 */
export const SearchIcon = defineComponent({
  name: 'SearchIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z',
      clipRule: 'evenodd'
    })
  ])
})

/**
 * 眼睛图标 - 用于无障碍检测功能
 */
export const EyeIcon = defineComponent({
  name: 'EyeIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      d: 'M10 12a2 2 0 100-4 2 2 0 000 4z'
    }),
    h('path', {
      fillRule: 'evenodd',
      d: 'M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z',
      clipRule: 'evenodd'
    })
  ])
})

/**
 * 代码图标 - 用于 CSS 生成器功能
 */
export const CodeIcon = defineComponent({
  name: 'CodeIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z',
      clipRule: 'evenodd'
    })
  ])
})

/**
 * 云图标 - 用于 PWA 离线功能
 */
export const CloudIcon = defineComponent({
  name: 'CloudIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      d: 'M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z'
    })
  ])
})

/**
 * 画笔图标 - 用于 UI/UX 设计师
 */
export const PaintBrushIcon = defineComponent({
  name: 'PaintBrushIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M18 3a1 1 0 00-1.447-.894L8.763 6.138a3 3 0 100 2.724l7.79 4.032A1 1 0 0018 12V3zM5 9a1 1 0 100-2 1 1 0 000 2z',
      clipRule: 'evenodd'
    }),
    h('path', {
      d: 'M4 12.5a.5.5 0 01.5-.5h3a.5.5 0 01.5.5v2a1.5 1.5 0 01-1.5 1.5h-1A1.5 1.5 0 014 14.5v-2z'
    })
  ])
})

/**
 * 代码括号图标 - 用于前端开发者
 */
export const CodeBracketIcon = defineComponent({
  name: 'CodeBracketIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M6.28 5.22a.75.75 0 010 1.06L2.56 10l3.72 3.72a.75.75 0 01-1.06 1.06L.97 10.53a.75.75 0 010-1.06l4.25-4.25a.75.75 0 011.06 0zm7.44 0a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L17.44 10l-3.72-3.72a.75.75 0 010-1.06zM11.377 2.011a.75.75 0 01.612.867l-2.5 14a.75.75 0 01-1.478-.256l2.5-14a.75.75 0 01.866-.611z',
      clipRule: 'evenodd'
    })
  ])
})

/**
 * 扩音器图标 - 用于品牌营销团队
 */
export const MegaphoneIcon = defineComponent({
  name: 'MegaphoneIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M18 3a1 1 0 00-1.447-.894L8.763 6.138a3 3 0 100 2.724l7.79 4.032A1 1 0 0018 12V3zM5 9a1 1 0 100-2 1 1 0 000 2z',
      clipRule: 'evenodd'
    }),
    h('path', {
      d: 'M4 12.5a.5.5 0 01.5-.5h3a.5.5 0 01.5.5v2a1.5 1.5 0 01-1.5 1.5h-1A1.5 1.5 0 014 14.5v-2z'
    })
  ])
})

/**
 * 学术帽图标 - 用于教育用户
 */
export const AcademicCapIcon = defineComponent({
  name: 'AcademicCapIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      d: 'M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z'
    }),
    h('path', {
      d: 'M3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z'
    })
  ])
})

/**
 * CPU 芯片图标 - 用于 Vue 3.x 技术
 */
export const CpuChipIcon = defineComponent({
  name: 'CpuChipIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      d: 'M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z'
    })
  ])
})

/**
 * 闪电图标 - 用于 Vite 技术
 */
export const BoltIcon = defineComponent({
  name: 'BoltIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z',
      clipRule: 'evenodd'
    })
  ])
})

/**
 * 火箭发射图标 - 用于 WebAssembly 技术
 */
export const RocketLaunchIcon = defineComponent({
  name: 'RocketLaunchIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M5.22 14.78a.75.75 0 001.06 0l7.22-7.22v5.69a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75h-7.5a.75.75 0 000 1.5h5.69l-7.22 7.22a.75.75 0 000 1.06z',
      clipRule: 'evenodd'
    })
  ])
})

/**
 * 手机设备图标 - 用于移动设备展示
 */
export const DevicePhoneMobileIcon = defineComponent({
  name: 'DevicePhoneMobileIcon',
  render: () => h('svg', {
    viewBox: '0 0 20 20',
    fill: 'currentColor'
  }, [
    h('path', {
      fillRule: 'evenodd',
      d: 'M8 1a2.5 2.5 0 00-2.5 2.5v13A2.5 2.5 0 008 19h4a2.5 2.5 0 002.5-2.5v-13A2.5 2.5 0 0012 1H8zm4 14a1 1 0 11-2 0 1 1 0 012 0z',
      clipRule: 'evenodd'
    })
  ])
})

// 导出所有图标组件
export default {
  ColorSwatchIcon,
  EyeIcon,
  CodeIcon,
  CloudIcon,
  PaintBrushIcon,
  CodeBracketIcon,
  MegaphoneIcon,
  AcademicCapIcon,
  CpuChipIcon,
  BoltIcon,
  RocketLaunchIcon,
  DevicePhoneMobileIcon
}
