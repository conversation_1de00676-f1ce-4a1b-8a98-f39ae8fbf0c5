<!--
  WikiSkeleton.vue - Wiki 加载骨架屏组件
  在内容加载时显示占位符
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="wiki-skeleton">
    <!-- 概览部分骨架 -->
    <div class="skeleton-section">
      <div class="skeleton-overview">
        <div class="skeleton-demo">
          <div class="skeleton-color-preview"></div>
          <div class="skeleton-code"></div>
        </div>
        <div class="skeleton-info">
          <div class="skeleton-title"></div>
          <div class="skeleton-description"></div>
          <div class="skeleton-specs">
            <div class="skeleton-spec"></div>
            <div class="skeleton-spec"></div>
            <div class="skeleton-spec"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 内容部分骨架 -->
    <div class="skeleton-section">
      <div class="skeleton-content-title"></div>
      <div class="skeleton-paragraph"></div>
      <div class="skeleton-paragraph short"></div>
      <div class="skeleton-paragraph"></div>
    </div>
    
    <!-- 示例部分骨架 -->
    <div class="skeleton-section">
      <div class="skeleton-subtitle"></div>
      <div class="skeleton-examples">
        <div class="skeleton-example"></div>
        <div class="skeleton-example"></div>
        <div class="skeleton-example"></div>
      </div>
    </div>
    
    <!-- 转换工具骨架 -->
    <div class="skeleton-section">
      <div class="skeleton-subtitle"></div>
      <div class="skeleton-converter">
        <div class="skeleton-input"></div>
        <div class="skeleton-arrow"></div>
        <div class="skeleton-output"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 无需 props 或逻辑，纯展示组件
</script>

<style scoped>
.wiki-skeleton {
  padding: 2rem 0;
  animation: pulse 2s ease-in-out infinite;
}

.skeleton-section {
  margin-bottom: 3rem;
}

.skeleton-overview {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.skeleton-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.skeleton-color-preview {
  width: 120px;
  height: 120px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 12px;
}

.skeleton-code {
  width: 100px;
  height: 24px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 6px;
}

.skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skeleton-title {
  width: 200px;
  height: 32px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
}

.skeleton-description {
  width: 100%;
  height: 20px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 6px;
}

.skeleton-specs {
  display: flex;
  gap: 1rem;
}

.skeleton-spec {
  width: 80px;
  height: 16px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 4px;
}

.skeleton-content-title {
  width: 150px;
  height: 28px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.skeleton-subtitle {
  width: 120px;
  height: 24px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 6px;
  margin-bottom: 1rem;
}

.skeleton-paragraph {
  width: 100%;
  height: 16px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 4px;
  margin-bottom: 0.75rem;
}

.skeleton-paragraph.short {
  width: 70%;
}

.skeleton-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.skeleton-example {
  height: 100px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
}

.skeleton-converter {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: var(--skeleton-bg, #f9fafb);
  border-radius: 12px;
}

.skeleton-input,
.skeleton-output {
  flex: 1;
  height: 48px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 8px;
}

.skeleton-arrow {
  width: 24px;
  height: 24px;
  background: var(--skeleton-color, #e5e7eb);
  border-radius: 50%;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 渐变动画效果 */
.skeleton-color-preview,
.skeleton-code,
.skeleton-title,
.skeleton-description,
.skeleton-spec,
.skeleton-content-title,
.skeleton-subtitle,
.skeleton-paragraph,
.skeleton-example,
.skeleton-input,
.skeleton-output,
.skeleton-arrow {
  background: linear-gradient(
    90deg,
    var(--skeleton-color, #e5e7eb) 25%,
    var(--skeleton-highlight, #f3f4f6) 50%,
    var(--skeleton-color, #e5e7eb) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wiki-skeleton {
    padding: 1rem 0;
  }
  
  .skeleton-overview {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .skeleton-converter {
    flex-direction: column;
    padding: 1.5rem;
  }
  
  .skeleton-examples {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .wiki-skeleton {
    --skeleton-color: #374151;
    --skeleton-highlight: #4b5563;
    --skeleton-bg: #1f2937;
  }
}

/* CSS 变量定义 */
:root {
  --skeleton-color: #e5e7eb;
  --skeleton-highlight: #f3f4f6;
  --skeleton-bg: #f9fafb;
}

[data-theme="dark"] {
  --skeleton-color: #374151;
  --skeleton-highlight: #4b5563;
  --skeleton-bg: #1f2937;
}
</style>
