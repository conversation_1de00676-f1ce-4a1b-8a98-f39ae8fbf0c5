<!--
  WikiIndex.vue - Wiki 首页组件
  显示颜色格式概览和导航入口
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="wiki-index">
    <!-- 页面头部 -->
    <header class="wiki-index-header">
      <div class="header-content">
        <h1 class="page-title">ColorWiki 颜色格式知识库</h1>
        <p class="page-description">
          专业的颜色格式技术文档和应用指南，涵盖从基础到高级的所有颜色空间
        </p>
        
        <!-- 搜索框 -->
        <div class="search-section">
          <div class="search-input-wrapper">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索颜色格式、属性或用例..."
              class="search-input"
              @input="handleSearch"
            />
            <div class="search-icon">🔍</div>
          </div>
          
          <!-- 快速过滤 -->
          <div class="quick-filters">
            <button
              v-for="filter in quickFilters"
              :key="filter.id"
              :class="['filter-btn', { active: activeFilter === filter.id }]"
              @click="setActiveFilter(filter.id)"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 格式分类展示 -->
    <main class="wiki-index-main">
      <div class="formats-container">
        <!-- 基础格式 -->
        <section class="format-category">
          <h2 class="category-title">
            <span class="category-icon">🎨</span>
            基础颜色格式
          </h2>
          <p class="category-description">
            最常用的颜色表示方法，适合日常设计和开发工作
          </p>
          <div class="format-grid">
            <router-link
              v-for="format in basicFormats"
              :key="format.id"
              :to="`/wiki/${format.id}`"
              class="format-card basic"
            >
              <div class="card-header">
                <div class="format-icon">{{ format.icon }}</div>
                <div class="format-info">
                  <h3 class="format-name">{{ format.name }}</h3>
                  <span class="format-fullname">{{ format.fullName }}</span>
                </div>
                <div class="difficulty-badge beginner">入门</div>
              </div>
              <p class="format-description">{{ format.description }}</p>
              <div class="format-example">
                <span class="example-label">示例:</span>
                <code class="example-code">{{ format.example }}</code>
              </div>
            </router-link>
          </div>
        </section>

        <!-- 高级格式 -->
        <section class="format-category">
          <h2 class="category-title">
            <span class="category-icon">🔬</span>
            高级颜色格式
          </h2>
          <p class="category-description">
            专业级颜色空间，提供更精确的颜色控制和更广的色域
          </p>
          <div class="format-grid">
            <router-link
              v-for="format in advancedFormats"
              :key="format.id"
              :to="`/wiki/${format.id}`"
              class="format-card advanced"
            >
              <div class="card-header">
                <div class="format-icon">{{ format.icon }}</div>
                <div class="format-info">
                  <h3 class="format-name">{{ format.name }}</h3>
                  <span class="format-fullname">{{ format.fullName }}</span>
                </div>
                <div class="difficulty-badge advanced">高级</div>
              </div>
              <p class="format-description">{{ format.description }}</p>
              <div class="format-example">
                <span class="example-label">示例:</span>
                <code class="example-code">{{ format.example }}</code>
              </div>
            </router-link>
          </div>
        </section>

        <!-- 特殊格式 -->
        <section class="format-category">
          <h2 class="category-title">
            <span class="category-icon">⚡</span>
            特殊颜色格式
          </h2>
          <p class="category-description">
            特定用途的颜色格式，包括打印、关键字和设备相关格式
          </p>
          <div class="format-grid">
            <router-link
              v-for="format in specialFormats"
              :key="format.id"
              :to="`/wiki/${format.id}`"
              class="format-card special"
            >
              <div class="card-header">
                <div class="format-icon">{{ format.icon }}</div>
                <div class="format-info">
                  <h3 class="format-name">{{ format.name }}</h3>
                  <span class="format-fullname">{{ format.fullName }}</span>
                </div>
                <div class="difficulty-badge intermediate">中级</div>
              </div>
              <p class="format-description">{{ format.description }}</p>
              <div class="format-example">
                <span class="example-label">示例:</span>
                <code class="example-code">{{ format.example }}</code>
              </div>
            </router-link>
          </div>
        </section>
      </div>

      <!-- 侧边栏 -->
      <aside class="wiki-sidebar">
        <!-- 快速导航 -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">快速导航</h3>
          <nav class="quick-nav">
            <a href="#basic" class="nav-link">基础格式</a>
            <a href="#advanced" class="nav-link">高级格式</a>
            <a href="#special" class="nav-link">特殊格式</a>
          </nav>
        </div>

        <!-- 热门格式 -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">热门格式</h3>
          <div class="popular-formats">
            <router-link
              v-for="format in popularFormats"
              :key="format.id"
              :to="`/wiki/${format.id}`"
              class="popular-item"
            >
              <span class="format-icon">{{ format.icon }}</span>
              <span class="format-name">{{ format.name }}</span>
              <span class="view-count">{{ format.views }}+ 浏览</span>
            </router-link>
          </div>
        </div>

        <!-- 最近更新 -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">最近更新</h3>
          <div class="recent-updates">
            <div
              v-for="update in recentUpdates"
              :key="update.id"
              class="update-item"
            >
              <router-link :to="`/wiki/${update.format}`" class="update-link">
                <span class="update-format">{{ update.formatName }}</span>
                <span class="update-desc">{{ update.description }}</span>
                <span class="update-date">{{ update.date }}</span>
              </router-link>
            </div>
          </div>
        </div>
      </aside>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const searchQuery = ref('')
const activeFilter = ref('all')

// 快速过滤选项
const quickFilters = [
  { id: 'all', label: '全部' },
  { id: 'beginner', label: '入门' },
  { id: 'intermediate', label: '中级' },
  { id: 'advanced', label: '高级' },
  { id: 'popular', label: '热门' }
]

// 基础格式
const basicFormats = [
  {
    id: 'hex',
    name: 'HEX',
    fullName: 'Hexadecimal',
    icon: '#',
    description: '最常用的网页颜色格式，简洁易用',
    example: '#FF5733',
    views: 15420
  },
  {
    id: 'rgb',
    name: 'RGB',
    fullName: 'Red Green Blue',
    icon: 'R',
    description: '基于红绿蓝三原色的加色模型',
    example: 'rgb(255, 87, 51)',
    views: 12350
  },
  {
    id: 'hsl',
    name: 'HSL',
    fullName: 'Hue Saturation Lightness',
    icon: 'H',
    description: '基于色相、饱和度、亮度的直观模型',
    example: 'hsl(9, 100%, 60%)',
    views: 9870
  }
]

// 高级格式
const advancedFormats = [
  {
    id: 'oklch',
    name: 'OKLCH',
    fullName: 'OK Lightness Chroma Hue',
    icon: 'O',
    description: '最新的感知均匀颜色空间，精确度最高',
    example: 'oklch(0.7 0.15 180)',
    views: 3420
  },
  {
    id: 'lch',
    name: 'LCH',
    fullName: 'Lightness Chroma Hue',
    icon: 'L',
    description: '感知均匀的柱坐标颜色空间',
    example: 'lch(70% 50 180)',
    views: 2890
  },
  {
    id: 'xyz',
    name: 'XYZ',
    fullName: 'CIE XYZ',
    icon: 'X',
    description: 'CIE 标准颜色空间，色彩科学基础',
    example: 'color(xyz 0.4 0.5 0.6)',
    views: 1560
  }
]

// 特殊格式
const specialFormats = [
  {
    id: 'cmyk',
    name: 'CMYK',
    fullName: 'Cyan Magenta Yellow Key',
    icon: 'C',
    description: '印刷行业标准的减色模型',
    example: 'cmyk(0%, 83%, 80%, 0%)',
    views: 4320
  },
  {
    id: 'keywords',
    name: 'Keywords',
    fullName: 'Color Keywords',
    icon: 'K',
    description: 'CSS 预定义的颜色关键字',
    example: 'red, blue, green',
    views: 6780
  }
]

// 计算属性
const popularFormats = computed(() => {
  return [...basicFormats, ...advancedFormats, ...specialFormats]
    .sort((a, b) => b.views - a.views)
    .slice(0, 5)
})

const recentUpdates = ref([
  {
    id: 1,
    format: 'oklch',
    formatName: 'OKLCH',
    description: '新增浏览器支持信息',
    date: '2天前'
  },
  {
    id: 2,
    format: 'hsl',
    formatName: 'HSL',
    description: '更新配色方案示例',
    date: '5天前'
  },
  {
    id: 3,
    format: 'rgb',
    formatName: 'RGB',
    description: '添加无障碍设计指南',
    date: '1周前'
  }
])

// 方法
const handleSearch = () => {
  // 搜索逻辑
  console.log('搜索:', searchQuery.value)
}

const setActiveFilter = (filterId) => {
  activeFilter.value = filterId
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
.wiki-index {
  min-height: 100vh;
  background: var(--color-gray-50, #f9fafb);
}

.wiki-index-header {
  background: white;
  border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
  padding: 3rem 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.page-description {
  font-size: 1.125rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 2rem 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.search-section {
  max-width: 500px;
  margin: 0 auto;
}

.search-input-wrapper {
  position: relative;
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid var(--color-gray-300, #d1d5db);
  border-radius: 12px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary, #3b82f6);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400, #9ca3af);
}

.quick-filters {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 20px;
  background: white;
  color: var(--color-gray-600, #6b7280);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--color-primary, #3b82f6);
  color: white;
  border-color: var(--color-primary, #3b82f6);
}

.wiki-index-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  align-items: start;
}

.format-category {
  margin-bottom: 3rem;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.category-icon {
  font-size: 1.25rem;
}

.category-description {
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 1.5rem 0;
}

.format-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.format-card {
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
  padding: 1.5rem;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.format-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary, #3b82f6);
}

.format-card.basic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #10b981, #3b82f6);
}

.format-card.advanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #8b5cf6, #ec4899);
}

.format-card.special::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #f59e0b, #ef4444);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.format-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: var(--color-gray-100, #f3f4f6);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: var(--color-primary, #3b82f6);
}

.format-info {
  flex: 1;
}

.format-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.25rem 0;
}

.format-fullname {
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
}

.difficulty-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.difficulty-badge.beginner {
  background: #dcfce7;
  color: #166534;
}

.difficulty-badge.intermediate {
  background: #fef3c7;
  color: #92400e;
}

.difficulty-badge.advanced {
  background: #fce7f3;
  color: #be185d;
}

.format-description {
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 1rem 0;
  line-height: 1.5;
}

.format-example {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.example-label {
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
}

.example-code {
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  color: var(--color-gray-800, #1f2937);
}

.wiki-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sidebar-section {
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
  padding: 1.5rem;
}

.sidebar-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.quick-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-link {
  color: var(--color-gray-600, #6b7280);
  text-decoration: none;
  padding: 0.5rem 0;
  border-bottom: 1px solid transparent;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: var(--color-primary, #3b82f6);
}

.popular-formats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.popular-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
  transition: background-color 0.2s ease;
}

.popular-item:hover {
  background: var(--color-gray-50, #f9fafb);
}

.popular-item .format-icon {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 0.75rem;
}

.popular-item .format-name {
  flex: 1;
  font-weight: 500;
  color: var(--color-gray-900, #111827);
}

.view-count {
  font-size: 0.75rem;
  color: var(--color-gray-500, #6b7280);
}

.recent-updates {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.update-item {
  padding: 0.75rem;
  border-radius: 8px;
  background: var(--color-gray-50, #f9fafb);
}

.update-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.update-format {
  font-weight: 500;
  color: var(--color-primary, #3b82f6);
  display: block;
  margin-bottom: 0.25rem;
}

.update-desc {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  display: block;
  margin-bottom: 0.25rem;
}

.update-date {
  font-size: 0.75rem;
  color: var(--color-gray-500, #6b7280);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .wiki-index-main {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .wiki-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .wiki-index-main {
    padding: 1rem;
  }
  
  .format-grid {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .header-content {
    padding: 0 1rem;
  }
}
</style>
