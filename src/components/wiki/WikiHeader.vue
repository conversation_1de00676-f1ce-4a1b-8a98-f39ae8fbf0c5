<!--
  WikiHeader.vue - Wiki 页面头部组件
  显示格式标题、面包屑导航和格式元信息
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <header class="wiki-header">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb">
      <router-link to="/" class="breadcrumb-item">首页</router-link>
      <span class="breadcrumb-separator">/</span>
      <router-link to="/wiki" class="breadcrumb-item">Wiki</router-link>
      <span class="breadcrumb-separator">/</span>
      <span class="breadcrumb-current">{{ formatInfo.name }}</span>
    </nav>
    
    <!-- 格式标题和信息 -->
    <div class="format-header">
      <div class="format-title-section">
        <div class="format-icon" :class="`difficulty-${formatInfo.difficulty}`">
          {{ formatInfo.icon }}
        </div>
        <div class="format-info">
          <h1 class="format-title">{{ formatInfo.name }} 颜色格式</h1>
          <p class="format-description">{{ formatInfo.description }}</p>
        </div>
      </div>
      
      <!-- 格式元信息 -->
      <div class="format-meta">
        <div class="meta-item">
          <span class="meta-label">难度等级:</span>
          <span class="meta-value difficulty-badge" :class="`difficulty-${formatInfo.difficulty}`">
            {{ getDifficultyText(formatInfo.difficulty) }}
          </span>
        </div>
        <div class="meta-item">
          <span class="meta-label">应用场景:</span>
          <span class="meta-value">{{ formatInfo.usage }}</span>
        </div>
        <div class="meta-item">
          <span class="meta-label">浏览器支持:</span>
          <span class="meta-value support-badge" :class="formatInfo.supportLevel">
            {{ getSupportText(formatInfo.supportLevel) }}
          </span>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  format: {
    type: String,
    required: true
  }
})

// 格式信息配置
const formatConfigs = {
  hex: {
    name: 'HEX',
    icon: '#',
    difficulty: 'beginner',
    description: '十六进制颜色代码，Web 开发中最常用的颜色表示方法',
    usage: 'Web 开发、UI 设计、CSS 样式',
    supportLevel: 'excellent'
  },
  rgb: {
    name: 'RGB',
    icon: 'R',
    difficulty: 'beginner',
    description: '红绿蓝三原色模型，基于光的加色混合原理',
    usage: '显示器、数字图像、Web 开发',
    supportLevel: 'excellent'
  },
  hsl: {
    name: 'HSL',
    icon: 'H',
    difficulty: 'intermediate',
    description: '色相、饱和度、亮度模型，更符合人类对颜色的感知',
    usage: 'UI 设计、颜色调整、主题系统',
    supportLevel: 'excellent'
  },
  hsv: {
    name: 'HSV',
    icon: 'V',
    difficulty: 'intermediate',
    description: '色相、饱和度、明度模型，常用于颜色选择器',
    usage: '图像处理、颜色选择器、设计工具',
    supportLevel: 'good'
  },
  cmyk: {
    name: 'CMYK',
    icon: 'C',
    difficulty: 'advanced',
    description: '青品黄黑四色印刷模型，用于印刷行业',
    usage: '印刷设计、出版物、包装设计',
    supportLevel: 'limited'
  },
  oklch: {
    name: 'OKLCH',
    icon: 'O',
    difficulty: 'expert',
    description: '现代感知均匀色彩空间，提供更准确的颜色表示',
    usage: '现代 Web 开发、高精度设计、无障碍设计',
    supportLevel: 'emerging'
  },
  lch: {
    name: 'LCH',
    icon: 'L',
    difficulty: 'expert',
    description: 'Lab 色彩空间的柱坐标表示，感知均匀',
    usage: '专业设计、色彩科学、印刷校色',
    supportLevel: 'emerging'
  },
  xyz: {
    name: 'XYZ',
    icon: 'X',
    difficulty: 'expert',
    description: 'CIE XYZ 色彩空间，国际照明委员会标准',
    usage: '色彩科学、设备校准、标准化',
    supportLevel: 'limited'
  },
  p3: {
    name: 'Display P3',
    icon: 'P',
    difficulty: 'expert',
    description: '广色域显示标准，支持更丰富的颜色',
    usage: '高端显示器、专业摄影、视频制作',
    supportLevel: 'emerging'
  },
  rec2020: {
    name: 'Rec2020',
    icon: '2',
    difficulty: 'expert',
    description: '超高清电视色彩标准，极广色域',
    usage: '4K/8K 视频、专业影视、未来显示技术',
    supportLevel: 'limited'
  },
  keywords: {
    name: 'Keywords',
    icon: 'K',
    difficulty: 'beginner',
    description: 'CSS 颜色关键字，预定义的颜色名称',
    usage: 'Web 开发、快速原型、教学演示',
    supportLevel: 'excellent'
  }
}

// 计算属性
const formatInfo = computed(() => {
  return formatConfigs[props.format] || {
    name: props.format.toUpperCase(),
    icon: '?',
    difficulty: 'beginner',
    description: '未知颜色格式',
    usage: '未知',
    supportLevel: 'unknown'
  }
})

// 方法
const getDifficultyText = (difficulty) => {
  const texts = {
    beginner: '入门',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家'
  }
  return texts[difficulty] || '未知'
}

const getSupportText = (supportLevel) => {
  const texts = {
    excellent: '优秀',
    good: '良好',
    limited: '有限',
    emerging: '新兴',
    unknown: '未知'
  }
  return texts[supportLevel] || '未知'
}
</script>

<style scoped>
.wiki-header {
  background: white;
  border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
  padding: 2rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.breadcrumb-item {
  color: var(--color-gray-600, #6b7280);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: var(--color-primary, #3b82f6);
}

.breadcrumb-separator {
  color: var(--color-gray-400, #9ca3af);
}

.breadcrumb-current {
  color: var(--color-gray-900, #111827);
  font-weight: 500;
}

.format-header {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.format-title-section {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.format-icon {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.format-icon.difficulty-beginner {
  background: var(--color-green-500, #10b981);
}

.format-icon.difficulty-intermediate {
  background: var(--color-yellow-500, #f59e0b);
}

.format-icon.difficulty-advanced {
  background: var(--color-orange-500, #f97316);
}

.format-icon.difficulty-expert {
  background: var(--color-red-500, #ef4444);
}

.format-info {
  flex: 1;
}

.format-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.format-description {
  font-size: 1.125rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
  line-height: 1.5;
}

.format-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 12px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.meta-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700, #374151);
}

.meta-value {
  font-size: 0.875rem;
  color: var(--color-gray-900, #111827);
}

.difficulty-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.difficulty-badge.difficulty-beginner {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-700, #15803d);
}

.difficulty-badge.difficulty-intermediate {
  background: var(--color-yellow-100, #fef3c7);
  color: var(--color-yellow-700, #a16207);
}

.difficulty-badge.difficulty-advanced {
  background: var(--color-orange-100, #ffedd5);
  color: var(--color-orange-700, #c2410c);
}

.difficulty-badge.difficulty-expert {
  background: var(--color-red-100, #fee2e2);
  color: var(--color-red-700, #b91c1c);
}

.support-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.support-badge.excellent {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-700, #15803d);
}

.support-badge.good {
  background: var(--color-blue-100, #dbeafe);
  color: var(--color-blue-700, #1d4ed8);
}

.support-badge.limited {
  background: var(--color-yellow-100, #fef3c7);
  color: var(--color-yellow-700, #a16207);
}

.support-badge.emerging {
  background: var(--color-purple-100, #f3e8ff);
  color: var(--color-purple-700, #7c3aed);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wiki-header {
    padding: 1rem;
  }
  
  .format-title-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .format-title {
    font-size: 1.5rem;
  }
  
  .format-meta {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .wiki-header {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .breadcrumb-current,
  .format-title {
    color: white;
  }
  
  .format-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .format-meta {
    background: var(--color-gray-700, #374151);
  }
  
  .meta-label {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .meta-value {
    color: white;
  }
}
</style>
