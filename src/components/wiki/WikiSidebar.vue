<!--
  WikiSidebar.vue - Wiki 侧边栏导航组件
  提供格式列表、搜索和最近访问功能
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="wiki-sidebar" role="navigation" aria-label="颜色格式导航">
    <!-- 搜索框 -->
    <div class="search-section">
      <label for="format-search" class="sr-only">搜索颜色格式</label>
      <input
        id="format-search"
        v-model="searchQuery"
        type="text"
        placeholder="搜索颜色格式..."
        class="search-input"
        aria-describedby="search-help"
      />
      <div id="search-help" class="sr-only">输入格式名称进行搜索</div>
    </div>
    
    <!-- 格式列表 -->
    <nav class="format-nav">
      <div class="nav-section">
        <h3 class="section-title">颜色格式</h3>
        <ul class="format-list">
          <li 
            v-for="format in filteredFormats" 
            :key="format.id"
            class="format-item"
            :class="{ active: format.id === current }"
          >
            <button
              @click="$emit('format-change', format.id)"
              @keydown.enter="$emit('format-change', format.id)"
              class="format-button"
              :aria-label="`切换到 ${format.name} 格式`"
              :aria-current="format.id === current ? 'page' : false"
              :data-format="format.id"
            >
              <span class="format-icon">{{ format.icon }}</span>
              <span class="format-name">{{ format.name }}</span>
              <span class="difficulty-badge" :class="`difficulty-${format.difficulty}`">
                {{ getDifficultyText(format.difficulty) }}
              </span>
            </button>
          </li>
        </ul>
      </div>
      
      <!-- 最近访问 -->
      <div v-if="recentFormats.length > 0" class="nav-section">
        <h3 class="section-title">最近访问</h3>
        <ul class="format-list">
          <li 
            v-for="formatId in recentFormats" 
            :key="`recent-${formatId}`"
            class="format-item"
          >
            <button
              @click="$emit('format-change', formatId)"
              class="format-button recent"
            >
              <span class="format-name">{{ getFormatName(formatId) }}</span>
            </button>
          </li>
        </ul>
      </div>
    </nav>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useColorStore } from '@/stores/colorStore'

const props = defineProps({
  formats: {
    type: Array,
    required: true
  },
  current: {
    type: String,
    default: ''
  }
})

defineEmits(['format-change'])

const colorStore = useColorStore()
const searchQuery = ref('')

// 计算属性
const filteredFormats = computed(() => {
  if (!searchQuery.value) return props.formats
  
  const query = searchQuery.value.toLowerCase()
  return props.formats.filter(format => 
    format.name.toLowerCase().includes(query) ||
    format.id.toLowerCase().includes(query)
  )
})

const recentFormats = computed(() => {
  return colorStore.recentlyViewed.slice(0, 5)
})

// 方法
const getDifficultyText = (difficulty) => {
  const texts = {
    beginner: '入门',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家'
  }
  return texts[difficulty] || '未知'
}

const getFormatName = (formatId) => {
  const format = props.formats.find(f => f.id === formatId)
  return format ? format.name : formatId.toUpperCase()
}
</script>

<style scoped>
.wiki-sidebar {
  padding: 1.5rem;
  height: 100%;
  overflow-y: auto;
}

.search-section {
  margin-bottom: 2rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.format-nav {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.nav-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-gray-700, #374151);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.format-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.format-item {
  margin: 0;
}

.format-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.format-button:hover {
  background: var(--color-gray-100, #f3f4f6);
}

.format-item.active .format-button {
  background: var(--color-primary-50, #eff6ff);
  color: var(--color-primary-700, #1d4ed8);
}

.format-icon {
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-gray-200, #e5e7eb);
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.75rem;
}

.format-item.active .format-icon {
  background: var(--color-primary-200, #bfdbfe);
  color: var(--color-primary-700, #1d4ed8);
}

.format-name {
  flex: 1;
  font-weight: 500;
  font-size: 0.875rem;
}

.difficulty-badge {
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-weight: 500;
}

.difficulty-beginner {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-700, #15803d);
}

.difficulty-intermediate {
  background: var(--color-yellow-100, #fef3c7);
  color: var(--color-yellow-700, #a16207);
}

.difficulty-advanced {
  background: var(--color-orange-100, #ffedd5);
  color: var(--color-orange-700, #c2410c);
}

.difficulty-expert {
  background: var(--color-red-100, #fee2e2);
  color: var(--color-red-700, #b91c1c);
}

.format-button.recent {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
}

/* 无障碍支持 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .search-input {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: white;
  }
  
  .section-title {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .format-button:hover {
    background: var(--color-gray-700, #374151);
  }
  
  .format-icon {
    background: var(--color-gray-600, #4b5563);
    color: var(--color-gray-200, #e5e7eb);
  }
}
</style>
