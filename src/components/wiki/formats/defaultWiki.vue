<!--
  defaultWiki.vue - 默认 Wiki 格式页面
  为暂未实现的颜色格式提供基础信息展示
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="default-wiki">
    <!-- 格式概述 -->
    <section class="format-overview">
      <h1 class="format-title">{{ formatInfo.name }} 颜色格式</h1>
      <p class="format-description">
        {{ formatInfo.description }}
      </p>
      
      <div class="format-status">
        <div class="status-badge">
          📚 文档正在完善中
        </div>
        <p class="status-text">
          我们正在为 {{ formatInfo.name }} 格式编写详细的技术文档。
          如果您需要相关信息，请访问我们的转换工具或联系我们。
        </p>
      </div>
    </section>

    <!-- 基本信息 -->
    <section class="basic-info">
      <h2>基本信息</h2>
      <div class="info-grid">
        <div class="info-item">
          <h3>格式类型</h3>
          <p>{{ formatInfo.type }}</p>
        </div>
        <div class="info-item">
          <h3>应用领域</h3>
          <p>{{ formatInfo.usage }}</p>
        </div>
        <div class="info-item">
          <h3>复杂度</h3>
          <p>{{ formatInfo.difficulty }}</p>
        </div>
        <div class="info-item">
          <h3>标准支持</h3>
          <p>{{ formatInfo.standard }}</p>
        </div>
      </div>
    </section>

    <!-- 快速转换 -->
    <section class="quick-conversion">
      <h2>颜色转换</h2>
      <p>使用我们的专业转换工具进行 {{ formatInfo.name }} 格式转换：</p>
      <div class="converter-links">
        <router-link 
          v-for="converter in availableConverters" 
          :key="converter.path"
          :to="converter.path" 
          class="converter-link"
        >
          {{ converter.name }}
        </router-link>
      </div>
    </section>

    <!-- 相关资源 -->
    <section class="related-resources">
      <h2>相关资源</h2>
      <div class="resource-list">
        <div class="resource-item">
          <h3>📖 学习资源</h3>
          <p>查看其他已完成的颜色格式文档，了解颜色理论基础。</p>
          <div class="resource-links">
            <router-link to="/wiki/hex" class="resource-link">HEX 格式</router-link>
            <router-link to="/wiki/rgb" class="resource-link">RGB 格式</router-link>
            <router-link to="/wiki/hsl" class="resource-link">HSL 格式</router-link>
          </div>
        </div>
        
        <div class="resource-item">
          <h3>🔧 转换工具</h3>
          <p>使用我们的专业转换器进行颜色格式转换。</p>
          <router-link to="/converter" class="resource-link primary">
            访问转换器中心
          </router-link>
        </div>
        
        <div class="resource-item">
          <h3>💡 建议反馈</h3>
          <p>如果您需要 {{ formatInfo.name }} 格式的详细文档，请联系我们。</p>
          <a href="#contact" class="resource-link">联系我们</a>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  format: {
    type: String,
    required: true
  }
})

// 格式信息映射
const formatInfoMap = {
  hsv: {
    name: 'HSV',
    description: 'HSV (Hue, Saturation, Value) 颜色模型，也称为 HSB，基于色相、饱和度和明度来描述颜色。',
    type: '圆柱坐标系颜色模型',
    usage: '图像处理、颜色选择器',
    difficulty: '中级',
    standard: 'CSS Color Level 4'
  },
  cmyk: {
    name: 'CMYK',
    description: 'CMYK (Cyan, Magenta, Yellow, Key/Black) 是印刷行业标准的减色模型。',
    type: '减色模型',
    usage: '印刷设计、出版物',
    difficulty: '高级',
    standard: 'ISO 12647'
  },
  lch: {
    name: 'LCH',
    description: 'LCH (Lightness, Chroma, Hue) 是基于 LAB 颜色空间的圆柱坐标表示。',
    type: '感知均匀颜色空间',
    usage: '专业设计、科学应用',
    difficulty: '专家级',
    standard: 'CSS Color Level 4'
  },
  xyz: {
    name: 'XYZ',
    description: 'CIE XYZ 是国际照明委员会定义的标准颜色空间，是其他颜色空间的基础。',
    type: '设备无关颜色空间',
    usage: '颜色科学、标准化',
    difficulty: '专家级',
    standard: 'CIE 1931'
  },
  p3: {
    name: 'Display P3',
    description: 'Display P3 是苹果定义的广色域颜色空间，用于现代显示设备。',
    type: '广色域颜色空间',
    usage: '现代显示器、移动设备',
    difficulty: '专家级',
    standard: 'DCI-P3'
  },
  rec2020: {
    name: 'Rec2020',
    description: 'Rec. 2020 是超高清电视的颜色空间标准，提供极广的色域覆盖。',
    type: '广色域颜色空间',
    usage: '4K/8K 电视、专业显示',
    difficulty: '专家级',
    standard: 'ITU-R BT.2020'
  },
  keywords: {
    name: 'CSS Keywords',
    description: 'CSS 颜色关键词是预定义的颜色名称，如 red、blue、green 等。',
    type: '命名颜色',
    usage: 'Web 开发、快速原型',
    difficulty: '初级',
    standard: 'CSS Color Level 3/4'
  }
}

// 当前格式信息
const formatInfo = computed(() => {
  return formatInfoMap[props.format] || {
    name: props.format.toUpperCase(),
    description: `${props.format.toUpperCase()} 是一种颜色格式，详细文档正在编写中。`,
    type: '颜色格式',
    usage: '颜色表示',
    difficulty: '待定',
    standard: '待确认'
  }
})

// 可用的转换器
const availableConverters = computed(() => {
  const converters = [
    { path: '/converter/hex-rgb', name: 'HEX ↔ RGB' },
    { path: '/converter/rgb-hsl', name: 'RGB ↔ HSL' },
    { path: '/converter/hsl-hsv', name: 'HSL ↔ HSV' },
    { path: '/converter/oklch-hsl', name: 'OKLCH ↔ HSL' }
  ]
  
  // 过滤出与当前格式相关的转换器
  return converters.filter(converter => 
    converter.name.toLowerCase().includes(props.format.toLowerCase())
  )
})
</script>

<style scoped>
.default-wiki {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  line-height: 1.6;
}

.format-title {
  font-size: 2.5rem;
  color: #1f2937;
  margin-bottom: 1rem;
}

.format-description {
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
}

.format-status {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.status-badge {
  display: inline-block;
  background: #f59e0b;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.status-text {
  margin: 0;
  color: #92400e;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.info-item {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #6366f1;
}

.info-item h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
  font-size: 1rem;
}

.info-item p {
  margin: 0;
  color: #6b7280;
}

.converter-links {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.converter-link {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: #6366f1;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.converter-link:hover {
  background: #4f46e5;
}

.resource-list {
  display: grid;
  gap: 1.5rem;
}

.resource-item {
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.resource-item h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.resource-item p {
  margin: 0 0 1rem 0;
  color: #6b7280;
}

.resource-links {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.resource-link {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: #f3f4f6;
  color: #374151;
  text-decoration: none;
  border-radius: 4px;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.resource-link:hover {
  background: #e5e7eb;
}

.resource-link.primary {
  background: #6366f1;
  color: white;
}

.resource-link.primary:hover {
  background: #4f46e5;
}

section {
  margin-bottom: 3rem;
}

h2 {
  font-size: 1.875rem;
  color: #1f2937;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}
</style>
