<!--
  MarkdownWiki.vue - 基于 Markdown 的标准化 Wiki 组件
  用于渲染颜色格式的文档页面，内容来源于 markdown 文件
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="markdown-wiki">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载文档...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">⚠️</div>
      <h3>文档加载失败</h3>
      <p>{{ error.message }}</p>
      <button @click="loadContent" class="retry-btn">重试</button>
    </div>

    <!-- 文档内容 -->
    <div v-else class="wiki-content">
      <!-- 颜色预览区域 -->
      <section class="color-preview-section" v-if="showColorPreview">
        <div class="preview-container">
          <ColorPreview 
            :color="demoColor" 
            :show-contrast="true"
            :show-luminance="true"
            size="large"
          />
          <div class="preview-info">
            <code class="color-code">{{ demoColor }}</code>
            <button @click="copyToClipboard(demoColor)" class="copy-btn">
              复制
            </button>
          </div>
        </div>
      </section>

      <!-- Markdown 内容渲染 -->
      <article 
        class="markdown-content" 
        v-html="renderedContent"
      ></article>

      <!-- 快速转换工具 -->
      <section class="quick-converter-section" v-if="showQuickConverter">
        <h3>快速转换</h3>
        <QuickConverter 
          :input-format="format"
          :output-formats="getRelatedFormats()"
          :initial-color="demoColor"
          @color-change="handleColorChange"
        />
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useMarkdown } from '@/composables/useMarkdown'
import { useColorStore } from '@/stores/colorStore'
import ColorPreview from '@/components/common/ColorPreview.vue'
import QuickConverter from '../QuickConverter.vue'

const props = defineProps({
  format: {
    type: String,
    required: true
  },
  colorExamples: {
    type: Array,
    default: () => []
  },
  showColorPreview: {
    type: Boolean,
    default: true
  },
  showQuickConverter: {
    type: Boolean,
    default: true
  }
})

const colorStore = useColorStore()
const { loadMarkdownFile, isLoading, error } = useMarkdown()

// 响应式数据
const renderedContent = ref('')
const demoColor = ref('#FF6B35')

// 计算属性
const markdownPath = computed(() => `/docs/wiki/${props.format}.md`)

// 获取相关格式
const getRelatedFormats = () => {
  const relations = {
    'hex': ['rgb', 'hsl'],
    'rgb': ['hex', 'hsl', 'hsv'],
    'hsl': ['rgb', 'hsv', 'oklch'],
    'oklch': ['hsl', 'lch'],
    'cmyk': ['rgb', 'hex']
  }
  return relations[props.format] || ['hex', 'rgb']
}

// 方法
const loadContent = async () => {
  try {
    renderedContent.value = await loadMarkdownFile(markdownPath.value)
  } catch (err) {
    console.error('Failed to load markdown content:', err)
  }
}

const handleColorChange = (color) => {
  demoColor.value = color
  colorStore.parseColor(color)
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // 可以添加成功提示
  } catch (error) {
    console.warn('Failed to copy to clipboard:', error)
  }
}

// 生命周期
onMounted(() => {
  loadContent()
  
  // 设置默认演示颜色
  if (props.colorExamples && props.colorExamples.length > 0) {
    demoColor.value = props.colorExamples[0]
  }
})

// 监听格式变化
watch(() => props.format, () => {
  loadContent()
})
</script>

<style scoped>
.markdown-wiki {
  max-width: 800px;
  margin: 0 auto;
  padding: 0;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-gray-200, #e5e7eb);
  border-top: 4px solid var(--color-primary, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-state h3 {
  color: var(--color-red-600, #dc2626);
  margin: 0 0 0.5rem 0;
}

.error-state p {
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 1.5rem 0;
}

.retry-btn {
  padding: 0.75rem 1.5rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;
}

.retry-btn:hover {
  background: var(--color-primary-600, #2563eb);
}

/* 颜色预览区域 */
.color-preview-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 12px;
  border: 1px solid var(--color-gray-200, #e5e7eb);
}

.preview-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.preview-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.color-code {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

.copy-btn {
  padding: 0.5rem 1rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.2s ease;
  align-self: flex-start;
}

.copy-btn:hover {
  background: var(--color-primary-600, #2563eb);
}

/* 快速转换区域 */
.quick-converter-section {
  margin-top: 3rem;
  padding: 2rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 12px;
  border: 1px solid var(--color-gray-200, #e5e7eb);
}

.quick-converter-section h3 {
  margin: 0 0 1.5rem 0;
  color: var(--color-gray-900, #111827);
  font-size: 1.25rem;
  font-weight: 600;
}

/* Markdown 内容样式 */
.markdown-content {
  line-height: 1.7;
  color: var(--color-gray-800, #1f2937);
}

.markdown-content :deep(h1) {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1.5rem 0;
  border-bottom: 3px solid var(--color-primary, #3b82f6);
  padding-bottom: 0.75rem;
}

.markdown-content :deep(h2) {
  font-size: 1.875rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 2.5rem 0 1rem 0;
  border-bottom: 2px solid var(--color-gray-200, #e5e7eb);
  padding-bottom: 0.5rem;
}

.markdown-content :deep(h3) {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 2rem 0 1rem 0;
}

.markdown-content :deep(h4) {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-800, #1f2937);
  margin: 1.5rem 0 0.75rem 0;
}

.markdown-content :deep(p) {
  margin: 0 0 1.25rem 0;
  line-height: 1.7;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0 0 1.25rem 0;
  padding-left: 1.5rem;
}

.markdown-content :deep(li) {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.markdown-content :deep(code) {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  background: var(--color-gray-100, #f3f4f6);
  color: var(--color-gray-800, #1f2937);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.markdown-content :deep(pre) {
  background: var(--color-gray-900, #111827);
  color: var(--color-gray-100, #f3f4f6);
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

.markdown-content :deep(pre code) {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid var(--color-primary, #3b82f6);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 0 8px 8px 0;
}

.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.875rem;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
}

.markdown-content :deep(th) {
  background: var(--color-gray-50, #f9fafb);
  font-weight: 600;
  color: var(--color-gray-900, #111827);
}

.markdown-content :deep(a) {
  color: var(--color-primary, #3b82f6);
  text-decoration: none;
  font-weight: 500;
}

.markdown-content :deep(a:hover) {
  color: var(--color-primary-600, #2563eb);
  text-decoration: underline;
}

.markdown-content :deep(strong) {
  font-weight: 600;
  color: var(--color-gray-900, #111827);
}

.markdown-content :deep(em) {
  font-style: italic;
  color: var(--color-gray-700, #374151);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-container {
    flex-direction: column;
    text-align: center;
  }

  .color-preview-section {
    padding: 1rem;
  }

  .quick-converter-section {
    padding: 1.5rem;
  }

  .markdown-content :deep(h1) {
    font-size: 1.875rem;
  }

  .markdown-content :deep(h2) {
    font-size: 1.5rem;
  }

  .markdown-content :deep(pre) {
    padding: 1rem;
    font-size: 0.8rem;
  }

  .markdown-content :deep(table) {
    font-size: 0.8rem;
  }

  .markdown-content :deep(th),
  .markdown-content :deep(td) {
    padding: 0.5rem 0.75rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .markdown-content {
    color: var(--color-gray-200, #e5e7eb);
  }

  .markdown-content :deep(h1),
  .markdown-content :deep(h2),
  .markdown-content :deep(h3),
  .markdown-content :deep(h4) {
    color: white;
  }

  .markdown-content :deep(code) {
    background: var(--color-gray-700, #374151);
    color: var(--color-gray-100, #f3f4f6);
  }

  .markdown-content :deep(blockquote) {
    background: var(--color-gray-800, #1f2937);
  }

  .markdown-content :deep(th) {
    background: var(--color-gray-800, #1f2937);
    color: white;
  }

  .color-code {
    background: var(--color-gray-700, #374151);
    color: white;
  }
}
</style>
