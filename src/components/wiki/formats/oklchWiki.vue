<!--
  oklchWiki.vue - OKLCH 颜色格式 Wiki 页面
  基于 Markdown 的文档页面，提供 OKLCH 格式的详细文档

  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <MarkdownWiki
    :format="format"
    :color-examples="colorExamples"
    :show-color-preview="true"
    :show-quick-converter="true"
  />
</template>

<script setup>
import MarkdownWiki from './MarkdownWiki.vue'

const props = defineProps({
  format: {
    type: String,
    default: 'oklch'
  },
  colorExamples: {
    type: Array,
    default: () => ['oklch(0.7 0.15 180)', 'oklch(0.9 0.1 120)', 'oklch(0.5 0.2 0)', 'oklch(0.8 0.12 240)', 'oklch(0.6 0.18 60)']
  }
})
</script>


