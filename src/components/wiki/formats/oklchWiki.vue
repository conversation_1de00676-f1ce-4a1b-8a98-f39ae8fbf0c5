<!--
  oklchWiki.vue - OKLCH 颜色格式 Wiki 页面
  提供 OKLCH 颜色格式的详细技术文档
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="oklch-wiki">
    <!-- 格式概述 -->
    <section class="format-overview">
      <h1 class="format-title">OKLCH 颜色格式</h1>
      <p class="format-description">
        OKLCH (OK Lightness Chroma Hue) 是一种感知均匀的颜色空间，基于 OKLab 颜色空间，
        提供更准确的颜色感知和更好的颜色操作体验。
      </p>
      
      <div class="format-syntax">
        <h3>语法格式</h3>
        <code class="syntax-example">oklch(L C H / A)</code>
        <ul class="syntax-params">
          <li><strong>L (Lightness)</strong>: 亮度，范围 0-1 或 0%-100%</li>
          <li><strong>C (Chroma)</strong>: 色度，范围 0-0.4 或更高</li>
          <li><strong>H (Hue)</strong>: 色相，范围 0-360度</li>
          <li><strong>A (Alpha)</strong>: 透明度，范围 0-1（可选）</li>
        </ul>
      </div>
    </section>

    <!-- 颜色示例 -->
    <section class="color-examples">
      <h2>颜色示例</h2>
      <div class="examples-grid">
        <div 
          v-for="example in oklchExamples" 
          :key="example.name"
          class="color-example"
        >
          <div 
            class="color-swatch" 
            :style="{ backgroundColor: example.value }"
          ></div>
          <div class="example-info">
            <h4>{{ example.name }}</h4>
            <code>{{ example.value }}</code>
            <p class="example-desc">{{ example.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术特性 -->
    <section class="technical-features">
      <h2>技术特性</h2>
      <div class="features-grid">
        <div class="feature-card">
          <h3>感知均匀性</h3>
          <p>OKLCH 提供感知均匀的颜色空间，数值变化与人眼感知变化成正比。</p>
        </div>
        <div class="feature-card">
          <h3>色域支持</h3>
          <p>支持广色域显示器，如 Display P3 和 Rec2020 色彩空间。</p>
        </div>
        <div class="feature-card">
          <h3>颜色操作</h3>
          <p>更直观的颜色调整，亮度、饱和度和色相独立控制。</p>
        </div>
        <div class="feature-card">
          <h3>未来标准</h3>
          <p>CSS Color Level 4 规范的一部分，代表颜色技术的未来方向。</p>
        </div>
      </div>
    </section>

    <!-- 应用场景 -->
    <section class="use-cases">
      <h2>应用场景</h2>
      <div class="use-case-list">
        <div class="use-case">
          <h3>设计系统</h3>
          <p>构建感知一致的颜色调色板，确保颜色在不同亮度下的视觉平衡。</p>
        </div>
        <div class="use-case">
          <h3>广色域设计</h3>
          <p>利用现代显示器的广色域能力，创造更鲜艳、更准确的颜色。</p>
        </div>
        <div class="use-case">
          <h3>无障碍设计</h3>
          <p>基于感知亮度进行对比度计算，提供更准确的无障碍设计支持。</p>
        </div>
      </div>
    </section>

    <!-- 浏览器支持 -->
    <section class="browser-support">
      <h2>浏览器支持</h2>
      <div class="support-table">
        <div class="support-row">
          <span class="browser">Chrome</span>
          <span class="version">111+</span>
          <span class="status supported">✓ 支持</span>
        </div>
        <div class="support-row">
          <span class="browser">Firefox</span>
          <span class="version">113+</span>
          <span class="status supported">✓ 支持</span>
        </div>
        <div class="support-row">
          <span class="browser">Safari</span>
          <span class="version">16.4+</span>
          <span class="status supported">✓ 支持</span>
        </div>
        <div class="support-row">
          <span class="browser">Edge</span>
          <span class="version">111+</span>
          <span class="status supported">✓ 支持</span>
        </div>
      </div>
    </section>

    <!-- 转换工具 -->
    <section class="conversion-tools">
      <h2>颜色转换</h2>
      <p>使用我们的专业转换工具在 OKLCH 和其他颜色格式之间进行转换：</p>
      <div class="tool-links">
        <router-link to="/converter/oklch-hsl" class="tool-link">
          OKLCH ↔ HSL 转换器
        </router-link>
        <router-link to="/converter/oklch-rgb" class="tool-link">
          OKLCH ↔ RGB 转换器
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// OKLCH 颜色示例
const oklchExamples = ref([
  {
    name: '纯红色',
    value: 'oklch(0.628 0.257 29.234)',
    description: '感知均匀的红色，在 OKLCH 空间中的表示'
  },
  {
    name: '鲜艳蓝色',
    value: 'oklch(0.452 0.313 264.052)',
    description: '高饱和度的蓝色，展示 OKLCH 的色域优势'
  },
  {
    name: '自然绿色',
    value: 'oklch(0.518 0.176 142.495)',
    description: '自然的绿色调，适合界面设计'
  },
  {
    name: '温暖橙色',
    value: 'oklch(0.705 0.199 70.67)',
    description: '温暖的橙色，具有良好的视觉舒适度'
  }
])
</script>

<style scoped>
.oklch-wiki {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  line-height: 1.6;
}

.format-title {
  font-size: 2.5rem;
  color: #1f2937;
  margin-bottom: 1rem;
}

.format-description {
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 2rem;
}

.format-syntax {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.syntax-example {
  display: block;
  font-size: 1.25rem;
  font-weight: 600;
  color: #6366f1;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
}

.syntax-params {
  list-style: none;
  padding: 0;
}

.syntax-params li {
  margin-bottom: 0.5rem;
  color: #374151;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.color-example {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.color-swatch {
  height: 100px;
  width: 100%;
}

.example-info {
  padding: 1rem;
}

.example-info h4 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.example-info code {
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.example-desc {
  margin: 0.5rem 0 0 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.feature-card {
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #6366f1;
}

.feature-card h3 {
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.use-case-list {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.use-case {
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.use-case h3 {
  margin: 0 0 0.5rem 0;
  color: #6366f1;
}

.support-table {
  display: grid;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.support-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 4px;
}

.supported {
  color: #059669;
  font-weight: 600;
}

.tool-links {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.tool-link {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: #6366f1;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.tool-link:hover {
  background: #4f46e5;
}

section {
  margin-bottom: 3rem;
}

h2 {
  font-size: 1.875rem;
  color: #1f2937;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}
</style>
