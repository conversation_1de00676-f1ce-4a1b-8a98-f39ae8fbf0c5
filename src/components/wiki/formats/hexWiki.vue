<!--
  hexWiki.vue - HEX 颜色格式 Wiki 页面
  提供 HEX 格式的详细文档和交互示例
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="hex-wiki">
    <!-- 格式概览 -->
    <section class="format-overview">
      <div class="overview-demo">
        <ColorPreview 
          :color="demoColor" 
          :show-contrast="true"
          :show-luminance="true"
          size="large"
        />
        <div class="demo-code">
          <code class="hex-code">{{ demoColor }}</code>
          <button @click="copyToClipboard(demoColor)" class="copy-btn">
            复制
          </button>
        </div>
      </div>
      
      <div class="overview-info">
        <h2 class="section-title">HEX 颜色格式</h2>
        <p class="format-description">
          HEX（十六进制）颜色代码是 Web 开发中最常用的颜色表示方法。
          它使用 6 位十六进制数字来表示红、绿、蓝三个颜色通道的强度。
        </p>
        
        <div class="format-specs">
          <div class="spec-item">
            <span class="spec-label">格式:</span>
            <span class="spec-value">#RRGGBB</span>
          </div>
          <div class="spec-item">
            <span class="spec-label">范围:</span>
            <span class="spec-value">00-FF (0-255)</span>
          </div>
          <div class="spec-item">
            <span class="spec-label">浏览器支持:</span>
            <span class="spec-value">100%</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 格式详解 -->
    <section class="format-details">
      <h3 class="section-title">格式详解</h3>
      
      <div class="hex-breakdown">
        <div class="breakdown-visual">
          <div class="hex-parts">
            <span class="hex-hash">#</span>
            <span class="hex-red">{{ redHex }}</span>
            <span class="hex-green">{{ greenHex }}</span>
            <span class="hex-blue">{{ blueHex }}</span>
          </div>
          <div class="parts-labels">
            <span class="label-hash">前缀</span>
            <span class="label-red">红色 ({{ redDecimal }})</span>
            <span class="label-green">绿色 ({{ greenDecimal }})</span>
            <span class="label-blue">蓝色 ({{ blueDecimal }})</span>
          </div>
        </div>
        
        <div class="breakdown-explanation">
          <ul class="explanation-list">
            <li><strong>#</strong> - 井号前缀，标识这是一个 HEX 颜色值</li>
            <li><strong>RR</strong> - 红色通道，范围 00-FF (0-255)</li>
            <li><strong>GG</strong> - 绿色通道，范围 00-FF (0-255)</li>
            <li><strong>BB</strong> - 蓝色通道，范围 00-FF (0-255)</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 常用示例 -->
    <section class="format-examples">
      <h3 class="section-title">常用示例</h3>
      
      <div class="examples-grid">
        <div 
          v-for="example in hexExamples" 
          :key="example.hex"
          class="example-card"
          @click="selectExample(example.hex)"
        >
          <ColorSwatch 
            :color="example.hex" 
            :show-label="true"
            :label="example.hex"
            size="large"
          />
          <div class="example-info">
            <h4 class="example-name">{{ example.name }}</h4>
            <p class="example-description">{{ example.description }}</p>
            <div class="example-values">
              <span class="value-hex">{{ example.hex }}</span>
              <span class="value-rgb">{{ hexToRgb(example.hex) }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 快速转换工具 -->
    <section class="quick-converter">
      <h3 class="section-title">快速转换</h3>
      
      <QuickConverter 
        :input-format="'hex'"
        :output-formats="['rgb', 'hsl', 'hsv']"
        :initial-color="demoColor"
        @color-change="handleColorChange"
      />
    </section>

    <!-- 最佳实践 -->
    <section class="best-practices">
      <h3 class="section-title">最佳实践</h3>
      
      <div class="practices-grid">
        <div class="practice-card">
          <div class="practice-icon">✅</div>
          <h4 class="practice-title">推荐做法</h4>
          <ul class="practice-list">
            <li>始终使用 6 位完整格式 (#FF0000)</li>
            <li>使用大写字母保持一致性</li>
            <li>为重要颜色定义 CSS 变量</li>
            <li>考虑无障碍对比度要求</li>
          </ul>
        </div>
        
        <div class="practice-card">
          <div class="practice-icon">❌</div>
          <h4 class="practice-title">避免做法</h4>
          <ul class="practice-list">
            <li>混用 3 位和 6 位格式</li>
            <li>忘记井号前缀</li>
            <li>使用无效的十六进制字符</li>
            <li>忽略颜色的语义含义</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 浏览器兼容性 -->
    <section class="browser-support">
      <h3 class="section-title">浏览器兼容性</h3>
      
      <div class="support-table">
        <div class="support-row">
          <span class="browser-name">Chrome</span>
          <span class="support-status excellent">完全支持</span>
          <span class="version-info">所有版本</span>
        </div>
        <div class="support-row">
          <span class="browser-name">Firefox</span>
          <span class="support-status excellent">完全支持</span>
          <span class="version-info">所有版本</span>
        </div>
        <div class="support-row">
          <span class="browser-name">Safari</span>
          <span class="support-status excellent">完全支持</span>
          <span class="version-info">所有版本</span>
        </div>
        <div class="support-row">
          <span class="browser-name">Edge</span>
          <span class="support-status excellent">完全支持</span>
          <span class="version-info">所有版本</span>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import ColorPreview from '@/components/common/ColorPreview.vue'
import ColorSwatch from '@/components/common/ColorSwatch.vue'
import QuickConverter from '../QuickConverter.vue'

const props = defineProps({
  format: {
    type: String,
    default: 'hex'
  },
  colorExamples: {
    type: Array,
    default: () => []
  }
})

const colorStore = useColorStore()
const demoColor = ref('#FF6B35')

// HEX 示例颜色
const hexExamples = [
  {
    hex: '#FF0000',
    name: '纯红色',
    description: '最基本的红色，RGB 值为 (255, 0, 0)'
  },
  {
    hex: '#00FF00',
    name: '纯绿色',
    description: '最基本的绿色，RGB 值为 (0, 255, 0)'
  },
  {
    hex: '#0000FF',
    name: '纯蓝色',
    description: '最基本的蓝色，RGB 值为 (0, 0, 255)'
  },
  {
    hex: '#FFFFFF',
    name: '纯白色',
    description: '所有通道最大值，RGB 值为 (255, 255, 255)'
  },
  {
    hex: '#000000',
    name: '纯黑色',
    description: '所有通道最小值，RGB 值为 (0, 0, 0)'
  },
  {
    hex: '#808080',
    name: '中性灰',
    description: '50% 灰度，RGB 值为 (128, 128, 128)'
  }
]

// 计算属性
const redHex = computed(() => demoColor.value.slice(1, 3))
const greenHex = computed(() => demoColor.value.slice(3, 5))
const blueHex = computed(() => demoColor.value.slice(5, 7))

const redDecimal = computed(() => parseInt(redHex.value, 16))
const greenDecimal = computed(() => parseInt(greenHex.value, 16))
const blueDecimal = computed(() => parseInt(blueHex.value, 16))

// 方法
const hexToRgb = (hex) => {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  return `rgb(${r}, ${g}, ${b})`
}

const selectExample = (hex) => {
  demoColor.value = hex
}

const handleColorChange = (color) => {
  demoColor.value = color
  colorStore.parseColor(color)
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // 可以添加成功提示
  } catch (error) {
    console.warn('Failed to copy to clipboard:', error)
  }
}
</script>

<style scoped>
.hex-wiki {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.format-overview {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.overview-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.demo-code {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.hex-code {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

.copy-btn {
  padding: 0.5rem 1rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.copy-btn:hover {
  background: var(--color-primary-600, #2563eb);
}

.overview-info {
  flex: 1;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.format-description {
  font-size: 1rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.format-specs {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.spec-label {
  font-weight: 500;
  color: var(--color-gray-700, #374151);
  min-width: 80px;
}

.spec-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-weight: 600;
  color: var(--color-gray-900, #111827);
}

.hex-breakdown {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 12px;
}

.breakdown-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.hex-parts {
  display: flex;
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 2rem;
  font-weight: bold;
}

.hex-hash { color: var(--color-gray-500, #6b7280); }
.hex-red { color: var(--color-red-500, #ef4444); }
.hex-green { color: var(--color-green-500, #10b981); }
.hex-blue { color: var(--color-blue-500, #3b82f6); }

.parts-labels {
  display: flex;
  gap: 2rem;
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
}

.explanation-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.explanation-list li {
  font-size: 0.875rem;
  color: var(--color-gray-700, #374151);
  line-height: 1.5;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.example-card {
  padding: 1.5rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.example-card:hover {
  border-color: var(--color-primary, #3b82f6);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.example-info {
  margin-top: 1rem;
}

.example-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.example-description {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.example-values {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.value-hex,
.value-rgb {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.75rem;
  color: var(--color-gray-700, #374151);
}

.practices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.practice-card {
  padding: 2rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
}

.practice-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.practice-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.practice-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.practice-list li {
  font-size: 0.875rem;
  color: var(--color-gray-700, #374151);
  line-height: 1.5;
  padding-left: 1.5rem;
  position: relative;
}

.practice-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-gray-400, #9ca3af);
}

.support-table {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.support-row {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 8px;
}

.browser-name {
  font-weight: 500;
  color: var(--color-gray-900, #111827);
}

.support-status {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.support-status.excellent {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-700, #15803d);
}

.version-info {
  font-size: 0.875rem;
  color: var(--color-gray-500, #6b7280);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .format-overview {
    flex-direction: column;
  }
  
  .hex-breakdown {
    padding: 1.5rem;
  }
  
  .hex-parts {
    font-size: 1.5rem;
  }
  
  .parts-labels {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .examples-grid {
    grid-template-columns: 1fr;
  }
  
  .practices-grid {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .section-title {
    color: white;
  }
  
  .format-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .hex-code {
    background: var(--color-gray-700, #374151);
    color: white;
  }
  
  .hex-breakdown {
    background: var(--color-gray-800, #1f2937);
  }
  
  .example-card,
  .practice-card {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .example-name,
  .practice-title {
    color: white;
  }
  
  .support-row {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .browser-name {
    color: white;
  }
}
</style>
