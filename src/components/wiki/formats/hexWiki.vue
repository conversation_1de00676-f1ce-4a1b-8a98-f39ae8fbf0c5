<!--
  hexWiki.vue - HEX 颜色格式 Wiki 页面
  基于 Markdown 的文档页面，提供 HEX 格式的详细文档

  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <MarkdownWiki
    :format="format"
    :color-examples="colorExamples"
    :show-color-preview="true"
    :show-quick-converter="true"
  />
</template>

<script setup>
import MarkdownWiki from './MarkdownWiki.vue'

const props = defineProps({
  format: {
    type: String,
    default: 'hex'
  },
  colorExamples: {
    type: Array,
    default: () => ['#FF6B35', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
  }
})
</script>



