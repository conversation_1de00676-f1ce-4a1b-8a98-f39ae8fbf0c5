<!--
  hslWiki.vue - HSL 颜色格式 Wiki 页面
  基于 Markdown 的文档页面，提供 HSL 格式的详细文档

  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <MarkdownWiki
    :format="format"
    :color-examples="colorExamples"
    :show-color-preview="true"
    :show-quick-converter="true"
  />
</template>

<script setup>
import MarkdownWiki from './MarkdownWiki.vue'

const props = defineProps({
  format: {
    type: String,
    default: 'hsl'
  },
  colorExamples: {
    type: Array,
    default: () => ['hsl(14, 100%, 61%)', 'hsl(176, 57%, 55%)', 'hsl(197, 62%, 54%)', 'hsl(142, 52%, 70%)', 'hsl(45, 100%, 82%)']
  }
})
</script>




