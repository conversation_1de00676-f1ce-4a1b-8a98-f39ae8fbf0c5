<!--
  hslWiki.vue - HSL 颜色格式 Wiki 页面
  提供 HSL 格式的详细文档和交互示例
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="hsl-wiki">
    <!-- 格式概览 -->
    <section class="format-overview">
      <div class="overview-demo">
        <ColorPreview 
          :color="demoColor" 
          :show-contrast="true"
          :show-luminance="true"
          size="large"
        />
        <div class="demo-code">
          <code class="hsl-code">{{ hslValue }}</code>
          <button @click="copyToClipboard(hslValue)" class="copy-btn">
            复制
          </button>
        </div>
      </div>
      
      <div class="overview-info">
        <h2 class="section-title">HSL 颜色格式</h2>
        <p class="format-description">
          HSL（Hue, Saturation, Lightness）是一种更符合人类对颜色感知的颜色模型。
          它通过色相、饱和度和亮度三个维度来描述颜色，
          使得颜色调整和配色方案设计更加直观。
        </p>
        
        <div class="format-specs">
          <div class="spec-item">
            <span class="spec-label">色相 (H):</span>
            <span class="spec-value">0-360°</span>
          </div>
          <div class="spec-item">
            <span class="spec-label">饱和度 (S):</span>
            <span class="spec-value">0-100%</span>
          </div>
          <div class="spec-item">
            <span class="spec-label">亮度 (L):</span>
            <span class="spec-value">0-100%</span>
          </div>
        </div>
      </div>
    </section>

    <!-- HSL 参数控制 -->
    <section class="hsl-controls">
      <h3 class="section-title">HSL 参数控制</h3>
      
      <div class="controls-demo">
        <div class="control-sliders">
          <div class="slider-group">
            <label class="slider-label">
              色相 (Hue): {{ hueValue }}°
            </label>
            <input
              v-model.number="hueValue"
              type="range"
              min="0"
              max="360"
              class="color-slider hue-slider"
              @input="updateColor"
            />
            <div class="hue-spectrum"></div>
          </div>
          
          <div class="slider-group">
            <label class="slider-label">
              饱和度 (Saturation): {{ saturationValue }}%
            </label>
            <input
              v-model.number="saturationValue"
              type="range"
              min="0"
              max="100"
              class="color-slider saturation-slider"
              @input="updateColor"
            />
            <div class="saturation-gradient" :style="saturationGradientStyle"></div>
          </div>
          
          <div class="slider-group">
            <label class="slider-label">
              亮度 (Lightness): {{ lightnessValue }}%
            </label>
            <input
              v-model.number="lightnessValue"
              type="range"
              min="0"
              max="100"
              class="color-slider lightness-slider"
              @input="updateColor"
            />
            <div class="lightness-gradient" :style="lightnessGradientStyle"></div>
          </div>
        </div>
        
        <div class="color-wheel-container">
          <div class="color-wheel">
            <div class="wheel-center" :style="{ backgroundColor: demoColor }"></div>
          </div>
          <div class="color-info">
            <div class="info-item">
              <span class="info-label">当前颜色:</span>
              <span class="info-value">{{ hslValue }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">HEX 值:</span>
              <span class="info-value">{{ hexValue }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 色相环说明 -->
    <section class="hue-circle-explanation">
      <h3 class="section-title">色相环 (Hue Circle)</h3>
      
      <div class="hue-explanation">
        <div class="hue-circle">
          <div class="hue-markers">
            <div class="hue-marker red" style="transform: rotate(0deg)">
              <span>0° 红</span>
            </div>
            <div class="hue-marker yellow" style="transform: rotate(60deg)">
              <span>60° 黄</span>
            </div>
            <div class="hue-marker green" style="transform: rotate(120deg)">
              <span>120° 绿</span>
            </div>
            <div class="hue-marker cyan" style="transform: rotate(180deg)">
              <span>180° 青</span>
            </div>
            <div class="hue-marker blue" style="transform: rotate(240deg)">
              <span>240° 蓝</span>
            </div>
            <div class="hue-marker magenta" style="transform: rotate(300deg)">
              <span>300° 品红</span>
            </div>
          </div>
        </div>
        
        <div class="hue-description">
          <h4>色相 (Hue) 说明</h4>
          <p>
            色相表示颜色在色相环上的位置，以角度表示（0-360°）。
            它决定了颜色的基本类型，如红色、绿色、蓝色等。
          </p>
          
          <div class="hue-examples">
            <div class="hue-example" v-for="example in hueExamples" :key="example.hue">
              <ColorSwatch :color="example.color" :show-label="true" :label="`${example.hue}°`" />
              <span class="hue-name">{{ example.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 饱和度和亮度说明 -->
    <section class="saturation-lightness">
      <h3 class="section-title">饱和度与亮度</h3>
      
      <div class="sl-grid">
        <div class="saturation-demo">
          <h4>饱和度 (Saturation)</h4>
          <p>饱和度控制颜色的纯度或强度。100% 为最纯的颜色，0% 为灰色。</p>
          
          <div class="saturation-examples">
            <div v-for="s in [0, 25, 50, 75, 100]" :key="s" class="sat-example">
              <ColorSwatch 
                :color="`hsl(${hueValue}, ${s}%, 50%)`" 
                :show-label="true" 
                :label="`${s}%`" 
              />
            </div>
          </div>
        </div>
        
        <div class="lightness-demo">
          <h4>亮度 (Lightness)</h4>
          <p>亮度控制颜色的明暗程度。0% 为黑色，50% 为正常颜色，100% 为白色。</p>
          
          <div class="lightness-examples">
            <div v-for="l in [0, 25, 50, 75, 100]" :key="l" class="light-example">
              <ColorSwatch 
                :color="`hsl(${hueValue}, 100%, ${l}%)`" 
                :show-label="true" 
                :label="`${l}%`" 
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 快速转换工具 -->
    <section class="quick-converter">
      <h3 class="section-title">快速转换</h3>
      
      <QuickConverter 
        :input-format="'hsl'"
        :output-formats="['hex', 'rgb', 'hsv']"
        :initial-color="hslValue"
        @color-change="handleColorChange"
      />
    </section>

    <!-- 配色方案生成 -->
    <section class="color-schemes">
      <h3 class="section-title">配色方案</h3>
      
      <div class="schemes-container">
        <div class="scheme-selector">
          <button 
            v-for="scheme in colorSchemes" 
            :key="scheme.type"
            @click="selectedScheme = scheme.type"
            class="scheme-button"
            :class="{ active: selectedScheme === scheme.type }"
          >
            {{ scheme.name }}
          </button>
        </div>
        
        <div class="scheme-preview">
          <div class="scheme-colors">
            <ColorSwatch 
              v-for="(color, index) in currentSchemeColors" 
              :key="index"
              :color="color" 
              :show-label="true"
              :label="color"
              size="large"
            />
          </div>
          <p class="scheme-description">
            {{ currentSchemeDescription }}
          </p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import { ColorUtils } from '@/utils/colorUtils'
import ColorPreview from '@/components/common/ColorPreview.vue'
import ColorSwatch from '@/components/common/ColorSwatch.vue'
import QuickConverter from '../QuickConverter.vue'

const props = defineProps({
  format: {
    type: String,
    default: 'hsl'
  },
  colorExamples: {
    type: Array,
    default: () => []
  }
})

const colorStore = useColorStore()

// 响应式数据
const hueValue = ref(14)
const saturationValue = ref(100)
const lightnessValue = ref(61)
const selectedScheme = ref('complementary')

// 色相示例
const hueExamples = [
  { hue: 0, name: '红色', color: 'hsl(0, 100%, 50%)' },
  { hue: 60, name: '黄色', color: 'hsl(60, 100%, 50%)' },
  { hue: 120, name: '绿色', color: 'hsl(120, 100%, 50%)' },
  { hue: 180, name: '青色', color: 'hsl(180, 100%, 50%)' },
  { hue: 240, name: '蓝色', color: 'hsl(240, 100%, 50%)' },
  { hue: 300, name: '品红', color: 'hsl(300, 100%, 50%)' }
]

// 配色方案
const colorSchemes = [
  { type: 'complementary', name: '补色' },
  { type: 'triadic', name: '三角色' },
  { type: 'analogous', name: '类似色' },
  { type: 'monochromatic', name: '单色' }
]

// 计算属性
const hslValue = computed(() => {
  return `hsl(${hueValue.value}, ${saturationValue.value}%, ${lightnessValue.value}%)`
})

const demoColor = computed(() => {
  return hslValue.value
})

const hexValue = computed(() => {
  return colorStore.convertToFormat('hex') || '#000000'
})

const saturationGradientStyle = computed(() => {
  return {
    background: `linear-gradient(to right, 
      hsl(${hueValue.value}, 0%, ${lightnessValue.value}%), 
      hsl(${hueValue.value}, 100%, ${lightnessValue.value}%))`
  }
})

const lightnessGradientStyle = computed(() => {
  return {
    background: `linear-gradient(to right, 
      hsl(${hueValue.value}, ${saturationValue.value}%, 0%), 
      hsl(${hueValue.value}, ${saturationValue.value}%, 50%), 
      hsl(${hueValue.value}, ${saturationValue.value}%, 100%))`
  }
})

const currentSchemeColors = computed(() => {
  return ColorUtils.generateColorScheme(demoColor.value, selectedScheme.value)
})

const currentSchemeDescription = computed(() => {
  const descriptions = {
    complementary: '补色方案使用色相环上相对的颜色，产生强烈的对比效果。',
    triadic: '三角色方案使用色相环上等距的三个颜色，平衡且充满活力。',
    analogous: '类似色方案使用色相环上相邻的颜色，和谐且舒适。',
    monochromatic: '单色方案使用同一色相的不同亮度，统一且优雅。'
  }
  return descriptions[selectedScheme.value] || ''
})

// 方法
const updateColor = () => {
  handleColorChange(hslValue.value)
}

const handleColorChange = (color) => {
  colorStore.parseColor(color)
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
  } catch (error) {
    console.warn('Failed to copy to clipboard:', error)
  }
}
</script>

<style scoped>
.hsl-wiki {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.format-overview {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.overview-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.demo-code {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.hsl-code {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

.copy-btn {
  padding: 0.5rem 1rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.copy-btn:hover {
  background: var(--color-primary-600, #2563eb);
}

.overview-info {
  flex: 1;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.format-description {
  font-size: 1rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.format-specs {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.spec-label {
  font-weight: 500;
  color: var(--color-gray-700, #374151);
  min-width: 100px;
}

.spec-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-weight: 600;
  color: var(--color-gray-900, #111827);
}

.controls-demo {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 3rem;
  padding: 2rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 12px;
}

.control-sliders {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.slider-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.slider-label {
  font-weight: 500;
  color: var(--color-gray-700, #374151);
  font-size: 0.875rem;
}

.color-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  background: transparent;
}

.hue-spectrum {
  height: 20px;
  border-radius: 10px;
  background: linear-gradient(to right, 
    hsl(0, 100%, 50%), hsl(60, 100%, 50%), hsl(120, 100%, 50%), 
    hsl(180, 100%, 50%), hsl(240, 100%, 50%), hsl(300, 100%, 50%), 
    hsl(360, 100%, 50%));
}

.saturation-gradient,
.lightness-gradient {
  height: 20px;
  border-radius: 10px;
}

.color-wheel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.color-wheel {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: conic-gradient(
    hsl(0, 100%, 50%), hsl(60, 100%, 50%), hsl(120, 100%, 50%), 
    hsl(180, 100%, 50%), hsl(240, 100%, 50%), hsl(300, 100%, 50%), 
    hsl(360, 100%, 50%)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.wheel-center {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.color-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
}

.info-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  color: var(--color-gray-900, #111827);
}

.hue-explanation {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: center;
}

.hue-circle {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: conic-gradient(
    hsl(0, 100%, 50%), hsl(60, 100%, 50%), hsl(120, 100%, 50%), 
    hsl(180, 100%, 50%), hsl(240, 100%, 50%), hsl(300, 100%, 50%), 
    hsl(360, 100%, 50%)
  );
  position: relative;
}

.hue-markers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hue-marker {
  position: absolute;
  top: 10px;
  left: 50%;
  transform-origin: 50% 140px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.hue-description h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.hue-description p {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.hue-examples {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hue-example {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.hue-name {
  font-size: 0.75rem;
  color: var(--color-gray-600, #6b7280);
}

.sl-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.saturation-demo h4,
.lightness-demo h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.75rem 0;
}

.saturation-demo p,
.lightness-demo p {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.5;
  margin: 0 0 1.5rem 0;
}

.saturation-examples,
.lightness-examples {
  display: flex;
  gap: 1rem;
}

.sat-example,
.light-example {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.schemes-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.scheme-selector {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.scheme-button {
  padding: 0.75rem 1.5rem;
  background: white;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700, #374151);
  cursor: pointer;
  transition: all 0.2s ease;
}

.scheme-button:hover {
  border-color: var(--color-primary, #3b82f6);
  color: var(--color-primary, #3b82f6);
}

.scheme-button.active {
  background: var(--color-primary, #3b82f6);
  border-color: var(--color-primary, #3b82f6);
  color: white;
}

.scheme-preview {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.scheme-colors {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.scheme-description {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.5;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .format-overview {
    flex-direction: column;
  }
  
  .controls-demo {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1.5rem;
  }
  
  .hue-explanation {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .hue-circle {
    width: 250px;
    height: 250px;
    margin: 0 auto;
  }
  
  .sl-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .saturation-examples,
  .lightness-examples {
    flex-wrap: wrap;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .section-title {
    color: white;
  }
  
  .format-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .hsl-code {
    background: var(--color-gray-700, #374151);
    color: white;
  }
  
  .controls-demo {
    background: var(--color-gray-800, #1f2937);
  }
  
  .hue-description h4,
  .saturation-demo h4,
  .lightness-demo h4 {
    color: white;
  }
  
  .scheme-button {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: var(--color-gray-200, #e5e7eb);
  }
  
  .scheme-button:hover {
    border-color: var(--color-primary, #3b82f6);
    color: var(--color-primary, #3b82f6);
  }
}
</style>
