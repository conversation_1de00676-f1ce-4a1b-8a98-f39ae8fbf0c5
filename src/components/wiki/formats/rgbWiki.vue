<!--
  rgbWiki.vue - RGB 颜色格式 Wiki 页面
  基于 Markdown 的文档页面，提供 RGB 格式的详细文档

  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <MarkdownWiki
    :format="format"
    :color-examples="colorExamples"
    :show-color-preview="true"
    :show-quick-converter="true"
  />
</template>

<script setup>
import MarkdownWiki from './MarkdownWiki.vue'

const props = defineProps({
  format: {
    type: String,
    default: 'rgb'
  },
  colorExamples: {
    type: Array,
    default: () => ['rgb(255, 107, 53)', 'rgb(78, 205, 196)', 'rgb(69, 183, 209)', 'rgb(150, 206, 180)', 'rgb(255, 234, 167)']
  }
})
</script>



