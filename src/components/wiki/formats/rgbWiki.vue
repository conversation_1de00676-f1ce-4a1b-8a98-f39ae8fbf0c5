<!--
  rgbWiki.vue - RGB 颜色格式 Wiki 页面
  提供 RGB 格式的详细文档和交互示例
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="rgb-wiki">
    <!-- 格式概览 -->
    <section class="format-overview">
      <div class="overview-demo">
        <ColorPreview 
          :color="demoColor" 
          :show-contrast="true"
          :show-luminance="true"
          size="large"
        />
        <div class="demo-code">
          <code class="rgb-code">{{ rgbValue }}</code>
          <button @click="copyToClipboard(rgbValue)" class="copy-btn">
            复制
          </button>
        </div>
      </div>
      
      <div class="overview-info">
        <h2 class="section-title">RGB 颜色格式</h2>
        <p class="format-description">
          RGB（Red, Green, Blue）是基于光的加色混合原理的颜色模型。
          它通过红、绿、蓝三个颜色通道的不同强度组合来表示颜色，
          是显示器和数字图像处理中最常用的颜色空间。
        </p>
        
        <div class="format-specs">
          <div class="spec-item">
            <span class="spec-label">格式:</span>
            <span class="spec-value">rgb(R, G, B)</span>
          </div>
          <div class="spec-item">
            <span class="spec-label">范围:</span>
            <span class="spec-value">0-255</span>
          </div>
          <div class="spec-item">
            <span class="spec-label">透明度:</span>
            <span class="spec-value">rgba(R, G, B, A)</span>
          </div>
        </div>
      </div>
    </section>

    <!-- RGB 通道分解 -->
    <section class="rgb-channels">
      <h3 class="section-title">RGB 通道分解</h3>
      
      <div class="channels-demo">
        <div class="channel-sliders">
          <div class="slider-group">
            <label class="slider-label">
              红色 (R): {{ redValue }}
            </label>
            <input
              v-model.number="redValue"
              type="range"
              min="0"
              max="255"
              class="color-slider red-slider"
              @input="updateColor"
            />
          </div>
          
          <div class="slider-group">
            <label class="slider-label">
              绿色 (G): {{ greenValue }}
            </label>
            <input
              v-model.number="greenValue"
              type="range"
              min="0"
              max="255"
              class="color-slider green-slider"
              @input="updateColor"
            />
          </div>
          
          <div class="slider-group">
            <label class="slider-label">
              蓝色 (B): {{ blueValue }}
            </label>
            <input
              v-model.number="blueValue"
              type="range"
              min="0"
              max="255"
              class="color-slider blue-slider"
              @input="updateColor"
            />
          </div>
        </div>
        
        <div class="channel-visualization">
          <div class="channel-bars">
            <div class="channel-bar red-bar" :style="{ width: `${(redValue / 255) * 100}%` }"></div>
            <div class="channel-bar green-bar" :style="{ width: `${(greenValue / 255) * 100}%` }"></div>
            <div class="channel-bar blue-bar" :style="{ width: `${(blueValue / 255) * 100}%` }"></div>
          </div>
          
          <div class="channel-swatches">
            <ColorSwatch :color="`rgb(${redValue}, 0, 0)`" :show-label="true" label="红色通道" />
            <ColorSwatch :color="`rgb(0, ${greenValue}, 0)`" :show-label="true" label="绿色通道" />
            <ColorSwatch :color="`rgb(0, 0, ${blueValue})`" :show-label="true" label="蓝色通道" />
          </div>
        </div>
      </div>
    </section>

    <!-- 常用示例 -->
    <section class="format-examples">
      <h3 class="section-title">常用示例</h3>
      
      <div class="examples-grid">
        <div 
          v-for="example in rgbExamples" 
          :key="example.rgb"
          class="example-card"
          @click="selectExample(example)"
        >
          <ColorSwatch 
            :color="example.rgb" 
            :show-label="true"
            :label="example.rgb"
            size="large"
          />
          <div class="example-info">
            <h4 class="example-name">{{ example.name }}</h4>
            <p class="example-description">{{ example.description }}</p>
            <div class="example-values">
              <span class="value-rgb">{{ example.rgb }}</span>
              <span class="value-hex">{{ example.hex }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 颜色空间说明 -->
    <section class="color-space-explanation">
      <h3 class="section-title">RGB 颜色空间</h3>
      
      <div class="explanation-content">
        <div class="explanation-text">
          <h4>加色混合原理</h4>
          <p>
            RGB 基于光的加色混合原理，通过红、绿、蓝三种光的不同强度组合产生各种颜色。
            当三个通道都为最大值 (255) 时产生白色，都为最小值 (0) 时产生黑色。
          </p>
          
          <h4>设备相关性</h4>
          <p>
            RGB 是设备相关的颜色空间，同一个 RGB 值在不同显示器上可能显示不同的颜色。
            这是因为不同设备的红、绿、蓝原色点和白点可能不同。
          </p>
          
          <h4>应用场景</h4>
          <ul class="application-list">
            <li>显示器和电视屏幕</li>
            <li>数字摄影和图像处理</li>
            <li>Web 开发和 UI 设计</li>
            <li>LED 照明控制</li>
          </ul>
        </div>
        
        <div class="color-cube">
          <div class="cube-title">RGB 颜色立方体</div>
          <div class="cube-description">
            RGB 颜色空间可以表示为一个立方体，其中：
            <ul>
              <li>原点 (0,0,0) 为黑色</li>
              <li>对角点 (255,255,255) 为白色</li>
              <li>三个轴分别代表红、绿、蓝通道</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 快速转换工具 -->
    <section class="quick-converter">
      <h3 class="section-title">快速转换</h3>
      
      <QuickConverter 
        :input-format="'rgb'"
        :output-formats="['hex', 'hsl', 'hsv']"
        :initial-color="rgbValue"
        @color-change="handleColorChange"
      />
    </section>

    <!-- 最佳实践 -->
    <section class="best-practices">
      <h3 class="section-title">最佳实践</h3>
      
      <div class="practices-grid">
        <div class="practice-card">
          <div class="practice-icon">✅</div>
          <h4 class="practice-title">推荐做法</h4>
          <ul class="practice-list">
            <li>使用 rgba() 添加透明度</li>
            <li>考虑不同设备的色彩显示差异</li>
            <li>为关键颜色提供备选方案</li>
            <li>使用色彩管理确保一致性</li>
          </ul>
        </div>
        
        <div class="practice-card">
          <div class="practice-icon">❌</div>
          <h4 class="practice-title">避免做法</h4>
          <ul class="practice-list">
            <li>超出 0-255 的数值范围</li>
            <li>忽略透明度通道的影响</li>
            <li>在印刷设计中直接使用 RGB</li>
            <li>不考虑色彩无障碍需求</li>
          </ul>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import ColorPreview from '@/components/common/ColorPreview.vue'
import ColorSwatch from '@/components/common/ColorSwatch.vue'
import QuickConverter from '../QuickConverter.vue'

const props = defineProps({
  format: {
    type: String,
    default: 'rgb'
  },
  colorExamples: {
    type: Array,
    default: () => []
  }
})

const colorStore = useColorStore()

// 响应式数据
const redValue = ref(255)
const greenValue = ref(107)
const blueValue = ref(53)

// RGB 示例颜色
const rgbExamples = [
  {
    rgb: 'rgb(255, 0, 0)',
    hex: '#FF0000',
    name: '纯红色',
    description: '最大红色通道，绿蓝通道为零'
  },
  {
    rgb: 'rgb(0, 255, 0)',
    hex: '#00FF00',
    name: '纯绿色',
    description: '最大绿色通道，红蓝通道为零'
  },
  {
    rgb: 'rgb(0, 0, 255)',
    hex: '#0000FF',
    name: '纯蓝色',
    description: '最大蓝色通道，红绿通道为零'
  },
  {
    rgb: 'rgb(255, 255, 0)',
    hex: '#FFFF00',
    name: '黄色',
    description: '红绿通道最大，蓝色通道为零'
  },
  {
    rgb: 'rgb(255, 0, 255)',
    hex: '#FF00FF',
    name: '品红色',
    description: '红蓝通道最大，绿色通道为零'
  },
  {
    rgb: 'rgb(0, 255, 255)',
    hex: '#00FFFF',
    name: '青色',
    description: '绿蓝通道最大，红色通道为零'
  }
]

// 计算属性
const rgbValue = computed(() => {
  return `rgb(${redValue.value}, ${greenValue.value}, ${blueValue.value})`
})

const demoColor = computed(() => {
  return rgbValue.value
})

// 方法
const updateColor = () => {
  // 触发颜色变化
  handleColorChange(rgbValue.value)
}

const selectExample = (example) => {
  // 解析 RGB 值
  const match = example.rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
  if (match) {
    redValue.value = parseInt(match[1])
    greenValue.value = parseInt(match[2])
    blueValue.value = parseInt(match[3])
    updateColor()
  }
}

const handleColorChange = (color) => {
  colorStore.parseColor(color)
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // 可以添加成功提示
  } catch (error) {
    console.warn('Failed to copy to clipboard:', error)
  }
}
</script>

<style scoped>
.rgb-wiki {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.format-overview {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
}

.overview-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.demo-code {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.rgb-code {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  background: var(--color-gray-100, #f3f4f6);
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

.copy-btn {
  padding: 0.5rem 1rem;
  background: var(--color-primary, #3b82f6);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.copy-btn:hover {
  background: var(--color-primary-600, #2563eb);
}

.overview-info {
  flex: 1;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.format-description {
  font-size: 1rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.format-specs {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.spec-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.spec-label {
  font-weight: 500;
  color: var(--color-gray-700, #374151);
  min-width: 80px;
}

.spec-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-weight: 600;
  color: var(--color-gray-900, #111827);
}

.channels-demo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  padding: 2rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 12px;
}

.channel-sliders {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.slider-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.slider-label {
  font-weight: 500;
  color: var(--color-gray-700, #374151);
  font-size: 0.875rem;
}

.color-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
}

.red-slider {
  background: linear-gradient(to right, #000000, #ff0000);
}

.green-slider {
  background: linear-gradient(to right, #000000, #00ff00);
}

.blue-slider {
  background: linear-gradient(to right, #000000, #0000ff);
}

.channel-visualization {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.channel-bars {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.channel-bar {
  height: 20px;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.red-bar {
  background: var(--color-red-500, #ef4444);
}

.green-bar {
  background: var(--color-green-500, #10b981);
}

.blue-bar {
  background: var(--color-blue-500, #3b82f6);
}

.channel-swatches {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.example-card {
  padding: 1.5rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.example-card:hover {
  border-color: var(--color-primary, #3b82f6);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.example-info {
  margin-top: 1rem;
}

.example-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.example-description {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.example-values {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.value-rgb,
.value-hex {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.75rem;
  color: var(--color-gray-700, #374151);
}

.explanation-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
}

.explanation-text h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.75rem 0;
}

.explanation-text p {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.application-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.application-list li {
  font-size: 0.875rem;
  color: var(--color-gray-700, #374151);
  padding-left: 1.5rem;
  position: relative;
}

.application-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-primary, #3b82f6);
  font-weight: bold;
}

.color-cube {
  padding: 2rem;
  background: var(--color-blue-50, #eff6ff);
  border-radius: 12px;
}

.cube-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-blue-900, #1e3a8a);
  margin: 0 0 1rem 0;
}

.cube-description {
  font-size: 0.875rem;
  color: var(--color-blue-700, #1d4ed8);
  line-height: 1.5;
}

.cube-description ul {
  margin: 0.75rem 0 0 0;
  padding-left: 1.5rem;
}

.cube-description li {
  margin: 0.25rem 0;
}

.practices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.practice-card {
  padding: 2rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
}

.practice-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.practice-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0 0 1rem 0;
}

.practice-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.practice-list li {
  font-size: 0.875rem;
  color: var(--color-gray-700, #374151);
  line-height: 1.5;
  padding-left: 1.5rem;
  position: relative;
}

.practice-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-gray-400, #9ca3af);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .format-overview {
    flex-direction: column;
  }
  
  .channels-demo {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1.5rem;
  }
  
  .explanation-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .examples-grid {
    grid-template-columns: 1fr;
  }
  
  .practices-grid {
    grid-template-columns: 1fr;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .section-title {
    color: white;
  }
  
  .format-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .rgb-code {
    background: var(--color-gray-700, #374151);
    color: white;
  }
  
  .channels-demo {
    background: var(--color-gray-800, #1f2937);
  }
  
  .example-card,
  .practice-card {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .example-name,
  .practice-title {
    color: white;
  }
  
  .color-cube {
    background: var(--color-blue-900, #1e3a8a);
  }
  
  .cube-title {
    color: var(--color-blue-100, #dbeafe);
  }
  
  .cube-description {
    color: var(--color-blue-200, #bfdbfe);
  }
}
</style>
