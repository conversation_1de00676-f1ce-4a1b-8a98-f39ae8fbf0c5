<!--
  WikiLayout.vue - Wiki 布局组件
  提供 Wiki 页面的主要布局结构
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="wiki-layout">
    <!-- 侧边栏导航 -->
    <aside class="wiki-sidebar">
      <WikiSidebar 
        :formats="colorFormats" 
        :current="currentFormat" 
        @format-change="handleFormatChange"
      />
    </aside>
    
    <!-- 主内容区 -->
    <main class="wiki-content">
      <!-- 页面头部 -->
      <WikiHeader :format="currentFormat" />
      
      <!-- 动态 Wiki 内容组件 -->
      <div class="wiki-body">
        <Suspense>
          <template #default>
            <component 
              :is="currentWikiComponent" 
              :format="currentFormat"
              :color-examples="colorExamples"
            />
          </template>
          <template #fallback>
            <WikiSkeleton />
          </template>
        </Suspense>
      </div>
      
      <!-- 相关格式推荐 -->
      <WikiRelated 
        :current="currentFormat" 
        :formats="relatedFormats" 
      />
    </main>
  </div>
</template>

<script setup>
import { computed, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useColorStore } from '@/stores/colorStore'

// 导入子组件
import WikiSidebar from './WikiSidebar.vue'
import WikiHeader from './WikiHeader.vue'
import WikiRelated from './WikiRelated.vue'
import WikiSkeleton from './WikiSkeleton.vue'

const route = useRoute()
const router = useRouter()
const colorStore = useColorStore()

// 计算属性
const currentFormat = computed(() => route.params.format || 'hex')

// 动态导入 Wiki 组件
const currentWikiComponent = computed(() => {
  return defineAsyncComponent({
    loader: () => import(`./formats/${currentFormat.value}Wiki.vue`),
    errorComponent: defineAsyncComponent(() => import('./formats/defaultWiki.vue')),
    delay: 200,
    timeout: 3000
  })
})

// 支持的颜色格式
const colorFormats = [
  { id: 'hex', name: 'HEX', icon: '#', difficulty: 'beginner' },
  { id: 'rgb', name: 'RGB', icon: 'R', difficulty: 'beginner' },
  { id: 'hsl', name: 'HSL', icon: 'H', difficulty: 'intermediate' },
  { id: 'hsv', name: 'HSV', icon: 'V', difficulty: 'intermediate' },
  { id: 'cmyk', name: 'CMYK', icon: 'C', difficulty: 'advanced' },
  { id: 'oklch', name: 'OKLCH', icon: 'O', difficulty: 'expert' },
  { id: 'lch', name: 'LCH', icon: 'L', difficulty: 'expert' },
  { id: 'xyz', name: 'XYZ', icon: 'X', difficulty: 'expert' },
  { id: 'p3', name: 'Display P3', icon: 'P', difficulty: 'expert' },
  { id: 'rec2020', name: 'Rec2020', icon: '2', difficulty: 'expert' },
  { id: 'keywords', name: 'Keywords', icon: 'K', difficulty: 'beginner' }
]

// 相关格式推荐逻辑
const relatedFormats = computed(() => {
  const current = currentFormat.value
  const relations = {
    'hex': ['rgb', 'hsl'],
    'rgb': ['hex', 'hsl', 'hsv'],
    'hsl': ['rgb', 'hsv', 'oklch'],
    'oklch': ['hsl', 'lch', 'p3'],
    'cmyk': ['rgb', 'hex']
  }
  return relations[current] || []
})

// 颜色示例
const colorExamples = computed(() => {
  // 根据当前格式提供示例颜色
  const examples = {
    'hex': ['#FF6B35', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
    'rgb': ['rgb(255, 107, 53)', 'rgb(78, 205, 196)', 'rgb(69, 183, 209)'],
    'hsl': ['hsl(14, 100%, 61%)', 'hsl(176, 57%, 55%)', 'hsl(197, 62%, 54%)']
  }
  return examples[currentFormat.value] || examples.hex
})

// 方法
const handleFormatChange = (format) => {
  router.push(`/wiki/${format}`)
}
</script>

<style scoped>
.wiki-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
  background: var(--color-gray-50, #f9fafb);
}

.wiki-sidebar {
  background: white;
  border-right: 1px solid var(--color-gray-200, #e5e7eb);
  overflow-y: auto;
  position: sticky;
  top: 0;
  height: 100vh;
}

.wiki-content {
  display: flex;
  flex-direction: column;
  max-width: 100%;
  overflow-x: hidden;
}

.wiki-body {
  flex: 1;
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .wiki-layout {
    grid-template-columns: 240px 1fr;
  }
}

@media (max-width: 768px) {
  .wiki-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }
  
  .wiki-sidebar {
    position: static;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--color-gray-200, #e5e7eb);
  }
  
  .wiki-body {
    padding: 1rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .wiki-layout {
    background: var(--color-gray-900, #111827);
  }
  
  .wiki-sidebar {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
}
</style>
