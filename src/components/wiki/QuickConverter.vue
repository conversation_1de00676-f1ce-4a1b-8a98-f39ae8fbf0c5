<!--
  QuickConverter.vue - 快速转换组件
  在 Wiki 页面中提供内联颜色转换功能
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="quick-converter">
    <div class="converter-input">
      <label class="input-label">输入颜色 ({{ inputFormat.toUpperCase() }})</label>
      <div class="input-group">
        <input
          v-model="inputValue"
          type="text"
          :placeholder="getPlaceholder(inputFormat)"
          class="color-input"
          @input="handleInput"
        />
        <ColorSwatch 
          v-if="isValidColor"
          :color="inputValue" 
          :size="'small'"
          :clickable="false"
        />
      </div>
    </div>
    
    <div class="converter-outputs">
      <div 
        v-for="format in outputFormats" 
        :key="format"
        class="output-item"
      >
        <label class="output-label">{{ format.toUpperCase() }}</label>
        <div class="output-group">
          <input
            :value="convertedValues[format] || ''"
            type="text"
            readonly
            class="output-value"
            :class="{ 'has-value': convertedValues[format] }"
          />
          <button
            v-if="convertedValues[format]"
            @click="copyToClipboard(convertedValues[format])"
            class="copy-button"
            :title="`复制 ${format.toUpperCase()} 值`"
          >
            📋
          </button>
        </div>
      </div>
    </div>
    
    <div v-if="conversionError" class="error-message">
      {{ conversionError }}
    </div>
    
    <div v-if="isValidColor" class="conversion-info">
      <div class="info-item">
        <span class="info-label">颜色温度:</span>
        <span class="info-value">{{ colorTemperature }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">亮度:</span>
        <span class="info-value">{{ luminancePercent }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useColorStore } from '@/stores/colorStore'
import { ColorUtils } from '@/utils/colorUtils'
import ColorSwatch from '@/components/common/ColorSwatch.vue'
import ColorParser from '@/scripts/ColorParser.js'

const props = defineProps({
  inputFormat: {
    type: String,
    required: true,
    validator: (value) => ['hex', 'rgb', 'hsl', 'hsv', 'cmyk'].includes(value)
  },
  outputFormats: {
    type: Array,
    required: true,
    validator: (value) => value.every(format => 
      ['hex', 'rgb', 'hsl', 'hsv', 'cmyk', 'oklch', 'lch'].includes(format)
    )
  },
  initialColor: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['color-change'])

const colorStore = useColorStore()
const inputValue = ref(props.initialColor)
const conversionError = ref('')

// 计算属性
const isValidColor = computed(() => {
  return ColorUtils.isValidColor(inputValue.value)
})

const convertedValues = computed(() => {
  if (!isValidColor.value) return {}
  
  const values = {}
  
  try {
    for (const format of props.outputFormats) {
      values[format] = colorStore.convertToFormat(format)
    }
  } catch (error) {
    console.warn('Conversion failed:', error)
  }
  
  return values
})

const colorTemperature = computed(() => {
  if (!isValidColor.value) return ''
  return ColorUtils.getColorTemperature(inputValue.value)
})

const luminancePercent = computed(() => {
  if (!isValidColor.value) return 0
  return Math.round(ColorUtils.getLuminance(inputValue.value) * 100)
})

// 方法
const getPlaceholder = (format) => {
  const placeholders = {
    hex: '#FF6B35',
    rgb: 'rgb(255, 107, 53)',
    hsl: 'hsl(14, 100%, 61%)',
    hsv: 'hsv(14, 79%, 100%)',
    cmyk: 'cmyk(0%, 58%, 79%, 0%)'
  }
  return placeholders[format] || ''
}

const handleInput = () => {
  conversionError.value = ''
  
  if (!inputValue.value.trim()) {
    return
  }
  
  // 解析颜色
  const parsed = ColorParser.parseEnhanced(inputValue.value, {
    enableCache: true,
    enableSuggestions: true
  })
  
  if (parsed.mode === 'unknown') {
    conversionError.value = parsed.suggestions?.length > 0 
      ? `无效颜色格式。建议: ${parsed.suggestions.join(', ')}`
      : '无效的颜色格式'
    return
  }
  
  // 更新 store 中的当前颜色
  colorStore.parseColor(inputValue.value)
  
  // 触发颜色变化事件
  emit('color-change', inputValue.value)
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    // 可以添加成功提示
  } catch (error) {
    console.warn('Failed to copy to clipboard:', error)
  }
}

// 监听初始颜色变化
watch(() => props.initialColor, (newColor) => {
  if (newColor && newColor !== inputValue.value) {
    inputValue.value = newColor
    handleInput()
  }
}, { immediate: true })
</script>

<style scoped>
.quick-converter {
  padding: 2rem;
  background: white;
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.converter-input {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.input-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-700, #374151);
}

.input-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.color-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 8px;
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.color-input:focus {
  outline: none;
  border-color: var(--color-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.converter-outputs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.output-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.output-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.output-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.output-value {
  flex: 1;
  padding: 0.5rem 0.75rem;
  background: var(--color-gray-50, #f9fafb);
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 6px;
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-size: 0.75rem;
  color: var(--color-gray-600, #6b7280);
  cursor: default;
}

.output-value.has-value {
  color: var(--color-gray-900, #111827);
  background: white;
}

.copy-button {
  padding: 0.5rem;
  background: transparent;
  border: 1px solid var(--color-gray-300, #d1d5db);
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.copy-button:hover {
  background: var(--color-gray-100, #f3f4f6);
  border-color: var(--color-gray-400, #9ca3af);
}

.error-message {
  padding: 0.75rem;
  background: var(--color-red-50, #fef2f2);
  border: 1px solid var(--color-red-200, #fecaca);
  border-radius: 8px;
  color: var(--color-red-700, #b91c1c);
  font-size: 0.875rem;
}

.conversion-info {
  display: flex;
  gap: 2rem;
  padding: 1rem;
  background: var(--color-blue-50, #eff6ff);
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-blue-700, #1d4ed8);
}

.info-value {
  font-size: 0.875rem;
  color: var(--color-blue-900, #1e3a8a);
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quick-converter {
    padding: 1.5rem;
  }
  
  .converter-outputs {
    grid-template-columns: 1fr;
  }
  
  .conversion-info {
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .quick-converter {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .input-label,
  .output-label {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .color-input {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: white;
  }
  
  .color-input:focus {
    border-color: var(--color-primary, #3b82f6);
  }
  
  .output-value {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: var(--color-gray-300, #d1d5db);
  }
  
  .output-value.has-value {
    color: white;
    background: var(--color-gray-600, #4b5563);
  }
  
  .copy-button {
    border-color: var(--color-gray-600, #4b5563);
    color: var(--color-gray-300, #d1d5db);
  }
  
  .copy-button:hover {
    background: var(--color-gray-600, #4b5563);
  }
  
  .error-message {
    background: var(--color-red-900, #7f1d1d);
    border-color: var(--color-red-700, #b91c1c);
    color: var(--color-red-200, #fecaca);
  }
  
  .conversion-info {
    background: var(--color-blue-900, #1e3a8a);
  }
  
  .info-label {
    color: var(--color-blue-300, #93c5fd);
  }
  
  .info-value {
    color: var(--color-blue-100, #dbeafe);
  }
}
</style>
