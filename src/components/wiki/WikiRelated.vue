<!--
  WikiRelated.vue - 相关格式推荐组件
  显示与当前格式相关的其他颜色格式
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <section v-if="relatedFormats.length > 0" class="wiki-related">
    <h2 class="section-title">相关格式</h2>
    <p class="section-description">
      探索与 {{ currentFormatName }} 相关的其他颜色格式
    </p>
    
    <div class="related-grid">
      <router-link
        v-for="format in relatedFormats"
        :key="format.id"
        :to="`/wiki/${format.id}`"
        class="related-card"
      >
        <div class="card-icon" :class="`difficulty-${format.difficulty}`">
          {{ format.icon }}
        </div>
        <div class="card-content">
          <h3 class="card-title">{{ format.name }}</h3>
          <p class="card-description">{{ format.shortDescription }}</p>
          <div class="card-meta">
            <span class="difficulty-badge" :class="`difficulty-${format.difficulty}`">
              {{ getDifficultyText(format.difficulty) }}
            </span>
            <span class="relation-type">{{ getRelationType(format.id) }}</span>
          </div>
        </div>
        <div class="card-arrow">
          →
        </div>
      </router-link>
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  current: {
    type: String,
    required: true
  },
  formats: {
    type: Array,
    required: true
  }
})

// 格式信息配置
const formatConfigs = {
  hex: {
    id: 'hex',
    name: 'HEX',
    icon: '#',
    difficulty: 'beginner',
    shortDescription: '十六进制颜色代码，Web 开发标准'
  },
  rgb: {
    id: 'rgb',
    name: 'RGB',
    icon: 'R',
    difficulty: 'beginner',
    shortDescription: '红绿蓝三原色模型，显示器标准'
  },
  hsl: {
    id: 'hsl',
    name: 'HSL',
    icon: 'H',
    difficulty: 'intermediate',
    shortDescription: '色相饱和度亮度，直观的颜色模型'
  },
  hsv: {
    id: 'hsv',
    name: 'HSV',
    icon: 'V',
    difficulty: 'intermediate',
    shortDescription: '色相饱和度明度，颜色选择器常用'
  },
  cmyk: {
    id: 'cmyk',
    name: 'CMYK',
    icon: 'C',
    difficulty: 'advanced',
    shortDescription: '青品黄黑四色，印刷行业标准'
  },
  oklch: {
    id: 'oklch',
    name: 'OKLCH',
    icon: 'O',
    difficulty: 'expert',
    shortDescription: '现代感知均匀色彩空间'
  },
  lch: {
    id: 'lch',
    name: 'LCH',
    icon: 'L',
    difficulty: 'expert',
    shortDescription: 'Lab 色彩空间柱坐标表示'
  },
  xyz: {
    id: 'xyz',
    name: 'XYZ',
    icon: 'X',
    difficulty: 'expert',
    shortDescription: 'CIE XYZ 国际标准色彩空间'
  },
  p3: {
    id: 'p3',
    name: 'Display P3',
    icon: 'P',
    difficulty: 'expert',
    shortDescription: '广色域显示标准'
  },
  rec2020: {
    id: 'rec2020',
    name: 'Rec2020',
    icon: '2',
    difficulty: 'expert',
    shortDescription: '超高清电视色彩标准'
  },
  keywords: {
    id: 'keywords',
    name: 'Keywords',
    icon: 'K',
    difficulty: 'beginner',
    shortDescription: 'CSS 预定义颜色关键字'
  }
}

// 关系类型配置
const relationTypes = {
  hex: {
    rgb: '直接转换',
    hsl: '常用组合'
  },
  rgb: {
    hex: '直接转换',
    hsl: '色彩空间',
    hsv: '相似模型'
  },
  hsl: {
    rgb: '色彩空间',
    hsv: '相似模型',
    oklch: '现代替代'
  },
  oklch: {
    hsl: '传统对比',
    lch: '相同基础',
    p3: '现代组合'
  },
  cmyk: {
    rgb: '显示转换',
    hex: 'Web 应用'
  }
}

// 计算属性
const currentFormatName = computed(() => {
  const config = formatConfigs[props.current]
  return config ? config.name : props.current.toUpperCase()
})

const relatedFormats = computed(() => {
  return props.formats.map(formatId => formatConfigs[formatId]).filter(Boolean)
})

// 方法
const getDifficultyText = (difficulty) => {
  const texts = {
    beginner: '入门',
    intermediate: '中级',
    advanced: '高级',
    expert: '专家'
  }
  return texts[difficulty] || '未知'
}

const getRelationType = (formatId) => {
  const relations = relationTypes[props.current]
  return relations?.[formatId] || '相关格式'
}
</script>

<style scoped>
.wiki-related {
  margin-top: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--color-gray-200, #e5e7eb);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0 0 0.5rem 0;
}

.section-description {
  color: var(--color-gray-600, #6b7280);
  margin: 0 0 2rem 0;
  font-size: 1rem;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.related-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--color-gray-50, #f9fafb);
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 12px;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease;
}

.related-card:hover {
  background: var(--color-gray-100, #f3f4f6);
  border-color: var(--color-primary, #3b82f6);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  flex-shrink: 0;
}

.card-icon.difficulty-beginner {
  background: var(--color-green-500, #10b981);
}

.card-icon.difficulty-intermediate {
  background: var(--color-yellow-500, #f59e0b);
}

.card-icon.difficulty-advanced {
  background: var(--color-orange-500, #f97316);
}

.card-icon.difficulty-expert {
  background: var(--color-red-500, #ef4444);
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-900, #111827);
  margin: 0;
}

.card-description {
  font-size: 0.875rem;
  color: var(--color-gray-600, #6b7280);
  margin: 0;
  line-height: 1.4;
}

.card-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.25rem;
}

.difficulty-badge {
  display: inline-block;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.difficulty-badge.difficulty-beginner {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-700, #15803d);
}

.difficulty-badge.difficulty-intermediate {
  background: var(--color-yellow-100, #fef3c7);
  color: var(--color-yellow-700, #a16207);
}

.difficulty-badge.difficulty-advanced {
  background: var(--color-orange-100, #ffedd5);
  color: var(--color-orange-700, #c2410c);
}

.difficulty-badge.difficulty-expert {
  background: var(--color-red-100, #fee2e2);
  color: var(--color-red-700, #b91c1c);
}

.relation-type {
  font-size: 0.75rem;
  color: var(--color-gray-500, #6b7280);
  font-weight: 500;
}

.card-arrow {
  font-size: 1.25rem;
  color: var(--color-gray-400, #9ca3af);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.related-card:hover .card-arrow {
  color: var(--color-primary, #3b82f6);
  transform: translateX(4px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wiki-related {
    margin-top: 2rem;
    padding: 1.5rem;
  }
  
  .related-grid {
    grid-template-columns: 1fr;
  }
  
  .related-card {
    padding: 1rem;
  }
  
  .card-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .wiki-related {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .section-title {
    color: white;
  }
  
  .section-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .related-card {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
  }
  
  .related-card:hover {
    background: var(--color-gray-600, #4b5563);
  }
  
  .card-title {
    color: white;
  }
  
  .card-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .relation-type {
    color: var(--color-gray-400, #9ca3af);
  }
}
</style>
