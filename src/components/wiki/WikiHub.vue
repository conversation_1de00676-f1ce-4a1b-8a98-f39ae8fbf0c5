<!--
  WikiHub.vue - Wiki 中心页面
  提供颜色格式知识库的入口和导航
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="wiki-hub">
    <!-- 页面头部 -->
    <header class="hub-header">
      <h1 class="hub-title">颜色格式知识库</h1>
      <p class="hub-description">
        深入了解各种颜色格式的原理、应用场景和最佳实践
      </p>
    </header>

    <!-- 搜索功能 -->
    <div class="search-section">
      <div class="search-container">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索颜色格式..."
          class="search-input"
          @input="handleSearch"
        />
        <SearchIcon class="search-icon" />
      </div>
    </div>

    <!-- 格式分类 -->
    <div class="format-categories">
      <div class="category-tabs">
        <button
          v-for="category in categories"
          :key="category.id"
          @click="selectedCategory = category.id"
          class="category-tab"
          :class="{ active: selectedCategory === category.id }"
        >
          {{ category.name }}
        </button>
      </div>
    </div>

    <!-- 格式列表 -->
    <div class="formats-grid">
      <div
        v-for="format in filteredFormats"
        :key="format.id"
        @click="navigateToFormat(format.id)"
        class="format-card"
        :class="{ popular: format.popular }"
      >
        <div class="format-icon">
          <component :is="format.icon" class="icon" />
        </div>
        <div class="format-info">
          <h3 class="format-name">{{ format.name }}</h3>
          <p class="format-description">{{ format.description }}</p>
          <div class="format-meta">
            <span class="difficulty" :class="format.difficulty">
              {{ getDifficultyText(format.difficulty) }}
            </span>
            <span class="usage">{{ format.usage }}</span>
          </div>
        </div>
        <div v-if="format.popular" class="popular-badge">
          热门
        </div>
      </div>
    </div>

    <!-- 快速开始 -->
    <div class="quick-start">
      <h2 class="section-title">快速开始</h2>
      <div class="quick-links">
        <router-link
          v-for="link in quickLinks"
          :key="link.id"
          :to="link.path"
          class="quick-link"
        >
          <component :is="link.icon" class="link-icon" />
          <span class="link-text">{{ link.text }}</span>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { SearchIcon } from '@/components/icons.js'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('all')

// 格式分类
const categories = [
  { id: 'all', name: '全部' },
  { id: 'basic', name: '基础格式' },
  { id: 'advanced', name: '高级格式' },
  { id: 'print', name: '印刷格式' },
  { id: 'web', name: 'Web 格式' }
]

// 颜色格式数据
const formats = [
  {
    id: 'hex',
    name: 'HEX',
    description: '十六进制颜色表示法，Web 开发最常用的格式',
    category: 'basic',
    difficulty: 'easy',
    usage: 'Web 开发',
    popular: true,
    icon: 'HashIcon'
  },
  {
    id: 'rgb',
    name: 'RGB',
    description: '红绿蓝加色模式，显示器和数字设备的标准格式',
    category: 'basic',
    difficulty: 'easy',
    usage: '显示设备',
    popular: true,
    icon: 'SwatchIcon'
  },
  {
    id: 'hsl',
    name: 'HSL',
    description: '色相饱和度亮度，更符合人类对颜色的感知',
    category: 'basic',
    difficulty: 'medium',
    usage: '设计工具',
    popular: true,
    icon: 'AdjustmentsIcon'
  },
  {
    id: 'hsv',
    name: 'HSV',
    description: '色相饱和度明度，常用于颜色选择器',
    category: 'advanced',
    difficulty: 'medium',
    usage: '颜色选择',
    popular: false,
    icon: 'ColorSwatchIcon'
  },
  {
    id: 'cmyk',
    name: 'CMYK',
    description: '青品黄黑减色模式，印刷行业标准格式',
    category: 'print',
    difficulty: 'hard',
    usage: '印刷设计',
    popular: false,
    icon: 'PrinterIcon'
  },
  {
    id: 'lab',
    name: 'LAB',
    description: '设备无关的颜色空间，专业色彩管理',
    category: 'advanced',
    difficulty: 'hard',
    usage: '色彩管理',
    popular: false,
    icon: 'BeakerIcon'
  },
  {
    id: 'oklch',
    name: 'OKLCH',
    description: '现代感知均匀色彩空间，CSS 新标准',
    category: 'web',
    difficulty: 'hard',
    usage: '现代 Web',
    popular: false,
    icon: 'SparklesIcon'
  }
]

// 快速链接
const quickLinks = [
  {
    id: 'converter',
    text: '颜色转换器',
    path: '/converter',
    icon: 'ArrowsRightLeftIcon'
  },
  {
    id: 'palette',
    text: '配色方案',
    path: '/palette',
    icon: 'PaintBrushIcon'
  },
  {
    id: 'tools',
    text: '设计工具',
    path: '/tools',
    icon: 'WrenchScrewdriverIcon'
  }
]

// 计算属性
const filteredFormats = computed(() => {
  let filtered = formats

  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(format => format.category === selectedCategory.value)
  }

  // 按搜索词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(format =>
      format.name.toLowerCase().includes(query) ||
      format.description.toLowerCase().includes(query) ||
      format.usage.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const navigateToFormat = (formatId) => {
  router.push(`/wiki/${formatId}`)
}

const getDifficultyText = (difficulty) => {
  const texts = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return texts[difficulty] || '未知'
}

// 生命周期
onMounted(() => {
  // 页面加载完成后的初始化
})
</script>

<style scoped>
.wiki-hub {
  max-width: var(--layout-max-width);
  margin: 0 auto;
  padding: var(--spacing-8);
}

.hub-header {
  text-align: center;
  margin-bottom: var(--spacing-12);
}

.hub-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-4) 0;
  font-family: var(--font-family-primary);
}

.hub-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.search-section {
  margin-bottom: var(--spacing-8);
}

.search-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-4) var(--spacing-12);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-family: var(--font-family-primary);
  transition: var(--transition-medium);
  background: var(--color-bg-card);
}

.search-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  left: var(--spacing-4);
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: var(--color-text-tertiary);
}

.format-categories {
  margin-bottom: var(--spacing-8);
}

.category-tabs {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.category-tab {
  padding: var(--spacing-3) var(--spacing-6);
  background: transparent;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: var(--transition-medium);
  font-family: var(--font-family-primary);
}

.category-tab:hover {
  background: var(--color-gray-50);
  border-color: var(--color-border-dark);
}

.category-tab.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
}

.formats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-12);
}

.format-card {
  position: relative;
  padding: var(--spacing-8);
  background: var(--color-bg-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-medium);
  box-shadow: var(--shadow-md);
}

.format-card:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.format-card.popular {
  border-color: var(--color-secondary-orange);
  background: linear-gradient(135deg, #fef3c7 0%, #ffffff 100%);
}

.format-icon {
  width: 3rem;
  height: 3rem;
  background: var(--color-gray-100);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-4);
}

.icon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--color-primary);
}

.format-info {
  flex: 1;
}

.format-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-2) 0;
  font-family: var(--font-family-primary);
}

.format-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--spacing-4) 0;
}

.format-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.difficulty {
  padding: var(--spacing-badge-padding);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.difficulty.easy {
  background: #dcfce7;
  color: #166534;
}

.difficulty.medium {
  background: #fef3c7;
  color: #92400e;
}

.difficulty.hard {
  background: #fee2e2;
  color: #b91c1c;
}

.usage {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

.popular-badge {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  padding: var(--spacing-badge-padding);
  background: var(--color-secondary-orange);
  color: var(--color-white);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
}

.quick-start {
  text-align: center;
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-6) 0;
  font-family: var(--font-family-primary);
}

.quick-links {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.quick-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4) var(--spacing-6);
  background: var(--color-gray-50);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-medium);
  font-family: var(--font-family-primary);
}

.quick-link:hover {
  background: #eff6ff;
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.link-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* 响应式设计 - 基于设计系统断点 */
@media (max-width: 768px) {
  .wiki-hub {
    padding: var(--spacing-4);
  }

  .hub-title {
    font-size: var(--font-size-3xl);
  }

  .formats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }

  .format-card {
    padding: var(--spacing-6);
  }

  .quick-links {
    flex-direction: column;
    align-items: center;
  }

  .category-tabs {
    gap: var(--spacing-1);
  }

  .category-tab {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .hub-title {
    font-size: var(--font-size-2xl);
  }

  .search-container {
    max-width: 100%;
  }

  .category-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: var(--spacing-2);
  }
}
</style>
