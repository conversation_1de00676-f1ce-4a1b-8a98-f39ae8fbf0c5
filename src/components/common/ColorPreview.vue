<!--
  ColorPreview.vue - 颜色预览组件
  提供更详细的颜色预览，包括文本对比度演示
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="color-preview" :class="sizeClass">
    <div 
      class="preview-container"
      :style="{ backgroundColor: color }"
    >
      <!-- 文本对比度演示 -->
      <div class="text-demo" v-if="showTextDemo">
        <div 
          class="demo-text light-text"
          :style="{ color: '#ffffff' }"
        >
          Aa
        </div>
        <div 
          class="demo-text dark-text"
          :style="{ color: '#000000' }"
        >
          Aa
        </div>
      </div>
      
      <!-- 颜色信息覆盖层 -->
      <div v-if="showInfo" class="color-info" :style="{ color: textColor }">
        <div class="color-value">{{ color }}</div>
        <div v-if="showLuminance" class="luminance-info">
          亮度: {{ luminancePercent }}%
        </div>
      </div>
    </div>
    
    <!-- 对比度信息 -->
    <div v-if="showContrast" class="contrast-info">
      <div class="contrast-item">
        <span class="contrast-label">白色文本:</span>
        <span class="contrast-ratio" :class="whiteContrastClass">
          {{ whiteContrast.ratio.toFixed(2) }}:1
        </span>
        <span class="contrast-level">{{ whiteContrast.level }}</span>
      </div>
      <div class="contrast-item">
        <span class="contrast-label">黑色文本:</span>
        <span class="contrast-ratio" :class="blackContrastClass">
          {{ blackContrast.ratio.toFixed(2) }}:1
        </span>
        <span class="contrast-level">{{ blackContrast.level }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ColorUtils } from '@/utils/colorUtils'

const props = defineProps({
  // 颜色值
  color: {
    type: String,
    required: true,
    validator: (value) => ColorUtils.isValidColor(value)
  },
  
  // 尺寸
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  
  // 是否显示文本演示
  showTextDemo: {
    type: Boolean,
    default: true
  },
  
  // 是否显示颜色信息
  showInfo: {
    type: Boolean,
    default: true
  },
  
  // 是否显示亮度信息
  showLuminance: {
    type: Boolean,
    default: false
  },
  
  // 是否显示对比度信息
  showContrast: {
    type: Boolean,
    default: false
  }
})

// 计算属性
const sizeClass = computed(() => `size-${props.size}`)

const luminance = computed(() => {
  return ColorUtils.getLuminance(props.color)
})

const luminancePercent = computed(() => {
  return Math.round(luminance.value * 100)
})

const textColor = computed(() => {
  return ColorUtils.getBestTextColor(props.color)
})

const whiteContrast = computed(() => {
  return ColorUtils.checkWCAGContrast('#ffffff', props.color)
})

const blackContrast = computed(() => {
  return ColorUtils.checkWCAGContrast('#000000', props.color)
})

const whiteContrastClass = computed(() => ({
  'contrast-excellent': whiteContrast.value.aaa,
  'contrast-good': whiteContrast.value.aa && !whiteContrast.value.aaa,
  'contrast-poor': !whiteContrast.value.aa
}))

const blackContrastClass = computed(() => ({
  'contrast-excellent': blackContrast.value.aaa,
  'contrast-good': blackContrast.value.aa && !blackContrast.value.aaa,
  'contrast-poor': !blackContrast.value.aa
}))
</script>

<style scoped>
.color-preview {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.preview-container {
  position: relative;
  border-radius: 12px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 尺寸变体 */
.size-small .preview-container {
  height: 4rem;
  border-radius: 8px;
}

.size-medium .preview-container {
  height: 6rem;
}

.size-large .preview-container {
  height: 8rem;
  border-radius: 16px;
}

.text-demo {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
}

.demo-text {
  font-size: 2rem;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.size-small .demo-text {
  font-size: 1.5rem;
}

.size-large .demo-text {
  font-size: 2.5rem;
}

.color-info {
  position: absolute;
  bottom: 0.5rem;
  left: 0.5rem;
  right: 0.5rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.color-value {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-weight: 600;
}

.luminance-info {
  font-size: 0.75rem;
  opacity: 0.9;
  margin-top: 0.25rem;
}

.contrast-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--color-gray-50, #f9fafb);
  border-radius: 8px;
  border: 1px solid var(--color-gray-200, #e5e7eb);
}

.contrast-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.contrast-label {
  flex: 1;
  color: var(--color-gray-600, #6b7280);
}

.contrast-ratio {
  font-family: var(--font-family-mono, 'JetBrains Mono', monospace);
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.contrast-ratio.contrast-excellent {
  background: var(--color-green-100, #dcfce7);
  color: var(--color-green-800, #166534);
}

.contrast-ratio.contrast-good {
  background: var(--color-yellow-100, #fef3c7);
  color: var(--color-yellow-800, #92400e);
}

.contrast-ratio.contrast-poor {
  background: var(--color-red-100, #fee2e2);
  color: var(--color-red-800, #991b1b);
}

.contrast-level {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-gray-500, #6b7280);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .preview-container {
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .contrast-info {
    background: var(--color-gray-800, #1f2937);
    border-color: var(--color-gray-700, #374151);
  }
  
  .contrast-label {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .contrast-level {
    color: var(--color-gray-400, #9ca3af);
  }
}
</style>
