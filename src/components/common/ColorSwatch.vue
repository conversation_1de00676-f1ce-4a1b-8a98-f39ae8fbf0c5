<!--
  ColorSwatch.vue - 颜色色块组件
  显示颜色预览，支持点击复制和无障碍功能
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div 
    class="color-swatch"
    :class="{ 'clickable': clickable, 'copied': showCopied }"
    @click="handleClick"
    :title="title"
    :aria-label="ariaLabel"
    role="button"
    :tabindex="clickable ? 0 : -1"
    @keydown.enter="handleClick"
    @keydown.space.prevent="handleClick"
  >
    <div 
      class="swatch-color"
      :style="{ backgroundColor: color }"
    >
      <div v-if="showCopied" class="copy-indicator">
        ✓ 已复制
      </div>
    </div>
    
    <div v-if="showLabel" class="swatch-label">
      {{ displayLabel }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ColorUtils } from '@/utils/colorUtils'

const props = defineProps({
  // 颜色值
  color: {
    type: String,
    required: true,
    validator: (value) => ColorUtils.isValidColor(value)
  },
  
  // 显示标签
  label: {
    type: String,
    default: ''
  },
  
  // 是否显示标签
  showLabel: {
    type: Boolean,
    default: false
  },
  
  // 是否可点击
  clickable: {
    type: Boolean,
    default: true
  },
  
  // 尺寸
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  
  // 自定义标题
  title: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['click', 'copy'])

// 响应式状态
const showCopied = ref(false)

// 计算属性
const displayLabel = computed(() => {
  return props.label || props.color
})

const ariaLabel = computed(() => {
  return `颜色 ${props.color}${props.clickable ? '，点击复制' : ''}`
})

const computedTitle = computed(() => {
  if (props.title) return props.title
  return props.clickable ? `点击复制 ${props.color}` : props.color
})

// 方法
const handleClick = async () => {
  if (!props.clickable) return
  
  emit('click', props.color)
  
  try {
    await navigator.clipboard.writeText(props.color)
    showCopied.value = true
    emit('copy', props.color)
    
    // 2秒后隐藏复制提示
    setTimeout(() => {
      showCopied.value = false
    }, 2000)
  } catch (error) {
    console.warn('Failed to copy color to clipboard:', error)
  }
}

// 监听颜色变化，重置复制状态
watch(() => props.color, () => {
  showCopied.value = false
})
</script>

<style scoped>
.color-swatch {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.color-swatch.clickable {
  cursor: pointer;
}

.color-swatch.clickable:hover {
  transform: translateY(-2px);
}

.color-swatch.clickable:focus {
  outline: 2px solid var(--color-primary, #3b82f6);
  outline-offset: 2px;
  border-radius: 4px;
}

.swatch-color {
  position: relative;
  width: 3rem;
  height: 3rem;
  border-radius: 8px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: box-shadow 0.2s ease;
}

.color-swatch.clickable .swatch-color:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 尺寸变体 */
.color-swatch.size-small .swatch-color {
  width: 2rem;
  height: 2rem;
}

.color-swatch.size-large .swatch-color {
  width: 4rem;
  height: 4rem;
}

.copy-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  animation: fadeInOut 2s ease-in-out;
}

.swatch-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text, #374151);
  text-align: center;
  max-width: 6rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.color-swatch.copied {
  animation: pulse 0.3s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .swatch-color {
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .swatch-label {
    color: var(--color-text-dark, #f3f4f6);
  }
}
</style>
