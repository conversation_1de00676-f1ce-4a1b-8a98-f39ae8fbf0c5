<!--
  NotFound.vue - 404 页面组件
  当用户访问不存在的页面时显示
  
  <AUTHOR> Team
  @version 2.0.0
-->

<template>
  <div class="not-found">
    <div class="not-found-container">
      <!-- 404 图标 -->
      <div class="error-icon">
        <div class="icon-404">404</div>
        <div class="icon-decoration">
          <div class="color-dot" style="background: #ff6b35;"></div>
          <div class="color-dot" style="background: #4ecdc4;"></div>
          <div class="color-dot" style="background: #45b7d1;"></div>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div class="error-content">
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移动。
          <br>
          请检查 URL 是否正确，或返回首页继续浏览。
        </p>
        
        <!-- 建议链接 -->
        <div class="suggestions">
          <h3 class="suggestions-title">您可能想要访问：</h3>
          <div class="suggestion-links">
            <router-link to="/" class="suggestion-link">
              <span class="link-icon">🏠</span>
              <span class="link-text">返回首页</span>
            </router-link>
            
            <router-link to="/wiki" class="suggestion-link">
              <span class="link-icon">📚</span>
              <span class="link-text">颜色格式 Wiki</span>
            </router-link>
            
            <router-link to="/converter" class="suggestion-link">
              <span class="link-icon">🔄</span>
              <span class="link-text">颜色转换工具</span>
            </router-link>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button @click="goBack" class="action-button secondary">
            ← 返回上页
          </button>
          <router-link to="/" class="action-button primary">
            回到首页
          </router-link>
        </div>
      </div>
    </div>
    
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-color" style="background: #ff6b35; top: 10%; left: 10%;"></div>
      <div class="floating-color" style="background: #4ecdc4; top: 20%; right: 15%;"></div>
      <div class="floating-color" style="background: #45b7d1; bottom: 30%; left: 20%;"></div>
      <div class="floating-color" style="background: #96ceb4; bottom: 20%; right: 10%;"></div>
      <div class="floating-color" style="background: #ffeaa7; top: 60%; left: 50%;"></div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.not-found-container {
  max-width: 600px;
  padding: 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

.error-icon {
  position: relative;
  margin-bottom: 2rem;
}

.icon-404 {
  font-size: 6rem;
  font-weight: 900;
  color: var(--color-gray-800, #1f2937);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.icon-decoration {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.color-dot {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  animation: bounce 2s infinite;
}

.color-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.color-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.error-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-gray-900, #111827);
  margin: 0;
}

.error-description {
  font-size: 1.125rem;
  color: var(--color-gray-600, #6b7280);
  line-height: 1.6;
  margin: 0;
}

.suggestions {
  text-align: left;
}

.suggestions-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-800, #1f2937);
  margin: 0 0 1rem 0;
}

.suggestion-links {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.suggestion-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: var(--color-gray-50, #f9fafb);
  border: 1px solid var(--color-gray-200, #e5e7eb);
  border-radius: 8px;
  text-decoration: none;
  color: var(--color-gray-700, #374151);
  transition: all 0.2s ease;
}

.suggestion-link:hover {
  background: var(--color-primary-50, #eff6ff);
  border-color: var(--color-primary-200, #bfdbfe);
  color: var(--color-primary-700, #1d4ed8);
  transform: translateX(4px);
}

.link-icon {
  font-size: 1.25rem;
}

.link-text {
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.action-button.primary {
  background: var(--color-primary, #3b82f6);
  color: white;
}

.action-button.primary:hover {
  background: var(--color-primary-600, #2563eb);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-button.secondary {
  background: var(--color-gray-100, #f3f4f6);
  color: var(--color-gray-700, #374151);
  border: 1px solid var(--color-gray-300, #d1d5db);
}

.action-button.secondary:hover {
  background: var(--color-gray-200, #e5e7eb);
  transform: translateY(-2px);
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-color {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.floating-color:nth-child(2) {
  animation-delay: 1s;
  animation-duration: 8s;
}

.floating-color:nth-child(3) {
  animation-delay: 2s;
  animation-duration: 7s;
}

.floating-color:nth-child(4) {
  animation-delay: 3s;
  animation-duration: 9s;
}

.floating-color:nth-child(5) {
  animation-delay: 4s;
  animation-duration: 5s;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-container {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .icon-404 {
    font-size: 4rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-description {
    font-size: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-button {
    width: 100%;
    max-width: 200px;
  }
  
  .floating-color {
    width: 40px;
    height: 40px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .not-found-container {
    background: rgba(31, 41, 55, 0.95);
  }
  
  .icon-404,
  .error-title,
  .suggestions-title {
    color: white;
  }
  
  .error-description {
    color: var(--color-gray-300, #d1d5db);
  }
  
  .suggestion-link {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: var(--color-gray-200, #e5e7eb);
  }
  
  .suggestion-link:hover {
    background: var(--color-gray-600, #4b5563);
  }
  
  .action-button.secondary {
    background: var(--color-gray-700, #374151);
    border-color: var(--color-gray-600, #4b5563);
    color: var(--color-gray-200, #e5e7eb);
  }
  
  .action-button.secondary:hover {
    background: var(--color-gray-600, #4b5563);
  }
}
</style>
