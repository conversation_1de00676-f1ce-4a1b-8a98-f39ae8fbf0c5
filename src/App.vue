<template>
  <div id="app">
    <!-- Vue Router 视图容器 -->
    <router-view />
  </div>
</template>

<script setup>
/**
 * ColorCode.cc Main Application
 * 基于 Vue 3.x + Vite 7.x + JavaScript ES6+ 技术栈
 * 
 * 应用特性：
 * - 专业级颜色工具平台
 * - 响应式设计
 * - 现代化用户体验
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { onMounted } from 'vue'

// ============================================================================
// 应用初始化
// ============================================================================

/**
 * 应用挂载后的初始化操作
 */
onMounted(() => {
  // 设置页面标题和元信息
  document.title = 'ColorCode.cc - 专业级在线颜色工具平台'
  
  // 设置 meta 描述
  const metaDescription = document.querySelector('meta[name="description"]')
  if (metaDescription) {
    metaDescription.setAttribute('content', 
      '专业级在线颜色工具平台，提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务。支持 OKLCH、LAB、CMYK 等专业格式，ΔE≤0.5 精度保障。'
    )
  }
  
  // 设置 meta 关键词
  const metaKeywords = document.querySelector('meta[name="keywords"]')
  if (metaKeywords) {
    metaKeywords.setAttribute('content',
      'ColorCode.cc,颜色工具,色彩转换,OKLCH,LAB,CMYK,无障碍设计,CSS变量生成,Vue3,Vite'
    )
  }
  
  // 添加结构化数据（SEO 优化）
  addStructuredData()
  
  // 初始化性能监控
  initPerformanceMonitoring()
  

})

/**
 * 添加结构化数据，提升 SEO 效果
 */
function addStructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "ColorCode.cc",
    "description": "专业级在线颜色工具平台",
    "url": "https://colorcode.cc",
    "applicationCategory": "DesignApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "CNY",
      "availability": "https://schema.org/InStock"
    },
    "creator": {
      "@type": "Organization",
      "name": "ColorCode.cc Team"
    },
    "featureList": [
      "高精度颜色转换（ΔE≤0.5）",
      "OKLCH 标准支持",
      "智能无障碍检测",
      "CSS 变量生成器"
    ]
  }
  
  const script = document.createElement('script')
  script.type = 'application/ld+json'
  script.textContent = JSON.stringify(structuredData)
  document.head.appendChild(script)
}

/**
 * 初始化性能监控
 * 监控页面加载性能和用户体验指标
 */
function initPerformanceMonitoring() {
  // 监控 Core Web Vitals
  if ('web-vital' in window) {
    // 实际项目中会集成 web-vitals 库
    console.log('Performance monitoring initialized')
  }
  
  // 监控页面加载时间
  window.addEventListener('load', () => {
    const loadTime = performance.now()
    console.log(`Page loaded in ${loadTime.toFixed(2)}ms`)
    
    // 发送性能数据到分析服务
    if (typeof gtag !== 'undefined') {
      gtag('event', 'page_load_time', {
        value: Math.round(loadTime),
        custom_parameter: 'landing_page'
      })
    }
  })
  
  // 监控资源加载错误
  window.addEventListener('error', (event) => {
    console.error('Resource loading error:', event.filename, event.message)
    
    // 发送错误报告
    if (typeof gtag !== 'undefined') {
      gtag('event', 'exception', {
        description: event.message,
        fatal: false
      })
    }
  })
}





// ============================================================================
// 全局错误处理
// ============================================================================

/**
 * 全局错误处理器
 */
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
  
  // 发送错误报告到监控服务
  if (typeof gtag !== 'undefined') {
    gtag('event', 'exception', {
      description: event.reason?.message || 'Unhandled promise rejection',
      fatal: false
    })
  }
  
  // 阻止默认的错误处理
  event.preventDefault()
})
</script>

<style>
/**
 * 全局样式
 * 导入 Landing Page 样式和基础重置样式
 */

/* 导入 Landing Page 样式 */
@import './styles/landing-page.css';

/* 全局字体导入 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* 应用根容器 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 确保页面内容占满视口 */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 优化滚动条样式（Webkit 浏览器） */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-400);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

/* 选择文本的颜色 */
::selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-700);
}

::-moz-selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-700);
}

/* 焦点样式优化 */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 图片优化 */
img {
  max-width: 100%;
  height: auto;
}

/* 链接样式重置 */
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 按钮样式重置 */
button {
  font-family: inherit;
  font-size: inherit;
}

/* 输入框样式优化 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

/* 表格样式优化 */
table {
  border-collapse: collapse;
  width: 100%;
}

/* 代码块样式 */
code, pre {
  font-family: var(--font-family-mono);
  background-color: var(--color-gray-100);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  font-size: 0.875em;
}

pre {
  padding: 1rem;
  overflow-x: auto;
  border-radius: var(--radius-lg);
}

/* 打印样式优化 */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .hero-section,
  .cta-section {
    background: white !important;
  }
  
  .btn {
    border: 1px solid black !important;
  }
}
</style>
