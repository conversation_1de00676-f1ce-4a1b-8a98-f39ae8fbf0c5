/**
 * Vue Router Configuration
 * 路由配置 - 支持 Wiki 知识库和转换工具模块
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

import { createRouter, createWebHistory } from 'vue-router'
import { useColorStore } from '@/stores/colorStore'

// 路由组件懒加载
const LandingPage = () => import('@/components/LandingPage.vue')
const WikiHub = () => import('@/components/wiki/WikiHub.vue')
const WikiLayout = () => import('@/components/wiki/WikiLayout.vue')
const ConverterHub = () => import('@/components/converter/ConverterHub.vue')
const ConverterLayout = () => import('@/components/converter/ConverterLayout.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: LandingPage,
    meta: {
      title: 'ColorCode.cc - 专业级颜色工具平台',
      description: '提供高精度颜色转换、智能配色方案生成和色彩空间可视化服务'
    }
  },
  
  // Wiki 知识库路由
  {
    path: '/wiki',
    name: 'Wiki',
    component: WikiHub,
    meta: {
      title: 'ColorWiki - 颜色格式知识库',
      description: '专业的颜色格式技术文档和应用指南'
    },
    children: [
      {
        path: '',
        name: 'WikiIndex',
        component: () => import('@/components/wiki/WikiIndex.vue')
      },
      {
        path: ':format',
        name: 'WikiFormat',
        component: WikiLayout,
        props: true,
        meta: {
          title: (route) => `${route.params.format.toUpperCase()} 颜色格式 - ColorWiki`,
          description: (route) => `${route.params.format.toUpperCase()} 颜色格式的技术原理、应用场景和最佳实践`
        },
        beforeEnter: (to, from, next) => {
          const validFormats = [
            'hex', 'rgb', 'hsl', 'hsv', 'cmyk',
            'oklch', 'lch', 'xyz', 'p3', 'rec2020', 'keywords'
          ]

          if (validFormats.includes(to.params.format)) {
            // 记录访问历史（仅在非测试环境）
            if (typeof window !== 'undefined' && !window.__VITEST__) {
              try {
                const colorStore = useColorStore()
                colorStore.addRecentlyViewed(to.params.format)
              } catch (error) {
                // 忽略 store 错误，继续导航
              }
            }
            next()
          } else {
            next({ path: '/wiki' })
          }
        }
      }
    ]
  },
  
  // 转换工具路由
  {
    path: '/converter',
    name: 'Converter',
    component: ConverterHub,
    meta: {
      title: '专业颜色转换工具 - ColorCode.cc',
      description: '提供高精度的颜色格式转换工具，支持实时预览和代码导出'
    },
    children: [
      {
        path: '',
        name: 'ConverterIndex',
        component: () => import('@/components/converter/ConverterIndex.vue')
      },
      {
        path: ':type',
        name: 'ConverterType',
        component: ConverterLayout,
        props: true,
        meta: {
          title: (route) => `${route.params.type.toUpperCase()} 转换器 - ColorCode.cc`,
          description: (route) => `专业的 ${route.params.type.replace('-', ' ↔ ').toUpperCase()} 颜色转换工具`
        },
        beforeEnter: (to, from, next) => {
          const validConverters = [
            'hex-rgb', 'rgb-hsl', 'hsl-hsv', 'cmyk-rgb',
            'oklch-hsl', 'lch-lab', 'xyz-rgb', 'p3-rgb'
          ]

          if (validConverters.includes(to.params.type)) {
            next()
          } else {
            next({ path: '/converter' })
          }
        }
      }
    ]
  },
  
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/components/common/NotFound.vue'),
    meta: {
      title: '页面未找到 - ColorCode.cc',
      description: '抱歉，您访问的页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 动态设置页面标题
  if (to.meta.title) {
    const title = typeof to.meta.title === 'function' 
      ? to.meta.title(to) 
      : to.meta.title
    document.title = title
  }
  
  // 设置 meta description
  if (to.meta.description) {
    const description = typeof to.meta.description === 'function'
      ? to.meta.description(to)
      : to.meta.description
    
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.name = 'description'
      document.head.appendChild(metaDescription)
    }
    metaDescription.content = description
  }
  
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('Router error:', error)
  
  // 发送错误报告
  if (typeof gtag !== 'undefined') {
    gtag('event', 'exception', {
      description: `Router error: ${error.message}`,
      fatal: false
    })
  }
})

export default router
