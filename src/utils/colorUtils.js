/**
 * ColorUtils - 扩展的颜色工具函数库
 * 提供高级颜色操作、转换和分析功能
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

import chroma from 'chroma-js'

export class ColorUtils {
  /**
   * 计算两个颜色之间的色差 (Delta E)
   * 使用 CIEDE2000 算法
   * @param {string} color1 - 第一个颜色
   * @param {string} color2 - 第二个颜色
   * @returns {number} Delta E 值
   */
  static calculateDeltaE(color1, color2) {
    try {
      const lab1 = chroma(color1).lab()
      const lab2 = chroma(color2).lab()
      return chroma.deltaE(lab1, lab2)
    } catch (error) {
      console.warn('Delta E calculation failed:', error)
      return 0
    }
  }

  /**
   * 生成颜色阶梯 (类似 Tailwind CSS)
   * @param {string} baseColor - 基础颜色
   * @param {number} steps - 阶梯数量 (默认 9)
   * @returns {Array} 颜色阶梯数组
   */
  static generateColorScale(baseColor, steps = 9) {
    try {
      const base = chroma(baseColor)
      const hsl = base.hsl()
      
      // 生成从浅到深的色阶
      const scale = []
      for (let i = 0; i < steps; i++) {
        const lightness = 0.95 - (i / (steps - 1)) * 0.85
        const color = chroma.hsl(hsl[0], hsl[1], lightness)
        scale.push({
          level: (i + 1) * 100,
          hex: color.hex(),
          rgb: color.css('rgb'),
          hsl: color.css('hsl')
        })
      }
      
      return scale
    } catch (error) {
      console.warn('Color scale generation failed:', error)
      return []
    }
  }

  /**
   * 转换为 OKLCH 格式
   * @param {string} color - 输入颜色
   * @returns {string|null} OKLCH 字符串
   */
  static toOklch(color) {
    try {
      const chromaColor = chroma(color)
      const oklch = chromaColor.oklch()
      return `oklch(${oklch[0].toFixed(3)} ${oklch[1].toFixed(3)} ${oklch[2].toFixed(1)})`
    } catch (error) {
      console.warn('OKLCH conversion failed:', error)
      return null
    }
  }

  /**
   * 检查颜色对比度是否符合 WCAG 标准
   * @param {string} foreground - 前景色
   * @param {string} background - 背景色
   * @returns {Object} 对比度检查结果
   */
  static checkWCAGContrast(foreground, background) {
    try {
      const contrast = chroma.contrast(foreground, background)
      
      return {
        ratio: contrast,
        aa: contrast >= 4.5,
        aaa: contrast >= 7,
        aaLarge: contrast >= 3,
        level: contrast >= 7 ? 'AAA' : contrast >= 4.5 ? 'AA' : 'Fail'
      }
    } catch (error) {
      console.warn('WCAG contrast check failed:', error)
      return { ratio: 0, aa: false, aaa: false, aaLarge: false, level: 'Error' }
    }
  }

  /**
   * 生成和谐配色方案
   * @param {string} baseColor - 基础颜色
   * @param {string} scheme - 配色方案类型
   * @returns {Array} 配色数组
   */
  static generateColorScheme(baseColor, scheme = 'complementary') {
    try {
      const base = chroma(baseColor)
      const hsl = base.hsl()
      const hue = hsl[0]
      
      switch (scheme) {
        case 'complementary':
          return [
            base.hex().toUpperCase(),
            chroma.hsl((hue + 180) % 360, hsl[1], hsl[2]).hex().toUpperCase()
          ]

        case 'triadic':
          return [
            base.hex().toUpperCase(),
            chroma.hsl((hue + 120) % 360, hsl[1], hsl[2]).hex().toUpperCase(),
            chroma.hsl((hue + 240) % 360, hsl[1], hsl[2]).hex().toUpperCase()
          ]

        case 'analogous':
          return [
            chroma.hsl((hue - 30) % 360, hsl[1], hsl[2]).hex().toUpperCase(),
            base.hex().toUpperCase(),
            chroma.hsl((hue + 30) % 360, hsl[1], hsl[2]).hex().toUpperCase()
          ]

        case 'monochromatic':
          return [
            chroma.hsl(hue, hsl[1], Math.min(hsl[2] + 0.3, 1)).hex().toUpperCase(),
            base.hex().toUpperCase(),
            chroma.hsl(hue, hsl[1], Math.max(hsl[2] - 0.3, 0)).hex().toUpperCase()
          ]
        
        default:
          return [base.hex().toUpperCase()]
      }
    } catch (error) {
      console.warn('Color scheme generation failed:', error)
      return []
    }
  }

  /**
   * 导出 CSS 变量格式
   * @param {Object} colors - 颜色对象
   * @param {string} prefix - CSS 变量前缀
   * @returns {string} CSS 变量字符串
   */
  static exportCSSVariables(colors, prefix = 'color') {
    const cssVars = Object.entries(colors)
      .map(([name, color]) => `  --${prefix}-${name}: ${color};`)
      .join('\n')
    
    return `:root {\n${cssVars}\n}`
  }

  /**
   * 导出 Tailwind 配置格式
   * @param {Object} colors - 颜色对象
   * @returns {string} Tailwind 配置字符串
   */
  static exportTailwindConfig(colors) {
    const config = JSON.stringify(colors, null, 2)
    return `module.exports = {\n  theme: {\n    extend: {\n      colors: ${config}\n    }\n  }\n}`
  }

  /**
   * 获取颜色的亮度值
   * @param {string} color - 颜色值
   * @returns {number} 亮度值 (0-1)
   */
  static getLuminance(color) {
    try {
      return chroma(color).luminance()
    } catch (error) {
      console.warn('Luminance calculation failed:', error)
      return 0
    }
  }

  /**
   * 判断颜色是否为深色
   * @param {string} color - 颜色值
   * @returns {boolean} 是否为深色
   */
  static isDark(color) {
    return this.getLuminance(color) < 0.5
  }

  /**
   * 获取颜色的最佳文本颜色（黑色或白色）
   * @param {string} backgroundColor - 背景颜色
   * @returns {string} 最佳文本颜色
   */
  static getBestTextColor(backgroundColor) {
    return this.isDark(backgroundColor) ? '#ffffff' : '#000000'
  }

  /**
   * 混合两个颜色
   * @param {string} color1 - 第一个颜色
   * @param {string} color2 - 第二个颜色
   * @param {number} ratio - 混合比例 (0-1)
   * @returns {string} 混合后的颜色
   */
  static mixColors(color1, color2, ratio = 0.5) {
    try {
      return chroma.mix(color1, color2, ratio).hex()
    } catch (error) {
      console.warn('Color mixing failed:', error)
      return color1
    }
  }

  /**
   * 调整颜色亮度
   * @param {string} color - 原始颜色
   * @param {number} amount - 调整量 (-1 到 1)
   * @returns {string} 调整后的颜色
   */
  static adjustBrightness(color, amount) {
    try {
      return chroma(color).brighten(amount).hex()
    } catch (error) {
      console.warn('Brightness adjustment failed:', error)
      return color
    }
  }

  /**
   * 调整颜色饱和度
   * @param {string} color - 原始颜色
   * @param {number} amount - 调整量 (-1 到 1)
   * @returns {string} 调整后的颜色
   */
  static adjustSaturation(color, amount) {
    try {
      return chroma(color).saturate(amount).hex()
    } catch (error) {
      console.warn('Saturation adjustment failed:', error)
      return color
    }
  }

  /**
   * 验证颜色格式
   * @param {string} color - 颜色值
   * @returns {boolean} 是否为有效颜色
   */
  static isValidColor(color) {
    try {
      // 首先检查基本格式
      if (!color || typeof color !== 'string') {
        return false
      }

      // 检查 RGB 格式的特殊情况
      if (color.includes('rgb')) {
        const rgbMatch = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/)
        if (rgbMatch) {
          const [, r, g, b] = rgbMatch
          const red = parseInt(r)
          const green = parseInt(g)
          const blue = parseInt(b)

          // 检查 RGB 值是否在有效范围内
          if (red < 0 || red > 255 || green < 0 || green > 255 || blue < 0 || blue > 255) {
            return false
          }
        }
      }

      // 检查 RGBA 格式的特殊情况
      if (color.includes('rgba')) {
        const rgbaMatch = color.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/)
        if (rgbaMatch) {
          const [, r, g, b, a] = rgbaMatch
          const red = parseInt(r)
          const green = parseInt(g)
          const blue = parseInt(b)
          const alpha = parseFloat(a)

          // 检查 RGBA 值是否在有效范围内
          if (red < 0 || red > 255 || green < 0 || green > 255 || blue < 0 || blue > 255 || alpha < 0 || alpha > 1) {
            return false
          }
        }
      }

      // 使用 chroma.js 进行最终验证
      const chromaColor = chroma(color)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 获取颜色的温度（暖色或冷色）
   * @param {string} color - 颜色值
   * @returns {string} 'warm', 'cool', 或 'neutral'
   */
  static getColorTemperature(color) {
    try {
      const hsl = chroma(color).hsl()
      const hue = hsl[0]

      if (hue >= 0 && hue < 60) return 'warm'      // 红-橙-黄
      if (hue >= 60 && hue < 120) return 'neutral' // 黄-绿
      if (hue >= 120 && hue < 240) return 'cool'   // 绿-青-蓝
      if (hue >= 240 && hue < 300) return 'cool'   // 蓝-紫
      return 'warm'                                // 紫-红
    } catch (error) {
      console.warn('Color temperature calculation failed:', error)
      return 'neutral'
    }
  }

  // ===== 基础颜色转换函数 =====

  /**
   * HEX 转 RGB
   * @param {string} hex - HEX 颜色值
   * @returns {Object|null} RGB 对象 {r, g, b}
   */
  static hexToRgb(hex) {
    try {
      const rgb = chroma(hex).rgb()
      return { r: rgb[0], g: rgb[1], b: rgb[2] }
    } catch (error) {
      console.warn('HEX to RGB conversion failed:', error)
      return null
    }
  }

  /**
   * RGB 转 HEX
   * @param {number} r - 红色值 (0-255)
   * @param {number} g - 绿色值 (0-255)
   * @param {number} b - 蓝色值 (0-255)
   * @returns {string} HEX 颜色值
   */
  static rgbToHex(r, g, b) {
    try {
      return chroma(r, g, b).hex().toUpperCase()
    } catch (error) {
      console.warn('RGB to HEX conversion failed:', error)
      return '#000000'
    }
  }

  /**
   * RGB 转 HSL
   * @param {number} r - 红色值 (0-255)
   * @param {number} g - 绿色值 (0-255)
   * @param {number} b - 蓝色值 (0-255)
   * @returns {Object} HSL 对象 {h, s, l}
   */
  static rgbToHsl(r, g, b) {
    try {
      const hsl = chroma(r, g, b).hsl()
      return {
        h: Math.round(hsl[0] || 0),
        s: Math.round((hsl[1] || 0) * 100),
        l: Math.round((hsl[2] || 0) * 100)
      }
    } catch (error) {
      console.warn('RGB to HSL conversion failed:', error)
      return { h: 0, s: 0, l: 0 }
    }
  }

  /**
   * HSL 转 RGB
   * @param {number} h - 色相 (0-360)
   * @param {number} s - 饱和度 (0-100)
   * @param {number} l - 亮度 (0-100)
   * @returns {Object} RGB 对象 {r, g, b}
   */
  static hslToRgb(h, s, l) {
    try {
      const rgb = chroma.hsl(h, s / 100, l / 100).rgb()
      return { r: Math.round(rgb[0]), g: Math.round(rgb[1]), b: Math.round(rgb[2]) }
    } catch (error) {
      console.warn('HSL to RGB conversion failed:', error)
      return { r: 0, g: 0, b: 0 }
    }
  }

  /**
   * HSL 转 HEX
   * @param {number} h - 色相 (0-360)
   * @param {number} s - 饱和度 (0-100)
   * @param {number} l - 亮度 (0-100)
   * @returns {string} HEX 颜色值
   */
  static hslToHex(h, s, l) {
    try {
      return chroma.hsl(h, s / 100, l / 100).hex().toUpperCase()
    } catch (error) {
      console.warn('HSL to HEX conversion failed:', error)
      return '#000000'
    }
  }

  /**
   * HEX 转 RGB 字符串
   * @param {string} hex - HEX 颜色值
   * @returns {string} RGB 字符串
   */
  static hexToRgbString(hex) {
    try {
      return chroma(hex).css('rgb')
    } catch (error) {
      console.warn('HEX to RGB string conversion failed:', error)
      return 'rgb(0, 0, 0)'
    }
  }

  /**
   * 解析 RGB 字符串
   * @param {string} rgbString - RGB 字符串
   * @returns {Array} RGB 数组 [r, g, b]
   */
  static parseRgb(rgbString) {
    try {
      const rgb = chroma(rgbString).rgb()
      return [Math.round(rgb[0]), Math.round(rgb[1]), Math.round(rgb[2])]
    } catch (error) {
      console.warn('RGB string parsing failed:', error)
      return [0, 0, 0]
    }
  }

  /**
   * 解析 HSL 字符串
   * @param {string} hslString - HSL 字符串
   * @returns {Object|null} HSL 对象 {h, s, l}
   */
  static parseHsl(hslString) {
    try {
      const hsl = chroma(hslString).hsl()
      return {
        h: Math.round(hsl[0] || 0),
        s: Math.round((hsl[1] || 0) * 100),
        l: Math.round((hsl[2] || 0) * 100)
      }
    } catch (error) {
      console.warn('HSL string parsing failed:', error)
      return null
    }
  }

  /**
   * 解析任意颜色格式
   * @param {string} color - 颜色字符串
   * @returns {Object|null} RGB 对象 {r, g, b}
   */
  static parseColor(color) {
    try {
      const rgb = chroma(color).rgb()
      return { r: Math.round(rgb[0]), g: Math.round(rgb[1]), b: Math.round(rgb[2]) }
    } catch (error) {
      console.warn('Color parsing failed:', error)
      return null
    }
  }

  /**
   * 获取颜色对比度
   * @param {string} color1 - 第一个颜色
   * @param {string} color2 - 第二个颜色
   * @returns {number} 对比度值
   */
  static getContrastRatio(color1, color2) {
    try {
      return chroma.contrast(color1, color2)
    } catch (error) {
      console.warn('Contrast ratio calculation failed:', error)
      return 1
    }
  }
}
