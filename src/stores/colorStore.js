/**
 * Enhanced Color Store - Pinia State Management
 * 管理颜色状态、转换历史、用户偏好和Wiki访问记录
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

import { defineStore } from 'pinia'
import ColorParser from '@/scripts/ColorParser.js'
import chroma from 'chroma-js'

export const useColorStore = defineStore('color', {
  state: () => ({
    // 当前颜色状态
    currentColor: null,
    colorHistory: [],
    
    // 转换设置
    conversionAccuracy: 'high', // high, medium, fast
    deltaEThreshold: 2.0,
    
    // Wiki 相关
    favoriteFormats: ['hex', 'rgb', 'hsl'],
    recentlyViewed: [],
    
    // 转换器相关
    activeConverter: null,
    conversionHistory: [],
    
    // 用户偏好
    preferences: {
      theme: 'auto', // light, dark, auto
      defaultFormat: 'hex',
      showPrecision: true,
      enableAnimations: true
    }
  }),

  getters: {
    // 当前颜色值（字符串格式）
    currentColorValue: (state) => {
      return state.currentColor?.value || null
    },

    // 当前颜色的所有格式
    currentColorFormats: (state) => {
      if (!state.currentColor) return {}

      try {
        const color = chroma(state.currentColor.value)
        return {
          hex: color.hex().toUpperCase(),
          rgb: color.css('rgb'),
          hsl: color.css('hsl'),
          hsv: color.hsv(),
          cmyk: color.cmyk(),
          lab: color.lab(),
          xyz: color.xyz()
        }
      } catch (error) {
        console.warn('Color format conversion failed:', error)
        return {}
      }
    },

    // 格式化的颜色值（用于向后兼容）
    formats: (state) => {
      if (!state.currentColor) return {}

      try {
        const color = chroma(state.currentColor.value)
        return {
          hex: color.hex().toUpperCase(),
          rgb: color.css('rgb'),
          hsl: color.css('hsl')
        }
      } catch (error) {
        console.warn('Color format conversion failed:', error)
        return {}
      }
    },

    // 推荐的相关格式
    recommendedFormats: (state) => {
      const current = state.currentColor?.mode
      const recommendations = {
        'hex': ['rgb', 'hsl'],
        'rgb': ['hex', 'hsl', 'hsv'],
        'hsl': ['rgb', 'hsv', 'oklch'],
        'oklch': ['hsl', 'lch', 'p3'],
        'cmyk': ['rgb', 'hex']
      }
      return recommendations[current] || []
    },

    // 颜色历史统计
    historyStats: (state) => {
      const formats = state.colorHistory.map(c => c.mode)
      const formatCounts = formats.reduce((acc, format) => {
        acc[format] = (acc[format] || 0) + 1
        return acc
      }, {})
      
      return {
        totalColors: state.colorHistory.length,
        mostUsedFormat: Object.keys(formatCounts).reduce((a, b) => 
          formatCounts[a] > formatCounts[b] ? a : b, 'hex'
        ),
        formatDistribution: formatCounts
      }
    }
  },

  actions: {
    // 解析颜色
    parseColor(input) {
      const parsed = ColorParser.parseEnhanced(input, {
        enableCache: true,
        enableSuggestions: true,
        enableFuzzyMatch: true,
        strictMode: false
      })
      
      if (parsed.mode !== 'unknown') {
        try {
          this.currentColor = {
            ...parsed,
            chroma: chroma(parsed.value),
            timestamp: Date.now()
          }
          this.addToHistory(this.currentColor)
        } catch (error) {
          // chroma 解析失败，清空当前颜色
          this.currentColor = null
          console.warn('Color parsing failed:', error)
        }
      } else {
        // 解析失败，清空当前颜色
        this.currentColor = null
      }
      
      return parsed
    },

    // 转换到指定格式
    convertToFormat(targetFormat) {
      if (!this.currentColor) return null
      
      try {
        const chromaColor = this.currentColor.chroma
        
        switch (targetFormat) {
          case 'hex':
            return chromaColor.hex()
          case 'rgb':
            return chromaColor.css('rgb')
          case 'hsl':
            return chromaColor.css('hsl')
          case 'hsv':
            const hsv = chromaColor.hsv()
            return `hsv(${Math.round(hsv[0] || 0)}, ${Math.round((hsv[1] || 0) * 100)}%, ${Math.round((hsv[2] || 0) * 100)}%)`
          case 'cmyk':
            const cmyk = chromaColor.cmyk()
            return `cmyk(${cmyk.map(v => Math.round(v * 100) + '%').join(', ')})`
          case 'lab':
            const lab = chromaColor.lab()
            return `lab(${lab[0].toFixed(1)} ${lab[1].toFixed(1)} ${lab[2].toFixed(1)})`
          case 'xyz':
            const xyz = chromaColor.xyz()
            return `xyz(${xyz.map(v => v.toFixed(3)).join(' ')})`
          default:
            return null
        }
      } catch (error) {
        console.error('Color conversion failed:', error)
        return null
      }
    },

    // 计算色差
    calculateDeltaE(color1, color2) {
      try {
        const lab1 = chroma(color1).lab()
        const lab2 = chroma(color2).lab()

        // 计算 Delta E (CIE76)
        const deltaL = lab1[0] - lab2[0]
        const deltaA = lab1[1] - lab2[1]
        const deltaB = lab1[2] - lab2[2]

        const deltaE = Math.sqrt(deltaL * deltaL + deltaA * deltaA + deltaB * deltaB)
        return Math.round(deltaE * 10) / 10 // 保留一位小数
      } catch (error) {
        console.warn('Delta E calculation failed:', error)
        return 0
      }
    },

    // 添加到历史记录
    addToHistory(color) {
      // 处理字符串输入，转换为对象
      let colorObj
      if (typeof color === 'string') {
        colorObj = {
          value: color,
          mode: 'hex', // 假设字符串是 HEX 格式
          timestamp: Date.now()
        }
      } else {
        colorObj = color
      }

      // 避免重复添加相同颜色
      const exists = this.colorHistory.find(c => c.value === colorObj.value)
      if (exists) return

      this.colorHistory.unshift(colorObj)

      // 限制历史记录数量
      if (this.colorHistory.length > 100) {
        this.colorHistory = this.colorHistory.slice(0, 100)
      }
    },

    // 添加到收藏格式
    addFavoriteFormat(format) {
      if (!this.favoriteFormats.includes(format)) {
        this.favoriteFormats.push(format)
      }
    },

    // 记录最近查看的 Wiki
    addRecentlyViewed(format) {
      const index = this.recentlyViewed.indexOf(format)
      if (index > -1) {
        this.recentlyViewed.splice(index, 1)
      }
      this.recentlyViewed.unshift(format)
      
      // 限制最近查看数量
      if (this.recentlyViewed.length > 10) {
        this.recentlyViewed = this.recentlyViewed.slice(0, 10)
      }
    },

    // 记录转换历史
    addConversionHistory(conversion) {
      this.conversionHistory.unshift({
        ...conversion,
        id: Date.now(),
        timestamp: new Date()
      })
      
      // 限制转换历史数量
      if (this.conversionHistory.length > 50) {
        this.conversionHistory = this.conversionHistory.slice(0, 50)
      }
    },

    // 更新用户偏好
    updatePreferences(newPreferences) {
      this.preferences = { ...this.preferences, ...newPreferences }
    },

    // 清空历史记录
    clearHistory() {
      this.colorHistory = []
      this.conversionHistory = []
      this.recentlyViewed = []
    },

    // 导出数据
    exportData() {
      return {
        colorHistory: this.colorHistory,
        conversionHistory: this.conversionHistory,
        favoriteFormats: this.favoriteFormats,
        preferences: this.preferences,
        exportDate: new Date().toISOString()
      }
    },

    // 导入数据
    importData(data) {
      if (data.colorHistory) this.colorHistory = data.colorHistory
      if (data.conversionHistory) this.conversionHistory = data.conversionHistory
      if (data.favoriteFormats) this.favoriteFormats = data.favoriteFormats
      if (data.preferences) this.preferences = { ...this.preferences, ...data.preferences }
    }
  },

}, {
  // 持久化配置
  persist: {
    key: 'colorcode-store',
    storage: localStorage,
    paths: ['favoriteFormats', 'recentlyViewed', 'preferences', 'colorHistory']
  }
})
