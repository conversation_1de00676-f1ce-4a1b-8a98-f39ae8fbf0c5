/**
 * ColorCode.cc Main Entry Point
 * 基于 Vue 3.x + Vite 7.x + JavaScript ES6+ 技术栈
 * 
 * 应用特性：
 * - 专业级颜色工具平台
 * - 现代化 Vue 3 应用
 * - PWA 支持
 * - 性能优化
 * - 用户体验优化
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import App from './App.vue'
import router from './router'

// 导入设计系统样式
import './styles/design-system.css'

// 导入图标组件
import * as Icons from './components/icons.js'

// ============================================================================
// 应用创建和配置
// ============================================================================

/**
 * 创建 Pinia 实例
 */
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

/**
 * 创建 Vue 应用实例
 */
const app = createApp(App)

// ============================================================================
// 全局组件注册
// ============================================================================

// ============================================================================
// 插件注册
// ============================================================================

/**
 * 注册 Pinia 状态管理
 */
app.use(pinia)

/**
 * 注册 Vue Router
 */
app.use(router)

/**
 * 注册所有图标组件为全局组件
 * 这样可以在模板中直接使用 <component :is="iconName" />
 */
Object.entries(Icons.default || Icons).forEach(([name, component]) => {
  app.component(name, component)
})

// ============================================================================
// 全局属性和方法
// ============================================================================

/**
 * 全局属性配置
 */
app.config.globalProperties.$version = __APP_VERSION__
app.config.globalProperties.$buildTime = __BUILD_TIME__

/**
 * 全局错误处理器
 * 捕获组件渲染错误和异步错误
 */
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue Error:', err)
  console.error('Component:', instance)
  console.error('Error Info:', info)
  
  // 发送错误报告到监控服务
  if (typeof gtag !== 'undefined') {
    gtag('event', 'exception', {
      description: err.message,
      fatal: false,
      custom_parameter_1: info
    })
  }
}

/**
 * 全局警告处理器（开发环境）
 */
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('Vue Warning:', msg)
    console.warn('Trace:', trace)
  }
}

// ============================================================================
// 性能监控
// ============================================================================

/**
 * 应用性能监控
 */
app.config.performance = import.meta.env.DEV

/**
 * 监控应用挂载时间
 */
const mountStartTime = performance.now()

// ============================================================================
// 应用挂载
// ============================================================================

/**
 * 挂载应用到 DOM
 */
app.mount('#app')

/**
 * 记录应用挂载完成时间
 */
const mountEndTime = performance.now()
const mountTime = mountEndTime - mountStartTime

console.log(`🎨 ColorCode.cc 应用启动完成`)
console.log(`⚡ 挂载时间: ${mountTime.toFixed(2)}ms`)
console.log(`🔧 Vue 版本: ${app.version}`)
console.log(`📦 应用版本: ${__APP_VERSION__}`)
console.log(`🕐 构建时间: ${__BUILD_TIME__}`)

// 发送性能数据
if (typeof gtag !== 'undefined') {
  gtag('event', 'app_mount_time', {
    value: Math.round(mountTime),
    custom_parameter: 'vue_app'
  })
}

// ============================================================================
// 开发环境调试工具
// ============================================================================

if (import.meta.env.DEV) {
  // 在开发环境中暴露应用实例到全局，方便调试
  window.__VUE_APP__ = app
  
  // 添加开发工具提示
  console.log('🛠️ 开发模式已启用')
  console.log('💡 可以通过 window.__VUE_APP__ 访问应用实例')
  
  // 热更新提示
  if (import.meta.hot) {
    console.log('🔥 热更新已启用')
    
    // 接受热更新
    import.meta.hot.accept('./App.vue', (newModule) => {
      console.log('🔄 应用组件已热更新')
    })
  }
}

// ============================================================================
// 生产环境优化
// ============================================================================

if (import.meta.env.PROD) {
  // 生产环境下的优化措施
  
  // 禁用开发工具
  app.config.devtools = false
  
  // 移除开发环境的日志
  console.log = () => {}
  console.warn = () => {}
  console.info = () => {}
  
  // 只保留错误日志
  const originalError = console.error
  console.error = (...args) => {
    originalError.apply(console, args)
    
    // 发送错误到监控服务
    if (typeof gtag !== 'undefined') {
      gtag('event', 'console_error', {
        description: args.join(' '),
        fatal: false
      })
    }
  }
}

// ============================================================================
// 浏览器兼容性检查
// ============================================================================

/**
 * 检查浏览器兼容性
 */
function checkBrowserCompatibility() {
  const requiredFeatures = [
    'Promise',
    'fetch',
    'Map',
    'Set',
    'Symbol',
    'Proxy'
  ]
  
  const unsupportedFeatures = requiredFeatures.filter(feature => 
    typeof window[feature] === 'undefined'
  )
  
  if (unsupportedFeatures.length > 0) {
    console.warn('⚠️ 浏览器兼容性警告:', unsupportedFeatures)
    
    // 显示兼容性提示
    const compatibilityWarning = document.createElement('div')
    compatibilityWarning.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #fbbf24;
        color: #92400e;
        padding: 1rem;
        text-align: center;
        z-index: 10000;
        font-family: sans-serif;
      ">
        <strong>浏览器兼容性提示：</strong>
        您的浏览器可能不支持某些现代功能，建议升级到最新版本以获得最佳体验。
        <button onclick="this.parentElement.remove()" style="
          margin-left: 1rem;
          padding: 0.25rem 0.5rem;
          background: #92400e;
          color: white;
          border: none;
          border-radius: 0.25rem;
          cursor: pointer;
        ">关闭</button>
      </div>
    `
    document.body.appendChild(compatibilityWarning)
  }
}

// 执行兼容性检查
checkBrowserCompatibility()

// ============================================================================
// 应用生命周期事件
// ============================================================================

/**
 * 页面可见性变化处理
 */
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    console.log('📱 应用进入后台')
  } else {
    console.log('📱 应用回到前台')
  }
})

/**
 * 页面卸载前的清理工作
 */
window.addEventListener('beforeunload', () => {
  console.log('👋 应用即将卸载')
  
  // 发送页面停留时间统计
  const stayTime = performance.now() - mountStartTime
  if (typeof gtag !== 'undefined') {
    gtag('event', 'page_stay_time', {
      value: Math.round(stayTime / 1000), // 转换为秒
      custom_parameter: 'landing_page'
    })
  }
})

/**
 * 网络状态变化处理
 */
window.addEventListener('online', () => {
  console.log('🌐 网络连接已恢复')
})

window.addEventListener('offline', () => {
  console.log('📡 网络连接已断开，PWA 离线模式启用')
})
